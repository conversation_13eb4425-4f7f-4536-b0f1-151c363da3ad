import mssql, { getRows } from '@lcs/mssql-utility'
import AgentModel from '../../../models/agents.model.js'
import { AgentTableName, Agent } from '@tess-f/sql-tables/dist/lrs/agent.js'

export default async function getAgentByID(id: string): Promise<AgentModel> {
  const pool = mssql.getPool()
  const records = await getRows<Agent>(AgentTableName, pool.request(), { ID: id })
  return new AgentModel(undefined, records[0])
}
