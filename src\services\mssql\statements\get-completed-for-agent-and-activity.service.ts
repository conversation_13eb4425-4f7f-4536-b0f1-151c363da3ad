import mssql, { getRows } from '@lcs/mssql-utility'
import StatementModel from '../../../models/statement.model.js'
import { Statement, StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { CMI5Verbs } from '@tess-f/sql-tables/dist/lrs/verb.js'

export default async function getCompletedStatementsForAgentAndActivity(agentID: string, activityID: string): Promise<StatementModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<Statement>(StatementTableName, pool.request(), {
    ActorID: agentID,
    ObjectActivityID: activityID,
    VerbID: CMI5Verbs.Terminated
  })
  return records.map(record => new StatementModel(undefined, record))
}
