import { Request } from 'mssql'
import { Agent<PERSON>son } from '../../../../models/agents.model.js'
import { StatementTableName as SUB_STATEMENT_TABLE_NAME } from '@tess-f/sql-tables/dist/lrs/sub-statement.js'
import { AgentRelationshipTableName } from '@tess-f/sql-tables/dist/lrs/agent-relationships.js'
import { AgentTableName } from '@tess-f/sql-tables/dist/lrs/agent.js'
import { StatementFields } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { ContextAgentFields, ContextAgentTableName } from '@tess-f/sql-tables/dist/lrs/context-agent.js'
import { ContextGroupsFields, ContextGroupsTableName } from '@tess-f/sql-tables/dist/lrs/context-groups.js'

// Groups that have members which match teh specified Agent based on their IFI are considered a match
export default function buildAgentStatementFilter(request: Request, agent: Agent<PERSON><PERSON>, relatedAgents = false): string {
  let query = ''
  let ifiWhereClause = ''

  if (agent.mbox) {
    ifiWhereClause = 'WHERE [Mbox] = @agent_mbox'
    request.input('agent_mbox', agent.mbox)
  } else if (agent.mbox_sha1sum) {
    ifiWhereClause = 'WHERE [MboxSHA1Sum] = @agent_mbox_sha1sum'
    request.input('agent_mbox_sha1sum', agent.mbox_sha1sum)
  } else if (agent.openid) {
    ifiWhereClause = 'WHERE [OpenID] = @agent_openid'
    request.input('agent_openid', agent.openid)
  } else if (agent.account?.homePage && agent.account?.name) {
    ifiWhereClause = 'WHERE [AccountHomePage] = @agent_account_home_page AND [AccountName] = @agent_account_name'
    request.input('agent_account_name', agent.account.name)
    request.input('agent_account_home_page', agent.account.homePage)
  }

  if (ifiWhereClause !== '') {
    // only return statements for which teh specified Agent or Group is the Actor or Object of the Statement
    const agentClause = `
      SELECT [ID]
      FROM [${AgentTableName}]
      ${ifiWhereClause}
    `

    const agentGroupClause = `
      SELECT [AgentID]
      FROM [${AgentRelationshipTableName}]
      WHERE [MemberID] IN (
        ${agentClause}
      )
    `

    query += `
      [ObjectAgentID] IN (
        ${agentClause}
      ) OR [ObjectAgentID] IN (
        ${agentGroupClause}
      ) OR [ActorID] IN (
        ${agentClause}
      ) OR [ActorID] IN (
        ${agentGroupClause}
      )
    `

    if (relatedAgents) {
      // Apply the Agent filter broadly.
      // Include Statements for which the
      //    Actor,
      //    Object,
      //    Authority,
      //    Instructor,
      //    Team,
      //    or any of these properties in a contained SubStatement match the Agent parameter,
      //    instead of that parameter's normal behavior.
      // Matching is defined in the same way it is for the "agent" parameter.

      // Team
      const contextTeamClause = `
        OR [ContextTeamID] IN (
          ${agentClause}
        ) OR [ContextTeamID] IN (
          ${agentGroupClause}
        )
      `

      query += contextTeamClause

      // Instructor
      const contextInstructorClause = `
        OR [ContextInstructorID] IN (
          ${agentClause}
        ) OR [ContextInstructorID] IN (
          ${agentGroupClause}
        )
      `
      query += contextInstructorClause
      query += `
        OR [${StatementFields.AuthorityID}] IN (
          ${agentClause}
        ) OR [${StatementFields.AuthorityID}] IN (
          ${agentGroupClause}
        )
      `

      // sub statement actor
      // sub statement activity
      // sub statement instructor
      // sub statement team
      query += `
        OR [ObjectSubStatementID] IN (
          SELECT [ID]
          FROM [${SUB_STATEMENT_TABLE_NAME}]
          WHERE [ObjectAgentID] IN (
            ${agentClause}
          ) OR [ObjectAgentID] IN (
            ${agentGroupClause}
          ) OR [ActorID] IN (
            ${agentClause}
          ) OR [ActorID] IN (
            ${agentGroupClause}
          )
          ${contextTeamClause}
          ${contextInstructorClause}
        )
      `

      // context agent
      query += `
        OR [${StatementFields.ID}] IN (
          SELECT [${ContextAgentFields.StatementID}]
          FROM [${ContextAgentTableName}]
          WHERE [${ContextAgentFields.AgentID}] IN (
            ${agentClause}
          )
        )
      `

      query += `
        OR [${StatementFields.ID}] IN (
          SELECT [${ContextGroupsFields.StatementID}]
          FROM [${ContextGroupsTableName}]
          WHERE [${ContextGroupsFields.GroupID}] IN (
            ${agentClause}
          )
        )`
    }
  }

  return query
}
