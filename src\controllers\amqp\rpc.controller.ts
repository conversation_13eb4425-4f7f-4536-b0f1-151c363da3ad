import rabbitMQ from '@lcs/rabbitmq'
import logger from '@lcs/logger'

import createStatement from './create-statement.controller.js'
import getStatementsController from './get.controller.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('AMQP.RPC-Controller')

export default class RpcController {
  private consumer!: Function
  active = false

  async connect (queueName: string) {
    this.consumer = await rabbitMQ.createRpcConsumer(queueName,
      (message, resolve, reject) => this.onMessage(message, resolve, reject),
      (error) => this.onReceiverError(error)
    )
  }

  async onMessage (message: any, resolve: Function, reject: Function) {
    try {
      // Default command if it doesn't exist
      message.command = message.command ? message.command : ''

      resolve(await this.routeMessage(message))
    } catch (error) {
      // send back the message
      reject()
      log('error', 'An error occurred while processing a message', { errorMessage: getErrorMessage(error), success: false })
    }
  }

  async onReceiverError (error: unknown) {
    log('error', 'Receiver Error', { errorMessage: getErrorMessage(error), success: false })
  }

  async close () {
    if (this.consumer) {
      await this.consumer()
    }
    this.active = false
  }

  async routeMessage (message: any) {
    switch (message.command) {
      case 'create':
        return await createStatement(message)

      case 'get':
        return await getStatementsController(message)

      default:
        log('warn', 'received unknown command', { command: message.command, success: false })
        return {
          success: false,
          message: 'Unknown command'
        }
    }
  }
}
