import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivityProfile from '../../../models/activity-profile.model.js'
import createActivityProfileService from './create.service.js'
import { getActivityProfileService } from './get.service.js'

export default async function getOrCreateActivityProfileService (activityProfile: ActivityProfile): Promise<{ activityProfile: ActivityProfile, created: boolean }> {
  if (!activityProfile.fields.ID || !activityProfile.fields.ActivityID) {
    throw new Error ('Missing required fields')
  }

  try {
    const Profile = await getActivityProfileService(activityProfile.fields.ID, activityProfile.fields.ActivityID)
    return {
      activityProfile: Profile,
      created: false
    }
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      const created = await createActivityProfileService(activityProfile)
      return {
        activityProfile: created,
        created: true
      }
    } else {
      throw error
    }
  }

}
