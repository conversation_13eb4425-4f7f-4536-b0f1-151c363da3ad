import logger from '@lcs/logger'
import Sinon from 'sinon'
import httpMocks from 'node-mocks-http'
import sessionAuthority from '@lcs/session-authority'
import middleware from './check-session.middleware.js'
import { expect } from 'chai'
import httpStatus from 'http-status'
const { UNAUTHORIZED } = httpStatus
import * as userAuth from '@tess-f/id-mgmt-npm/dist/amqp/authenticate-user.js'

describe('HTTP Middleware - Check Session', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  it('validates a session', async () => {
    const mocks = httpMocks.createMocks()
    const sessionStub = Sinon.stub(sessionAuthority, 'authenticate')
    sessionStub.returns(Promise.resolve({ result: 'valid', success: true }))
    const next = Sinon.spy()
    await middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.true
  })

  it('returns unauthorized when the session is invalid', async () => {
    const mocks = httpMocks.createMocks()
    const sessionStub = Sinon.stub(sessionAuthority, 'authenticate')
    sessionStub.returns(Promise.resolve({ result: 'invalid', success: true }))
    const next = Sinon.spy()
    await middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.false
    expect(mocks.res.statusCode).to.equal(UNAUTHORIZED)
  })

  it('returns unauthorized when the basic auth token is not formatted correctly', async () => {
    // user-password
    const mocks1 = httpMocks.createMocks({ headers: { authorization: 'Basic dXNlci1wYXNzd29yZA==' } })
    // :
    const mocks2 = httpMocks.createMocks({ headers: { authorization: 'Basic Og==' } })
    // user:
    const mocks3 = httpMocks.createMocks({ headers: { authorization: 'Basic dXNlcjo=' } })
    // :password
    const mocks4 = httpMocks.createMocks({ headers: { authorization: 'Basic OnBhc3N3b3Jk' } })

    const sessionStub = Sinon.stub(sessionAuthority, 'authenticate')
    sessionStub.returns(Promise.reject(new Error('invalid bearer token in request header')))

    const next = Sinon.spy()
    await middleware(mocks1.req, mocks1.res, next)
    expect(next.called).to.be.false
    expect(mocks1.res.statusCode).to.equal(UNAUTHORIZED)
    await middleware(mocks2.req, mocks2.res, next)
    expect(next.called).to.be.false
    expect(mocks2.res.statusCode).to.equal(UNAUTHORIZED)
    await middleware(mocks3.req, mocks3.res, next)
    expect(next.called).to.be.false
    expect(mocks3.res.statusCode).to.equal(UNAUTHORIZED)
    await middleware(mocks4.req, mocks4.res, next)
    expect(next.called).to.be.false
    expect(mocks4.res.statusCode).to.equal(UNAUTHORIZED)
  })

  xit('returns unauthorized when the user is not valid', async () => {
    const mocks = httpMocks.createMocks({ headers: { authorization: 'Basic dXNlcjpwYXNzd29yZA==' } })

    const sessionStub = Sinon.stub(sessionAuthority, 'authenticate')
    sessionStub.returns(Promise.reject(new Error('invalid bearer token in request header')))

    const authStub = Sinon.stub(userAuth, 'authenticateUser')
    authStub.returns(Promise.resolve({ success: false }))

    const next = Sinon.spy()

    await middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.false
    expect(mocks.res.statusCode).to.equal(UNAUTHORIZED)
  })

  xit('returns unauthorized when basic auth token validation fails', async () => {
    const mocks = httpMocks.createMocks({ headers: { authorization: 'Basic dXNlcjpwYXNzd29yZA==' } })

    const sessionStub = Sinon.stub(sessionAuthority, 'authenticate')
    sessionStub.returns(Promise.reject(new Error('invalid bearer token in request header')))

    const authStub = Sinon.stub(userAuth, 'authenticateUser')
    authStub.returns(Promise.reject(new Error('service error')))

    const next = Sinon.spy()

    await middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.false
    expect(mocks.res.statusCode).to.equal(UNAUTHORIZED)
  })

  xit('validates basic auth token', async () => {
    const mocks = httpMocks.createMocks({ headers: { authorization: 'Basic dXNlcjpwYXNzd29yZA==' } })

    const sessionStub = Sinon.stub(sessionAuthority, 'authenticate')
    sessionStub.returns(Promise.reject(new Error('invalid bearer token in request header')))

    const authStub = Sinon.stub(userAuth, 'authenticateUser')
    authStub.returns(Promise.resolve({ success: true, data: { id: 'test-user' } }))

    const next = Sinon.spy()

    await middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.req.session.userId).to.equal('test-user')
  })
})