import createStatement from '../../services/mssql/statements/create.service.js'
import logger from '@lcs/logger'
import Statement, { StatementRef } from '../../models/statement.model.js'
import validate, { validateVoidStatement } from '../../services/validators/statement/validate.service.js'
import getStatementById from '../../services/mssql/statements/get-by-id.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { getErrorMessage, getErrorStackTrace } from '@tess-f/backend-utils'

const log = logger.create('AMQP-Controller.Create-Statement')

export default async function (req: Partial<{ statement: any }>) {
  if (!req.statement) {
    log('warn', 'Failed to create statement, request missing statement object', { success: false })
    throw new Error('Missing statement')
  }

  try {
    validate(req.statement)
  } catch (error) {
    let errorMessage = 'validation error'
    if (error instanceof Error) {
      errorMessage = error.message
    }
    log('warn', 'Failed to save statement: validation error', { errorMessage, success: false })
    return {
      success: false,
      message: errorMessage
    }
  }

  const statement = new Statement(req.statement)

  try {
    // check for conflicting statement
    if (statement.fields.id) {
      try {
        await getStatementById(statement.fields.id)
        return {
          success: false,
          message: `Statement with the id: ${statement.fields.id} already exists`
        }
      } catch (error) {
        if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
          return {
            success: false,
            message: 'Failed to verify statement'
          }
        }
      }
    }

    if (statement.fields.verb?.id === 'http://adlnet.gov/expapi/verbs/voided') {
      // validate void statement
      await validateVoidStatement((statement.fields.object as StatementRef).id)
    }

    await createStatement(statement)

    log('info', 'Successfully created statement', { statementId: req.statement.id, success: true })
    return {
      success: true,
      id: req.statement.id
    }
  } catch (error) {
    log('error', 'Failed to create statement', { errorMessage: getErrorMessage(error), errorStack: getErrorStackTrace(error), success: false })
    return {
      success: false,
      message: `Failed to store statement\n${error}`
    }
  }
}
