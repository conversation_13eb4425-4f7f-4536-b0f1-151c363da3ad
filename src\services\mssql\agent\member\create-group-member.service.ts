import mssql, { addRow } from '@lcs/mssql-utility'
import AgentRelationshipModel from '../../../../models/agent-relationships.model.js'
import { AgentRelationship } from '@tess-f/sql-tables/dist/lrs/agent-relationships.js'

export default async function createGroupMember(agentRelationship: AgentRelationshipModel): Promise<AgentRelationshipModel> {
  const pool = mssql.getPool()
  const created = await addRow<AgentRelationship>(pool.request(), agentRelationship)
  return new AgentRelationshipModel(created)
}
