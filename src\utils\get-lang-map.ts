import settings from '../config/settings.js'

export default function getLangMap (langMap: { Lang: string , Display: string }[], languages?: string[]): {[key: string]: string} {
  if (langMap.length <= 0) {
    // we have no languages to search just return an empty object
    return {}
  }

  if (languages?.includes('all')) {
    // the user wan't all of the languages, let's send them all back
    return langMap.reduce((a, l) => ({ ...a, [l.Lang]: l.Display }), {})
  }

  const systemLang = findLanguageMatch(langMap, settings.languageCode)
  if (languages) {
    for (const language of languages) {
      if (language === 'anylanguage' && systemLang) {
        return systemLang
      } else if (language === 'anylanguage' && !systemLang) {
        // return the first item
        return { [langMap[0].Lang]: langMap[0].Display }
      } else {
        const requestedLang = findLanguageMatch(langMap, language)
        if (requestedLang) {
          // if we found a match for the requested language let's send it back
          // otherwise, let's look for the next language in the list
          return requestedLang
        }
      }
    }
  }
  // if we've gotten this far we don't have any canonical matches
  if (systemLang) {
    // but we do have a match for the systems default language
    // let's use that
    return systemLang
  } else {
    // we don't have any matches yes and there is no
    // match for the system default, but we do have options
    // let's just return the first value if we have one
    return { [langMap[0].Lang]: langMap[0].Display }
  }
}

function findLanguageMatch (langMap: { Lang: string, Display: string }[], language: string): {[key: string]: string} | undefined {
  // let's see if the there is an exact language map for the requested language
  const requestedLang = langMap.find(l => l.Lang === language)
  if (requestedLang) {
    return { [requestedLang.Lang]: requestedLang.Display }
  } else {
    // we don't have the exact language
    // lets see if this is a generic language with no region
    if (language.indexOf('-') === -1) {
      const matches = langMap.filter(l => l.Lang.includes(language))
      if (matches.length > 0) {
        // we have found one or more dialects for this language
        // let's just send the first one back
        return { [matches[0].Lang]: matches[0].Display }
      }
    }
  }
}
