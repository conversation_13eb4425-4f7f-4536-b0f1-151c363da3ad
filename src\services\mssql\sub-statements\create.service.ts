import mssql, { addRow } from '@lcs/mssql-utility'
import Activity, { ActivityJSON } from '../../../models/activity.model.js'
import Agent from '../../../models/agents.model.js'
import SubStatementModel from '../../../models/sub-statement.model.js'
import { SubStatement } from '@tess-f/sql-tables/dist/lrs/sub-statement.js'
import Verb from '../../../models/verb.model.js'
import getOrCreateActivityService from '../activities/get-or-create.service.js'
import getOrCreateAgent from '../agent/get-or-create.service.js'
import createRecord from '../generic/create-record.service.js'
import getOrCreateVerbService from '../verb/get-or-create.service.js'
import { SubStatementCategoryContextActivity } from '@tess-f/sql-tables/dist/lrs/sub-statement-category-context-activity.js'
import { SubStatementGroupingContextActivity } from '@tess-f/sql-tables/dist/lrs/sub-statement-grouping-context-activity.js'
import { SubStatementOtherContextActivity } from '@tess-f/sql-tables/dist/lrs/sub-statement-other-context-activity.js'
import { SubStatementParentContextActivity } from '@tess-f/sql-tables/dist/lrs/sub-statement-parent-context-activity.js'

export default async function createSubStatement(subStatement: SubStatementModel): Promise<SubStatementModel> {
  const pool = mssql.getPool()

  // 1. save the verb
  subStatement.fields.verb = (await getOrCreateVerbService(new Verb(subStatement.fields.verb))).fields

  // 2. save the actor
  subStatement.attachActor(await getOrCreateAgent(new Agent(subStatement.fields.actor)))

  // 3. save the object
  //    NOTE: sub statements cannot contain another sub statement
  if (subStatement.fields.object!.objectType === 'Agent' || subStatement.fields.object!.objectType === 'Group') {
    // Object is an Agent
    subStatement.attachObjectAgent(await getOrCreateAgent(new Agent(subStatement.fields.object)))
  } else if (subStatement.fields.object!.objectType === 'StatementRef') {
    // Object is a StatementRef (we don't need to do anything the sub statement class will handle it)
  } else {
    // Object is an Activity
    subStatement.fields.object!.objectType = 'Activity' // set to activity just in case it came without an object type
    subStatement.attachObjectActivity(await getOrCreateActivityService(new Activity(subStatement.fields.object as ActivityJSON)))
  }

  // 4. save context instructor / team
  if (subStatement.fields.context) {
    // instructor
    if (subStatement.fields.context.instructor) {
      subStatement.attachContextInstructor(await getOrCreateAgent(new Agent(subStatement.fields.context.instructor)))
    }
    // team
    if (subStatement.fields.context.team) {
      subStatement.attachContextTeam(await getOrCreateAgent(new Agent(subStatement.fields.context.team)))
    }
  }

  // 5. save the sub statement
  const created = await addRow<SubStatement>(pool.request(), subStatement)
  subStatement.ID = created.ID!

  // 6. if context has activities save those now
  //    a. save the activities first then save the sub statement attachments
  if (subStatement.getContextParentJoins().length > 0) {
    subStatement.attachContextParentActivities(await Promise.all(subStatement.fields.context!.contextActivities!.parent!.map(async activity => {
      return getOrCreateActivityService(new Activity(activity))
    })))
    await Promise.all(subStatement.getContextParentJoins().map(async join => createRecord<SubStatementParentContextActivity>(join)))
  }

  if (subStatement.getContextCategoryJoins().length > 0) {
    subStatement.attachContextCategoryActivities(await Promise.all(subStatement.fields.context!.contextActivities!.category!.map(async activity => {
      return getOrCreateActivityService(new Activity(activity))
    })))
    await Promise.all(subStatement.getContextCategoryJoins().map(async join => createRecord<SubStatementCategoryContextActivity>(join)))
  }

  if (subStatement.getContextGroupingJoins().length > 0) {
    subStatement.attachContextGroupingActivities(await Promise.all(subStatement.fields.context!.contextActivities!.grouping!.map(async activity => {
      return getOrCreateActivityService(new Activity(activity))
    })))
    await Promise.all(subStatement.getContextGroupingJoins().map(async join => createRecord<SubStatementGroupingContextActivity>(join)))
  }

  if (subStatement.getContextOtherJoins().length > 0) {
    subStatement.attachContextOtherActivities(await Promise.all(subStatement.fields.context!.contextActivities!.other!.map(async activity => {
      return getOrCreateActivityService(new Activity(activity))
    })))
    await Promise.all(subStatement.getContextOtherJoins().map(async join => createRecord<SubStatementOtherContextActivity>(join)))
  }

  return subStatement
}
