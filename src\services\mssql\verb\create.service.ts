import mssql, { addRow } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import VerbModel from '../../../models/verb.model.js'
import { Verb } from '@tess-f/sql-tables/dist/lrs/verb.js'
import VerbDisplayModel from '../../../models/verb-display.model.js'
import { VerbDisplay } from '@tess-f/sql-tables/dist/lrs/verb-display.js'
import { RedisClient as VerbClient } from '../../redis/verb-client.service.js'
import { RedisClient as VerbDisplayClient } from '../../redis/verb-display-client.service.js'

const log = logger.create('Service-MSSQL.create-verb')

export default async function createVerbService(verb: VerbModel): Promise<VerbModel> {
  const pool = mssql.getPool()

  const record = await addRow<Verb>(pool.request(), verb)
  const created = new VerbModel(undefined, record)

  log('info', 'Successfully saved verb in the database', { id: created.fields.id, success: true })

  if (verb.getDisplay().length > 0) {
    // attach the display
    created.attachDisplay(await Promise.all(verb.getDisplay().map(async display => {
      const displayRecord = await addRow<VerbDisplay>(pool.request(), display)
      return new VerbDisplayModel(displayRecord)
    })))
    log('info', 'Successfully saved verbs display properties in the database', { count: created.getDisplay().length, success: true })
    await VerbDisplayClient.setVerbDisplay(created.fields.id, created.getDisplay())
    log('info', 'Successfully set the verb display in cache', { success: true })
  }

  // store in the cache
  await VerbClient.setVerb(created.fields.id, created.fields)
  log('info', 'Successfully set verb in cache', { success: true })
  return created
}
