import { Router } from 'express'
import getActivityAttemptCountController from './activities/get-attempt-count.controller.js'
import getActivityAverageDurationController from './activities/get-average-duration.controller.js'
import getActivityByIdController from './activities/get-by-id.controller.js'
import getCourseActivitiesWithAttemptsController from './activities/get-course-activities-with-attempts.controller.js'
import getLeastAttemptedCourseActivityController from './activities/get-least-attempted-course-activity.controller.js'
import getMostAttemptedCourseActivityController from './activities/get-most-attempted-course-activity.controller.js'
import getMostUsedActivitiesController from './activities/get-most-interacted.controller.js'
import getAgentCountController from './agents/get-agent-count.controller.js'
import getLeastActiveAgentController from './agents/get-agent-with-least-activities.controller.js'
import getMostActiveAgentController from './agents/get-agent-with-most-activities.controller.js'
import getAgentByIdController from './agents/get-by-id.controller.js'
import getMultipleAgentsForActivityController from './agents/get-multiple-for-activity.controller.js'
import getAgentsWithSessionDataController from './agents/get-multiple.controller.js'
import getStatsForAgentController from './agents/get-stats-for-agent.controller.js'
import getCourseActivityCompletionAvgController from './content/get-course-activity-completion-avg.controller.js'
import getCourseCountController from './content/get-course-count.controller.js'
import getSessionsForAgentController from './session/get-multiple-for-agent.controller.js'
import getMostUsedVerbsController from './verbs/get-most-used-verb.controller.js'

const router = Router()

// Define Dashboard routes

router.get('/most-used-activities', getMostUsedActivitiesController)
router.get('/agent-count', getAgentCountController)
router.get('/most-active-agent', getMostActiveAgentController)
router.get('/least-active-agent', getLeastActiveAgentController)
router.get('/most-used-verbs', getMostUsedVerbsController)
router.get('/agents-with-session-data', getAgentsWithSessionDataController)
router.get('/stats-for-agent/:id', getStatsForAgentController)
router.get('/agent/:id', getAgentByIdController)
router.get('/agent-sessions/:id', getSessionsForAgentController)
router.get('/course-count', getCourseCountController)
router.get('/most-attempted-activity', getMostAttemptedCourseActivityController)
router.get('/least-attempted-activity', getLeastAttemptedCourseActivityController)
router.get('/course-activities-with-attempt-data', getCourseActivitiesWithAttemptsController)
router.get('/course-activity-average', getCourseActivityCompletionAvgController)
router.get('/activity-attempt-count', getActivityAttemptCountController)
router.get('/activity-average-duration', getActivityAverageDurationController)
router.get('/agents-for-activity', getMultipleAgentsForActivityController)
router.get('/activity', getActivityByIdController)

export default router
