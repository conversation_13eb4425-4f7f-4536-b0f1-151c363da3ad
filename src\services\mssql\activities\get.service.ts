import mssql, { getRows, DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivityModel from '../../../models/activity.model.js'
import getActivityDefinitionDescription from './description/get.service.js'
import getActivityDefinitionName from './name/get.service.js'
import { Activity, ActivityTableName } from '@tess-f/sql-tables/dist/lrs/activity.js'

export default async function getActivityService(id: string): Promise<ActivityModel> {
  const pool = mssql.getPool()
  const record = await getRows<Activity>(ActivityTableName, pool.request(), { ID: id })
  const activity = new ActivityModel(undefined, record[0])

  // attach definition name if there is one
  try {
    activity.attachDefinitionName(await getActivityDefinitionName(id))
  } catch (error) {
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    } // if we don't find the name this activity doesn't have one set
  }

  // attach definition description if there is one
  try {
    activity.attachDefinitionDescription(await getActivityDefinitionDescription(id))
  } catch (error) {
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    } // if we don't find the name this activity doesn't have one set
  }

  return activity
}
