// GET, gets Agent profile
import logger from '@lcs/logger'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import { getAgentProfileService, getAgentProfilesService } from '../../../../services/mssql/agent/profile/get.service.js'
import getOrCreateAgent from '../../../../services/mssql/agent/get-or-create.service.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus
import AgentModel from '../../../../models/agents.model.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import getFile from '../../../../services/amqp/file/get.service.js'

const log = logger.create('HTTP-Controller.Get-Activity-State', httpLogTransformer)

export default async function (req: Request, res: Response) {

  const profileID = req.query.profileId?.toString()
  const agent = await getOrCreateAgent(new AgentModel(JSON.parse(req.query.agent!.toString())))
  const since = req.query.since?.toString()

  if (profileID) {
    try {
      const agentProfile = await getAgentProfileService(profileID, agent.ID)
      log('info', 'Successfully retrieved agent profile', { id: agentProfile.fields.ProfileID, success: true, req })
      if (agentProfile.fields.Etag) {
        res.setHeader('ETag', `W/"${agentProfile.fields.Etag}"`)
      }
      res.setHeader('Last-Modified', agentProfile.fields.Updated!.toISOString())
      res.setHeader('Content-Type', agentProfile.fields.ContentType!)

      if (agentProfile.fields.FileID) {
        // Retrieve file from FDS and send back to caller
        const file = await getFile(agentProfile.fields.FileID)
        res.set('Content-disposition', `filename=${file.filename}`)
        res.send(file.buffer.toString())
      } else {
        res.send(agentProfile.fields.JsonProfile)
      }

    } catch (error) {
      const errorMessage = getErrorMessage(error)
      if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
        log('warn', 'Failed to get agent profile because was not found in the database', { success: false, profileID, errorMessage, req })
        res.status(NOT_FOUND).send('Agent profile not found')
      } else {
        log('error', 'Failed to get agent profile', { success: false, errorMessage, req })
        res.sendStatus(INTERNAL_SERVER_ERROR)
      }
    }
  } else {
    // No profile id means we want an array of profile ids
    try {
      const agentsProfile = await getAgentProfilesService(agent.ID, since)
      log('info', 'Successfully retrieved agent profile', { profileIds: agentsProfile.map(agent=>agent.fields.ProfileID).join(', '), success: true, req })
      res.json(agentsProfile.map(profile => profile.fields.ProfileID))

    } catch (error) {
      if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
        log('warn', 'Failed to get agent profile because was not found in the database', { success: false, profileID, req })
        res.status(NOT_FOUND).send('Agent profile not found')
      } else {
        log('error', 'Failed to get agent profiles', { success: false, errorMessage: getErrorMessage(error), req })
        res.sendStatus(INTERNAL_SERVER_ERROR)
      }
    }
  }
}
