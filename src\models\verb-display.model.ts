import { Table } from '@lcs/mssql-utility'
import { VerbDisplay, VerbDisplayFields, VerbDisplayTableName } from '@tess-f/sql-tables/dist/lrs/verb-display.js'

export default class VerbDisplayModel extends Table<VerbDisplay, VerbDisplay> {
  fields: VerbDisplay

  constructor(fields: VerbDisplay) {
    super(VerbDisplayTableName, [
      VerbDisplayFields.VerbID,
      VerbDisplayFields.Display,
      VerbDisplayFields.Lang
    ])
    this.fields = fields
  }

  importFromDatabase(record: VerbDisplay): void {
    this.fields = record
  }

  exportJsonToDatabase(): VerbDisplay {
    return this.fields
  }
}
