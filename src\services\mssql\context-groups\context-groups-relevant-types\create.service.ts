import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { ContextGroupRelevantTypes, ContextGroupRelevantTypesFields, ContextGroupRelevantTypesTableName } from '@tess-f/sql-tables/dist/lrs/context-group-relevant-types.js'
import getOrCreateRelevantType from '../../relevant-types/get-or-create.service.js'

export default async function createContextGroupRelevantTypes (iri: string, contextGroupId: string) {
  const request = mssql.getPool().request()
  const relevantType = await getOrCreateRelevantType(iri)

  request.input('relevantTypeId', relevantType.ID)
  request.input('contextGroupId', contextGroupId)

  const response = await request.query<ContextGroupRelevantTypes>(`
    INSERT INTO [${ContextGroupRelevantTypesTableName}] ([${ContextGroupRelevantTypesFields.ContextGroupID}], [${ContextGroupRelevantTypesFields.TypeID}])
    OUTPUT INSERTED.*
    VALUES (@contextGroupId, @relevantTypeId)
  `)

  if (response.recordset.length <= 0) {
    throw new Error(dbErrors.default.DB_INSERT_FAILED_TO_OUTPUT)
  }

  return response.recordset[0]
}