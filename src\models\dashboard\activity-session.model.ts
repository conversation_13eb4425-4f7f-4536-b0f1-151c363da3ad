import { Table } from '@lcs/mssql-utility'
import ActivityDescriptionModel from '../activity-descriptions.model.js'
import ActivityName from '../activity-names.model.js'

interface InteractionComponent {
  id: string,
  description?: {[key: string]: string}
}

interface ActivitySessionJSON {
  id: string
  objectType?: string
  definition?: {
    name?: {[key: string]: string},
    description?: {[key: string]: string},
    type?: string,
    moreInfo?: string,
    extensions?: {[key: string]: any},
    interactionType?: string,
    correctResponsePatter?: string[],
    choices?: InteractionComponent[],
    scale?: InteractionComponent[],
    source?: InteractionComponent[],
    target?: InteractionComponent[],
    steps?: InteractionComponent[]
  },
  attempts?: number
}

export interface ActivitySessionDatabaseRecord {
  ID: string
  ObjectType: string
  DefinitionType: string | null
  DefinitionMoreInfo: string | null
  InteractionType: string | null
  CorrectResponsePattern: string | null
  Choice: string | null
  Scale: string | null
  Source: string | null
  Target: string | null
  Steps: string | null
  DefinitionExtension: string | null
  TotalRecords?: number
  Attempts?: number
}

export const TABLE_NAME = 'Activities'

export default class ActivitySession extends Table<ActivitySessionJSON, ActivitySessionDatabaseRecord> {
  fields: ActivitySessionJSON

  constructor (fields?: ActivitySessionJSON, record?: ActivitySessionDatabaseRecord) {
    super(TABLE_NAME, ['ID'])
    if (fields) {
      this.fields = fields
    } else {
      this.fields = { objectType: 'Activity', id: '' }
    }

    if (record) {
      this.importFromDatabase(record)
    }
  }

  importFromDatabase (record: ActivitySessionDatabaseRecord): void {
    this.fields.id = record.ID
    this.fields.objectType = record.ObjectType
    if (record.DefinitionType ||
      record.DefinitionMoreInfo ||
      record.InteractionType ||
      record.CorrectResponsePattern ||
      record.Choice ||
      record.Scale ||
      record.Source ||
      record.Target ||
      record.Steps ||
      record.DefinitionExtension
    ) {
      this.fields.definition = {
        type: record.DefinitionType ? record.DefinitionType : undefined,
        moreInfo: record.DefinitionMoreInfo ? record.DefinitionMoreInfo : undefined,
        interactionType: record.InteractionType ? record.InteractionType : undefined,
        correctResponsePatter: record.CorrectResponsePattern ? JSON.parse(record.CorrectResponsePattern) : undefined,
        choices: record.Choice ? JSON.parse(record.Choice) : undefined,
        scale: record.Scale ? JSON.parse(record.Scale) : undefined,
        source: record.Source ? JSON.parse(record.Source) : undefined,
        target: record.Target ? JSON.parse(record.Target) : undefined,
        steps: record.Steps ? JSON.parse(record.Steps) : undefined,
        extensions: record.DefinitionExtension ? JSON.parse(record.DefinitionExtension) : undefined
      }
    }
    this.fields.attempts = record.Attempts
  }

  exportJsonToDatabase (): ActivitySessionDatabaseRecord {
    return {
      ID: this.fields.id,
      ObjectType: this.fields.objectType ? this.fields.objectType : 'Activity', // if object type is not present then it is an activity
      DefinitionType: this.fields.definition?.type || null,
      DefinitionMoreInfo: this.fields.definition?.moreInfo || null,
      InteractionType: this.fields.definition?.interactionType || null,
      CorrectResponsePattern: this.fields.definition?.correctResponsePatter ? JSON.stringify(this.fields.definition.correctResponsePatter) : null,
      Choice: this.fields.definition?.choices ? JSON.stringify(this.fields.definition.choices) : null,
      Scale: this.fields.definition?.scale ? JSON.stringify(this.fields.definition.scale) : null,
      Source: this.fields.definition?.source ? JSON.stringify(this.fields.definition.source) : null,
      Target: this.fields.definition?.target ? JSON.stringify(this.fields.definition.target) : null,
      Steps: this.fields.definition?.steps ? JSON.stringify(this.fields.definition.steps) : null,
      DefinitionExtension: this.fields.definition?.extensions ? JSON.stringify(this.fields.definition.extensions) : null
    }
  }

  attachDefinitionName (names: ActivityName[]): void {
    if (!this.fields.definition) {
      this.fields.definition = {}
    }
    this.fields.definition.name = names.reduce((a, v) => ({ ...a, [v.fields.Lang!]: v.fields.Display }), {})
  }

  attachDefinitionDescription (descriptions: ActivityDescriptionModel[]): void {
    if (!this.fields.definition) {
      this.fields.definition = {}
    }
    this.fields.definition.description = descriptions.reduce((a, v) => ({ ...a, [v.fields.Lang!]: v.fields.Display }), {})
  }

  getDefinitionName (): ActivityName[] {
    if (this.fields.definition?.name) {
      const ret: ActivityName[] = []
      for (const key in this.fields.definition.name) {
        ret.push(new ActivityName({
          ActivityID: this.fields.id,
          Lang: key,
          Display: this.fields.definition.name[key]
        }))
      }
      return ret
    }
    return []
  }

  getDefinitionDescription (): ActivityDescriptionModel[] {
    if (this.fields.definition?.description) {
      const ret: ActivityDescriptionModel[] = []
      for (const key in this.fields.definition.description) {
        ret.push(new ActivityDescriptionModel({
          ActivityID: this.fields.id,
          Lang: key,
          Display: this.fields.definition.description[key]
        }))
      }
      return ret
    }
    return []
  }
}
