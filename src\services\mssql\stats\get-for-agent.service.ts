import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'

export default async function getStatsForAgent(id: string): Promise<{ CourseCount: number, SessionCount: number }> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('actorID', id)

  const query = `
    WITH courseCounts AS (
      SELECT COUNT(DISTINCT [ObjectActivityID]) AS CourseCount, [ActorID]
      FROM [${StatementTableName}]
      WHERE [ActorID] = @actorID
      AND [VerbID] = 'https://adlnet.gov/expapi/verbs/initialized'
      AND [ContextRegistration] IS NOT NULL
      GROUP BY [ActorID]
    )

    SELECT
      COUNT(DISTINCT [ContextRegistration]) AS [SessionCount],
      COALESCE([courseCounts].[CourseCount], 0) AS [CourseCount]
    FROM [${StatementTableName}]
      LEFT JOIN [courseCounts] ON [courseCounts].[ActorID] = [${StatementTableName}].[ActorID]
    WHERE [${StatementTableName}].[ActorID] = @actorID
    GROUP BY [CourseCount]
  `

  const results = await request.query<{ CourseCount: number, SessionCount: number }>(query)

  if (results.recordset.length === 1) {
    return results.recordset[0]
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
