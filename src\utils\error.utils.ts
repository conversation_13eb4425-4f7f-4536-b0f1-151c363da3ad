export class VoidStatementError extends Error {
  __proto__ = Error

  constructor (message: string) {
    super(message)
    Object.setPrototypeOf(this, VoidStatementError.prototype)
  }
}

export class EtagPreconditionFail extends Error {
  __proto__ = Error

  constructor (message: string) {
    super(message)
    Object.setPrototypeOf(this, EtagPreconditionFail.prototype)
  }
}

export class ConflictError extends Error {
  __proto__ = Error

  constructor (message: string) {
    super(message)
    Object.setPrototypeOf(this, ConflictError.prototype)
  }
}
