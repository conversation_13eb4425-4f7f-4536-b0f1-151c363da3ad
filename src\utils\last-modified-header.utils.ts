import type { Response } from 'express'
import type StatementModel from '../models/statement.model.js'

function getMaxStoredTime(body: any): Date | null {
  let maxStoredTime: Date | null = null

  // Handle single statement response
  if (body.stored) {
    maxStoredTime = new Date(body.stored)
  }
  // Handle multiple statements response
  else if (body.statements && Array.isArray(body.statements)) {
    for (const statement of body.statements) {
      if (statement.stored) {
        const storedTime = new Date(statement.stored)
        if (!maxStoredTime || storedTime > maxStoredTime) {
          maxStoredTime = storedTime
        }
      }
    }
  }

  return maxStoredTime
}

// Helper function to extract max stored time from StatementModel objects
function getMaxStoredTimeFromModels(statements: StatementModel[]): Date | null {
  let maxStoredTime: Date | null = null

  for (const statement of statements) {
    // Handle StatementModel objects with fields property
    if (statement.fields?.stored) {
      const storedTime = new Date(statement.fields.stored)
      if (!maxStoredTime || storedTime > maxStoredTime) {
        maxStoredTime = storedTime
      }
    }
  }

  return maxStoredTime
}

/**
 * Sets the Last-Modified header based on statement data in the response body
 * Call this function before sending a JSON response with statement data
 */
export function setLastModifiedFromBody(res: Response, body: unknown): void {
  if (res.statusCode === 200 && body) {
    const maxStoredTime = getMaxStoredTime(body)

    // Set the Last-Modified header if we found a stored timestamp
    if (maxStoredTime && !isNaN(maxStoredTime.getTime())) {
      res.setHeader('Last-Modified', maxStoredTime.toUTCString())
    }
  }
}

/**
 * Sets the Last-Modified header based on StatementModel objects
 * Call this function before sending multipart responses with statement data
 */
export function setLastModifiedFromModels(res: Response, statements: StatementModel[]): void {
  if (res.statusCode === 200 && statements && Array.isArray(statements)) {
    const maxStoredTime = getMaxStoredTimeFromModels(statements)

    if (maxStoredTime && !isNaN(maxStoredTime.getTime())) {
      res.setHeader('Last-Modified', maxStoredTime.toUTCString())
    }
  }
}

 

