import { expect } from 'chai'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NOT_FOUND, OK } = httpStatus
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import middleware from './xapi-consistent-through-header.middleware.js'

describe('HTTP Middleware: XAPI Consistent Through Header', () => {
  afterEach(() => Sinon.restore())

  it('should include the "X-Experience-API-Consistent-Through" header in every response.', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.header('X-Experience-API-Consistent-Through')).to.exist
    expect(mocks.res.statusCode).to.eq(OK)
  })
})
