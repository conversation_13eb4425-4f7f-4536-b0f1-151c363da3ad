import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import ActivityNameModel from '../../../../models/activity-names.model.js'
import { ActivityName, ActivityNameTableName } from '@tess-f/sql-tables/dist/lrs/activity-name.js'

export default async function updateActivityDefinitionName(activityName: ActivityNameModel[]): Promise<ActivityNameModel[]> {
  if (activityName.length <= 0) {
    return []
  }
  await deleteRow<ActivityName>(mssql.getPool().request(), ActivityNameTableName, { ActivityID: activityName[0].fields.ActivityID })
  const name = await Promise.all(activityName.map(async activityName => addRow<ActivityName>(mssql.getPool().request(), activityName)))
  return name.map(record => new ActivityNameModel(record))
}