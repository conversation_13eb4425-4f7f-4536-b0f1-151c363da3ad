import { expect } from 'chai'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, OK } = httpStatus
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import middleware from './body-parser.middleware.js'

describe('HTTP Middleware: body-parser', () => {
  afterEach(() => Sinon.restore())

  it('should reject requests with malformed JSON', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    const err = new SyntaxError('Some JSON error')

    middleware(err, mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.statusCode).to.eq(BAD_REQUEST)
  })

  it('should reject requests with some internal server error', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    const err = { message: 'Some internal server error', status: INTERNAL_SERVER_ERROR }

    middleware(err, mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.statusCode).to.eq(INTERNAL_SERVER_ERROR)
  })

  it('should reject requests with some Unknown error', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    const err = { message: 'Some Unknown error' }

    middleware(err, mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.statusCode).to.eq(INTERNAL_SERVER_ERROR)
  })

  it('should accept requests with no error', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    const err = undefined

    middleware(err, mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.statusCode).to.eq(OK)
  })

})