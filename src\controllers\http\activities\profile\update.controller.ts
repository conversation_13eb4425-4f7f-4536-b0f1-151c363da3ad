// PUT, update Profile Resource
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { File } from 'formidable'
import { createHash } from 'crypto'
import getOrCreateActivityProfileService from '../../../../services/mssql/activity-profile/get-or-create.service.js'
import ActivityProfileModel from '../../../../models/activity-profile.model.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, CONFLICT, INTERNAL_SERVER_ERROR, NO_CONTENT, PRECONDITION_FAILED } = httpStatus
import fsExtra from 'fs-extra'
import parseForm from '../../../../utils/parse-form.js'
import { RESOURCE_DOES_NOT_EXIST, checkModificationConditions, RESOURCE_DETECTED_ERROR, MISSING_ETAG_INFO, NO_RESOURCE_MATCH } from '../../../../utils/etag.utils.js'
import { getErrorMessage, getErrorStackTrace, httpLogTransformer } from '@tess-f/backend-utils'
import { EtagPreconditionFail } from '../../../../utils/error.utils.js'
import deleteActivityProfile from '../../../../services/mssql/activity-profile/delete.service.js'
import deleteFile from '../../../../services/amqp/file/delete.service.js'
import saveFile from '../../../../services/amqp/file/save.service.js'
import updateActivityProfile from '../../../../services/mssql/activity-profile/update.service.js'

const log = logger.create('HTTP-Controller.Put-Activity-Profile', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    let file: File | undefined
    let base64Contents: string | undefined
    let profile: string | undefined
    const hasher = createHash('sha256')
    let etag: string | undefined

    if (req.is('multipart/*')) {
      // get the file from the request
      const { files } = await parseForm(req)
      let fileCount = 0
      for (const fileKey in files) {
        if (files[fileKey]!.length > 1) {
          fileCount += files[fileKey]!.length
        }
        fileCount++
        file = files[fileKey]![0]
      }
      if (fileCount > 1) {
        // cannot have more than 1 file
        res.status(BAD_REQUEST).send('Invalid form: request cannot contain more than 1 file')
        // remove the temp uploaded files
        for (const fileKey in files) {
          for (const f of files[fileKey]!) {
            await fsExtra.remove(f.filepath)
          }
        }
        return
      }

      if (file) {
        base64Contents = await fsExtra.readFile(file.filepath, 'base64')
        await fsExtra.remove(file.filepath)
        hasher.update(base64Contents)
        etag = hasher.digest('hex')
      }

    } else if (req.is('application/octet-stream')) {
      // payload is a buffer
      profile = (<Buffer>req.body).toString('utf-8')
      hasher.update(profile)
      etag = hasher.digest('hex')

    } else {
      profile = JSON.stringify(req.body)
      hasher.update(profile)
      etag = hasher.digest('hex')
    }

    // get or create the activity profile record
    const { activityProfile, created } = await getOrCreateActivityProfileService(new ActivityProfileModel({
      ID: req.query.profileId!.toString(),
      ActivityID: req.query.activityId!.toString(),
      ContentType: req.headers['content-type']
    }))

    try {
      checkModificationConditions(req, activityProfile.fields.Etag, created, true)
    } catch (error) {
      if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DOES_NOT_EXIST) {
        log('warn', 'Failed to create activity profile: ETag Precondition failed with resource does not exist. Removing created profile record', { success: false })
        await deleteActivityProfile(activityProfile.fields.ID!, activityProfile.fields.ActivityID!)
        if (activityProfile.fields.FileID) {
          await deleteFile([activityProfile.fields.FileID])
        }
      }

      throw error
    }

    activityProfile.fields.Etag = etag

    if (!created && activityProfile.fields.FileID) {
      // we did not create the record, and it had a file
      // delete the old file
      log('verbose', 'Activity profile had a file previously, removing old file')
      try {
        await deleteFile([activityProfile.fields.FileID])
      } catch (error) {
        // something went wrong the file is now orphaned
        log('error', 'Failed to delete previous profile file', { fileId: activityProfile.fields.FileID, errorMessage: getErrorMessage(error) })
      }
    }

    if (file && base64Contents) {
      // we are updating the file
      activityProfile.fields.Profile = null
      activityProfile.fields.ContentType = file.mimetype ?? 'text/plain'
      log('verbose', 'Activity profile is file, uploading to FDS')
      activityProfile.fields.FileID = await saveFile(base64Contents, file.originalFilename ?? 'profile-file.txt', activityProfile.fields.ContentType)
    } else if (profile) {
      activityProfile.fields.Profile = profile
      activityProfile.fields.ContentType = req.headers['content-type'] ?? 'application/json'
    }

    if (req.header('updated') && Date.parse(req.header('updated')!)) {
      activityProfile.fields.ModifiedOn = new Date(req.header('updated')!)
    } else {
      activityProfile.fields.ModifiedOn = new Date()
    }

    await updateActivityProfile(activityProfile)
    log('info', 'Successfully updated activity profile', {
      profileId: activityProfile.fields.ID,
      activityId: activityProfile.fields.ActivityID,
      success: true
    })

    res.sendStatus(NO_CONTENT)
  } catch (error) {
    if (error instanceof EtagPreconditionFail && error.message === MISSING_ETAG_INFO) {
      log('warn', 'Failed to update activity profile: missing etag header', { errorMessage: error.message, success: false })
      res.status(BAD_REQUEST).send(error.message)

    } else if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DOES_NOT_EXIST) {
      log('warn', 'Failed to update activity profile: resource does not exist', { errorMessage: error.message, success: false })
      res.status(PRECONDITION_FAILED).send(error.message)

    } else if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DETECTED_ERROR) {
      log('warn', 'Failed to update activity profile: Resource already exists', { errorMessage: error.message, success: false })
      res.status(PRECONDITION_FAILED).send(error.message)

    } else if (error instanceof EtagPreconditionFail && error.message === NO_RESOURCE_MATCH) {
      log('warn', 'No resources matched your etag precondition', { errorMessage: error.message, success: false })
      res.status(PRECONDITION_FAILED).send(error.message)

    } else {
      log('error', 'Failed to save activity profile: unknown error', { errorMessage: getErrorMessage(error), errorStack: getErrorStackTrace(error), success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}