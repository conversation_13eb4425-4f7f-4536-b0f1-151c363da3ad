import mssql, { addRow } from '@lcs/mssql-utility'
import ContextGroup from '../../../models/context-group.model.js'
import { ContextGroups }from '@tess-f/sql-tables/dist/lrs/context-groups.js'
import createContextGroupRelevantTypes from './context-groups-relevant-types/create.service.js'

export default async function createContextGroup (contextGroup: ContextGroup): Promise<ContextGroup> {
  const pool = mssql.getPool()
  const record = await addRow<ContextGroups>(pool.request(), contextGroup)
  const created = new ContextGroup(undefined, record)

  if (contextGroup.fields.relevantTypes && contextGroup.fields.relevantTypes.length > 0) {
    created.attachRelevantTypes(await Promise.all(contextGroup.fields.relevantTypes.map(async relevantType => {
      await createContextGroupRelevantTypes(relevantType, created.Id)
      return relevantType
    })))
  }
  
  
  return created
}