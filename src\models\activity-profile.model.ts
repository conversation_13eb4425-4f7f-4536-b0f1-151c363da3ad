import { Table } from '@lcs/mssql-utility'
import { ActivityProfile, ActivityProfileFields, ActivityProfileTableName } from '@tess-f/sql-tables/dist/lrs/activity-profile.js'

export default class ActivityProfileModel extends Table<ActivityProfile, ActivityProfile> {
  fields: ActivityProfile

  constructor(fields: ActivityProfile) {
    super(ActivityProfileTableName, [
      ActivityProfileFields.ID,
      ActivityProfileFields.ActivityID,
      ActivityProfileFields.ContentType
    ])
    this.fields = fields
  }

  importFromDatabase(record: ActivityProfile): void {
    this.fields = record
  }

  exportJsonToDatabase(): ActivityProfile {
    return this.fields
  }

}