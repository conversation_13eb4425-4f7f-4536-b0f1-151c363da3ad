import mssql, { addRow } from '@lcs/mssql-utility'
import AgentProfileModel from '../../../../models/agent-profile.model.js'
import { AgentProfile } from '@tess-f/sql-tables/dist/lrs/agent-profiles.js'

export default async function createAgentProfileService(agentProfile: AgentProfileModel): Promise<AgentProfileModel> {
  const pool = mssql.getPool()
  const created = await addRow<AgentProfile>(pool.request(), agentProfile)
  const createdActivityState = new AgentProfileModel(created)

  return createdActivityState
}