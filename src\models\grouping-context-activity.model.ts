import { Table } from '@lcs/mssql-utility'
import { GroupingContextActivity, GroupingContextActivityFields, GroupingContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/grouping-context-activity.js'

export default class GroupingContextActivityModel extends Table<GroupingContextActivity, GroupingContextActivity> {
  public fields: GroupingContextActivity

  constructor(fields: GroupingContextActivity) {
    super(GroupingContextActivityTableName, [
      GroupingContextActivityFields.StatementID,
      GroupingContextActivityFields.ActivityID
    ])
    this.fields = fields
  }

  public importFromDatabase(record: GroupingContextActivity): void {
    this.fields = record
  }

  public exportJsonToDatabase(): GroupingContextActivity {
    return this.fields
  }
}
