{
  "configurations": [
    {
      "type": "node",
      "name": "Attach to dev server",
      "request": "attach",
      "port": 9229
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Mocha Current File",
      "program": "${workspaceFolder}/node_modules/mocha/bin/_mocha",
      //"program": "${workspaceFolder}/node_modules/ts-mocha/bin/ts-mocha",
      "protocol": "inspector",
      "args": [
          "--timeout",
          "999999",
          "--colors",
          "${file}",
          "-r",
          "ts-node/register",
          "--exit"
      ],
      "env": { "NODE_ENV": "dev", "LOG_LEVEL": "debug", "LOAD_DEV_ENV": "true", "CONFIG_PATHS":"./test-config.yaml" },
      "console": "integratedTerminal",
      "sourceMaps": true,
      "internalConsoleOptions": "neverOpen"
    }
  ]
}