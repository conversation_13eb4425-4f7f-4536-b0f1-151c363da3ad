import { expect } from 'chai'
import validateVerb from './validate.service.js'

describe('Validate Verb', () => {
  it('throws an error when the verb is not a dictionary', () => {
    expect(validateVerb.bind(validateVerb, 'climbing'))
      .to.throw('Verb is not a properly formatted dictionary')
  })

  it('throws an error when the verb contains unallowed fields', () => {
    expect(validateVerb.bind(validateVerb, {
      id: 'running',
      display: 'run',
      foo: 'bar'
    })).to.throw('Invalid field(s) found in Verb - foo')
  })

  it('throws an error when the verb is missing an id field', () => {
    expect(validateVerb.bind(validateVerb, {
      display: 'running'
    })).to.throw('Verb must contain an id')
  })

  it('throws an error when the verb id is not a valid IRI', () => {
    expect(validateVerb.bind(validateVerb, {
      id: 'running'
    })).to.throw('Verb id with value running was not a valid IRI')
  })

  it('throws an error when the verb is voided and the statement object is not a statement ref', () => {
    expect(validateVerb.bind(validateVerb, {
      id: 'http://adlnet.gov/expapi/verbs/voided'
    })).to.throw('Statement with voided verb must have StatementRef as objectType')

    expect(validateVerb.bind(validateVerb, {
      id: 'http://adlnet.gov/expapi/verbs/voided'
    }, { objectType: 'Activity' })).to.throw('Statement with voided verb must have StatementRef as objectType')

    expect(validateVerb.bind(validateVerb, {
      id: 'http://adlnet.gov/expapi/verbs/voided'
    }, { foo: 'bar' })).to.throw('Statement with voided verb must have StatementRef as objectType')
  })

  it('throws an error when the verb display is not a dictionary', () => {
    expect(validateVerb.bind(validateVerb, {
      id: 'http://example.com/verb#1',
      display: 'verb 1'
    })).to.throw('Verb display is not a properly formatted dictionary')
  })

  it('throws an error when the verb display is not a valid lang map', () => {
    expect(validateVerb.bind(validateVerb, {
      id: 'http://example.com/verb#1',
      display: {
        'myFancyLang': 'verb 1'
      }
    })).to.throw('language myFancyLang is not valid in verb display')
  })

  it('throws an error when the verb display has an invalid value', () => {
    expect(validateVerb.bind(validateVerb, {
      id: 'http://example.com/verb#98',
      display: {
        'en-US': null
      }
    })).to.throw('verb display contains a null value')
  })

  it('returns void when valid', () => {
    expect(validateVerb({
      id: 'http://example.com/verb#1',
      display: {
        'en-US': 'Verb 1'
      }
    })).to.equal(void 0)
  })
})
