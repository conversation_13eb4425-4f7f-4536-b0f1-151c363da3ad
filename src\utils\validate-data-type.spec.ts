import { expect } from 'chai'
import {
  checkIfDict,
  checkIfList,
  validateDictValues,
  validateIri,
  validateUUID,
  checkAllowedFields,
  checkRequiredFields
} from './validate-data-type.js'

describe('Validate if object is a dictionary', () => {
  it('returns void when the given object is a dict', () => {
    expect(checkIfDict({
      id: 'some-value',
      foo: 'val 2',
      bar: 'val 3'
    }, 'my-test-object')).to.equal(void 0)
  })

  it('throws an error when the object is an array', () => {
    expect(checkIfDict.bind(checkIfDict, ['1', '2', '3'], 'my test array'))
      .to.throw('my test array is not a properly formatted dictionary')
  })

  it('throws an error when the object is a string', () => {
    expect(checkIfDict.bind(checkIfDict, 'test value', 'my test string'))
      .to.throw('my test string is not a properly formatted dictionary')
  })

  it('throws an error when the object is a number', () => {
    expect(checkIfDict.bind(checkIfDict, 42, 'my test number'))
      .to.throw('my test number is not a properly formatted dictionary')
  })

  it('throws an error when the object is a boolean', () => {
    expect(checkIfDict.bind(checkIfDict, true, 'my test boolean'))
      .to.throw('my test boolean is not a properly formatted dictionary')
  })
})

describe('Validate if object is a list', () => {
  it('returns void when object is a list', () => {
    expect(checkIfList(['1', '2', '3'], 'my list of strings')).to.equal(void 0)
    expect(checkIfList([1, 2, 3], 'my list of numbers')).to.equal(void 0)
    expect(checkIfList([{ foo: 1, bar: 1 }, { foo: 2, bar: 2 }], 'my list of objects')).to.equal(void 0)
  })

  it('throws an error when the object is an object', () => {
    expect(checkIfList.bind(checkIfList, { foo: 1, bar: 2 }, 'my object'))
      .to.throw('my object is not a properly formatted array')
  })

  it('throws an error when the object is a number', () => {
    expect(checkIfList.bind(checkIfList, 2, 'my number'))
      .to.throw('my number is not a properly formatted array')
  })

  it('throws an error when the object is a boolean', () => {
    expect(checkIfList.bind(checkIfList, true, 'my boolean'))
      .to.throw('my boolean is not a properly formatted array')
  })

  it('throws an error when the object is a string', () => {
    expect(checkIfList.bind(checkIfList, 'test', 'my string'))
      .to.throw('my string is not a properly formatted array')
  })
})

describe('Validates the values of a dictionary', () => {
  it('returns void when all properties contain a value', () => {
    expect(validateDictValues([1, '2', { foo: 'bar' }, true, false, [1, 2, 3]], 'my test values')).to.equal(void 0)
  })

  it('throws an error if one of the values is undefined', () => {
    expect(validateDictValues.bind(validateDictValues, [1, '2', 'test', { foo: 'bar' }, undefined, 3], 'undefined value set'))
      .to.throw('undefined value set contains a null value')
  })

  it('throws an error if one of the values is null', () => {
    expect(validateDictValues.bind(validateDictValues, [2, '30', 'orange', { bar: 'foo' }, null, 'green'], 'null value set'))
      .to.throw('null value set contains a null value')
  })
})

describe('Validates an IRI value', () => {
  it('returns void when the value is valid', () => {
    expect(validateIri('http://my.domain.net:8042/docs/spec?name=test#section1', 'my test IRI')).to.equal(void 0)
  })

  it('throws an error when the IRI value is an object', () => {
    expect(validateIri.bind(validateIri, { foo: 'bar' }, 'test object'))
      .to.throw('test object must be a string type')
  })

  it('throws an error when the IRI value is an array', () => {
    expect(validateIri.bind(validateIri, [1, 2, 3], 'test object'))
      .to.throw('test object must be a string type')
  })

  it('throws an error when the IRI value is a boolean', () => {
    expect(validateIri.bind(validateIri, true, 'test object'))
      .to.throw('test object must be a string type')
  })

  it('throws an error when the IRI value is a number', () => {
    expect(validateIri.bind(validateIri, 3, 'test object'))
      .to.throw('test object must be a string type')
  })

  //Systems accepting IRIs MAY also deal with the printable characters in
  //US-ASCII that are not allowed in URIs, namely "<", ">", '"', space,
  //"{", "}", "|", "\", "^", and "`", in step 2 above...
  //https://datatracker.ietf.org/doc/html/rfc3987
  it('throws an error when the IRI value is not properly formatted', () => {
    expect(validateIri.bind(validateIri, 'proto://test/org 30', 'test iri'))
      .to.throw('test iri with value proto://test/org 30 was not a valid IRI')
  })
})

describe('Validate a UUID', () => {
  it('returns void when the UUID is a valid v1 uuid', () => {
    expect(validateUUID('4f853cd6-3534-11ee-be56-0242ac120002', 'test-uuid')).to.equal(void 0)
  })

  it('returns void when the UUID is a valid v4 uuid', () => {
    expect(validateUUID('1f3a2c86-f4f5-4bc7-a917-eb5c860a69d5', 'test-uuid')).to.equal(void 0)
  })

  it('returns void when the UUID is nil', () => {
    expect(validateUUID('00000000-0000-0000-0000-000000000000', 'test-uuid')).to.equal(void 0)
  })

  it('throws an error when the UUID value is a boolean', () => {
    expect(validateUUID.bind(validateUUID, true, 'test uuid'))
      .to.throw('test uuid must be a string type')
  })

  it('throws an error when the UUID value is a number', () => {
    expect(validateUUID.bind(validateUUID, 3, 'test uuid'))
      .to.throw('test uuid must be a string type')
  })

  it('throws an error when the UUID value is an array', () => {
    expect(validateUUID.bind(validateUUID, [1, 2, 3], 'test uuid'))
      .to.throw('test uuid must be a string type')
  })

  it('throws an error when the UUID value is an object', () => {
    expect(validateUUID.bind(validateUUID, { foo: 'bar' }, 'test uuid'))
      .to.throw('test uuid must be a string type')
  })

  it('throws an error when the UUID is not valid', () => {
    expect(validateUUID.bind(validateUUID, '1f3a2c86-f4f5-4bc7-g917-eb5c860a69d5', 'test uuid'))
      .to.throw('test uuid is not a valid uuid')
  })
})

describe('Checks that object only contains allowed fields', () => {
  it('returns void when object only has allowed fields', () => {
    expect(checkAllowedFields(['id', 'verb', 'actor'], {
      id: 'test',
      verb: {
        id: 'id'
      },
      actor: ['bob']
    }, 'test object')).to.equal(void 0)
  })

  it('returns void when object is missing allowed filed but does not contain extra fields', () => {
    expect(checkAllowedFields(['id', 'verb', 'actor'], {
      id: 'test',
      actor: ['bob']
    }, 'test object')).to.equal(void 0)
  })

  it('throws an error when the object has an extra field', () => {
    expect(checkAllowedFields.bind(checkAllowedFields, ['id', 'foo'], {
      id: 'test',
      foo: { bar: 'test' },
      bar: [1, 2, 3]
    }, 'bad object'))
      .to.throw('Invalid field(s) found in bad object - bar')
  })

  it('throws an error when with a list of unallowed fields', () => {
    expect(checkAllowedFields.bind(checkAllowedFields, ['id', 'foo'], {
      id: 'test',
      foo: { bar: 'test' },
      bar: [1, 2, 3],
      test: true
    }, 'bad object'))
      .to.throw('Invalid field(s) found in bad object - bar, test')
  })
})

describe('Checks that an object contains required fields', () => {
  it('returns void when the object has all the required fields', () => {
    expect(checkRequiredFields(['id', 'verb', 'actor'], {
      id: 'test-1-2-3',
      verb: {
        id: 'test'
      },
      actor: 'bob'
    }, 'test object')).to.equal(void 0)
  })

  it('throws an error when the object is missing the required field', () => {
    expect(checkRequiredFields.bind(checkRequiredFields, ['id'], { foo: 'bar', bar: 'baz', test: 1 }, 'bad object'))
      .to.throw('id is missing in bad object')
  })
})
