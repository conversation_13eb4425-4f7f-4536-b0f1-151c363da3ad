import { expect } from 'chai'
import {
  validateEmail,
  validateSha1sumEmail
} from './validate-email.js'

describe('Validates an mbox email address', () => {
  it('returns void for a valid mbox address', () => {
    expect(validateEmail('mailto:<EMAIL>')).to.equal(void 0)
  })

  it('throws an error if the address is a number', () => {
    expect(validateEmail.bind(validateEmail, 3))
      .to.throw('mbox value must be a string type')
  })

  it('throws an error if the address is a boolean', () => {
    expect(validateEmail.bind(validateEmail, false))
      .to.throw('mbox value must be a string type')
  })

  it('throws an error if the address is an object', () => {
    expect(validateEmail.bind(validateEmail, { foo: 'bar' }))
      .to.throw('mbox value must be a string type')
  })

  it('throws an error if the address is an array', () => {
    expect(validateEmail.bind(validateEmail, [3, 2, 1]))
      .to.throw('mbox value must be a string type')
  })

  it('throws an error when the address does not start with mailto', () => {
    expect(validateEmail.bind(validateEmail, '<EMAIL>'))
      .to.throw('<NAME_EMAIL> did not start with mailto:')
  })

  it('throws an error when the address is not properly formed', () => {
    expect(validateEmail.bind(validateEmail, 'mailto:william.blizzard'))
      .to.throw('mbox value mailto:william.blizzard is not a valid email')
  })
})

describe('Validates an SHA1sum email address', () => {
  it('returns void for a valid SHA1sum value', () => {
    expect(validateSha1sumEmail('abcdefABCDEF0123456789abcdefABCDEF012345')).to.equal(void 0)
  })

  it('throws an error when the SHA1sum is too short', () => {
    expect(validateSha1sumEmail.bind(validateSha1sumEmail, 'abcdefABCDEF0123456789abcdefABCDEF01234'))
      .to.throw('mbox_sha1sum value [abcdefABCDEF0123456789abcdefABCDEF01234] is not a valid sha1sum')
  })

  it('throws an error when the SHA1sum is too long', () => {
    expect(validateSha1sumEmail.bind(validateSha1sumEmail, 'abcdefABCDEF0123456789abcdefABCDEF01234567'))
      .to.throw('mbox_sha1sum value [abcdefABCDEF0123456789abcdefABCDEF01234567] is not a valid sha1sum')
  })

  it('throws an error when the SHA1sum is contains invalid characters', () => {
    expect(validateSha1sumEmail.bind(validateSha1sumEmail, 'abcdefABCDEG0123456789abcdefABCDEF012345'))
      .to.throw('mbox_sha1sum value [abcdefABCDEG0123456789abcdefABCDEF012345] is not a valid sha1sum')
  })

  it('throws an error when the test value is a number', () => {
    expect(validateSha1sumEmail.bind(validateSha1sumEmail, 391283404582))
      .to.throw('mbox_sha1sum value must be a string type')
  })

  it('throws an error when the test value is an object', () => {
    expect(validateSha1sumEmail.bind(validateSha1sumEmail, { foo: 'bar' }))
      .to.throw('mbox_sha1sum value must be a string type')
  })

  it('throws an error when the test value is an array', () => {
    expect(validateSha1sumEmail.bind(validateSha1sumEmail, ['bar', 'baz']))
      .to.throw('mbox_sha1sum value must be a string type')
  })

  it('throws an error when the test value is a boolean', () => {
    expect(validateSha1sumEmail.bind(validateSha1sumEmail, false))
      .to.throw('mbox_sha1sum value must be a string type')
  })
})
