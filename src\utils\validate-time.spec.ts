import { expect } from 'chai'
import { validateTimestamp } from './validate-time.js'

describe('Validate timestamp', () => {
  it('throws an error when timestamp is not valid', () => {
    expect(validateTimestamp.bind(validateTimestamp, 'test')).to.throw('Invalid date time stamp: test')
  })

  it('returns void for rfc 3339 timestamp', () => {
    expect(validateTimestamp('2025-09-02 19:11:31Z')).to.equal(void 0)
    expect(validateTimestamp('2025-09-02t19:11:31Z')).to.equal(void 0)
    expect(validateTimestamp('2008-09-15T15:53:00.601+00:00')).to.equal(void 0)
  })

  it('returns void for iso8601 timestamp', () => {
    expect(validateTimestamp('2022-09-02T19:11:31Z')).to.equal(void 0)
  })

  it('returns void for timestamps that contain milliseconds', () => {
    expect(validateTimestamp('2025-09-03T13:49:25.944Z')).to.equal(void 0)
    expect(validateTimestamp('2023-05-04T12:00:00-05:00')).to.equal(void 0)
    expect(validateTimestamp('2030-09-03T13:49:24.737Z')).to.equal(void 0)
  })
})