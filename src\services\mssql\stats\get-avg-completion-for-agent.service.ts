import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { CMI5Verbs } from '@tess-f/sql-tables/dist/lrs/verb.js'

export default async function getAvgCompletionForAgent(agentID: string, registration?: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('agentID', agentID)
  request.input('verb', CMI5Verbs.Completed)
  if (registration) {
    request.input('registration', registration)
  }

  const query = `
    SELECT AVG([ResultScoreScaled]) as Completion
    FROM [${StatementTableName}]
    WHERE [VerbID] = @verb
    AND [ActorID] = @agentID
    ${registration ? 'AND [ContextRegistration] = @registration' : ''}
  `

  const result = await request.query<{ Completion: number }>(query)

  if (result.recordset.length === 1 && result.recordset[0].Completion !== null) {
    return result.recordset[0].Completion
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
