# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/) and this project adheres to [Semantic Versioning](https://semver.org/).

## [Unreleased](https://gihub.northgrum.com/LCS-TESS/TESS-LRS-Server/compare/v2.0.2...development)

### Added

### Changed

### Deprecated

### Removed

### Fixed

### Security

## [v2.0.2](https://gihub.northgrum.com/LCS-TESS/TESS-LRS-Server/compare/v2.0.1...v2.0.2)

### Added

- cookieSigned and cookieSecure settings
- Prometheus metrics

### Changed

- Updated log data
- Updated error responses
- Updated hard coded http status codes to use codes from the http-status library
- Updated express to version 5
- Updated etag hashing algorithms to sha256

### Security

- Updated base image to one that is FIPS enabled

## [v2.0.1](https://gihub.northgrum.com/LCS-TESS/TESS-LRS-Server/tree/v2.0.1)

### Fixed

- Saving/updating activity state that is not a json object

### Removed

- Removed unused code

### Security

- Updated package dependencies

## [v2.0.0](https://gihub.northgrum.com/LCS-TESS/TESS-LRS-Server/tree/v2.0.0)

### Changed

- Update to ESM module resolution

### Security

- Updated session authority and added an override for the ws package to address security vulnerabilities.

## [v1.0.4](https://gihub.northgrum.com/LCS-TESS/TESS-LRS-Server/tree/v1.0.4)

### Security

- Updated upstream image to ubi9 micro with nodejs 20.14.0

## [v1.0.3](https://gihub.northgrum.com/LCS-TESS/TESS-LRS-Server/tree/v1.0.3)

### Security

- Updated package dependencies

## [v1.0.2](https://gihub.northgrum.com/LCS-TESS/TESS-LRS-Server/tree/v1.0.2)

### Added

- AMQP route to search statements

## [v1.0.1](https://gihub.northgrum.com/LCS-TESS/TESS-LRS-Server/tree/v1.0.1)

### Fixed

- Updated logger to version that is compatible with elasticsearch v7

## [v1.0.0](https://gihub.northgrum.com/LCS-TESS/TESS-LRS-Server/tree/v1.0.0)

### Security

- Updated dependencies
- Updated upstream to UBI 8 with Node.JS 20

### Fixed 
- Fix to unit tests
- Added condition for NO_RESOURCE_MATCH in activity update controller
- Added NO_RESOURCE_MATCH for agent profile update controller
- Modifications to etag.utils conditions to return 400 and 409
- Added application/octet-stream content type to agent profile
- Added etag validation to agent profile 
- Added validation post-agent-profile if agent already exists
- Removed checkModificationConditions for state controller
- Fix added missing "If-Match" or "If-None-Match header" for activity profile middleware
- Fix to agent profile delete not sending a response
- Unit tests updates to agent profile 
- Fixes to agent profile: application/json validation, added application/octet-stream content-type, rename incorrect function names
- Fix to validation to delete agent profile
- Fix to update activity definition name
- Fixed version header processing so that the about route can be called without setting the version header
- Fix to return agent properties as array, if agent not found on db 
- Correct spelling of correctResponsesPattern property in activity model, update to mutable properties of activity
- Fix to content-type for activity state
- Added content-type validation to activity state
- Fix to delete state activity not returning a status
- Fix to get statement controller to return statements in DESC order if ascending is not defined
- Fix to check each attachment has a sha2
- Fix check session.middleware when authentication is in body.content
- Fix put-statement.controller to get statement from body.content when application/x-www-form-urlencoded
- Update to get-statement.controller to return 10 statements instead of 100 (faster to test)
- Fix to request header for content-type application/x-www-form-urlencoded 
- Fix to agent not found in db message, should return agent from request
- Fix to reject timestamp -00:00 offset
- Fix to add authority to statements
- Update to unit tests to reflect ISO8601 date validation
- Fix to function validateTimestamp in order to use RegEx expression for ISO8601 date validation 
- Fix to get more statements by specifying 'offset' in query parameter 
- Fix to get multiple statements, added property verb.id to sub-statement model
- Added try catch before parsing JSON file on function multiPartMixedParser
- Added Statement validation for null values
- Fix to score min and max
- Fix to incorrect checking of min and max properties if they do not exist
- Added X-Experience-API-Consistent-Through header
- Convert context activities to list
- Fix to not retrieve voided statements

### Added
- Added bodyParserMiddleware to all routes
- Added JWS body and algorithm validation
- Added About Resource endpoint to LRS
- Added statement data validation
- Added GET Agents Resource endpoint
- Added GET Activities Resource endpoint
