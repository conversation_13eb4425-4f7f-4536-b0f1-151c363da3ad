import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import getVerbService from './get.service.js'
import createVerbService from './create.service.js'
import Verb from '../../../models/verb.model.js'
import updateVerbDisplay from './display/update.service.js'
import { RedisClient as VerbDisplayClient } from '../../redis/verb-display-client.service.js'

export default async function getOrCreateVerbService(verb: Verb): Promise<Verb> {
  try {
    const dbVerb = await getVerbService(verb.fields.id)
    let langMap = dbVerb.fields.display ?? {}
    if (verb.fields.display) {
      for (const key in verb.fields.display) {
        langMap[key] = verb.fields.display[key]
      }
    }
    dbVerb.fields.display = langMap
    // update the lang map
    dbVerb.attachDisplay(await updateVerbDisplay(dbVerb.getDisplay()))
    await VerbDisplayClient.setVerbDisplay(dbVerb.fields.id!, dbVerb.getDisplay())
    return dbVerb
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      return await createVerbService(verb)
    } else {
      throw error
    }
  }
}
