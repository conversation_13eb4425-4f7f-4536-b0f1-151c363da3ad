import { Table } from '@lcs/mssql-utility'
import { ActivityDescription, ActivityDescriptionFields, ActivityDescriptionTableName } from '@tess-f/sql-tables/dist/lrs/activity-description.js'


export default class ActivityDescriptionModel extends Table<ActivityDescription, ActivityDescription> {
  fields: ActivityDescription

  constructor(fields: ActivityDescription) {
    super(ActivityDescriptionTableName, [
      ActivityDescriptionFields.ActivityID,
      ActivityDescriptionFields.Display,
      ActivityDescriptionFields.Lang
    ])
    this.fields = fields
  }

  importFromDatabase(record: ActivityDescription): void {
    this.fields = record
  }

  exportJsonToDatabase(): ActivityDescription {
    return this.fields
  }
}
