import { Table } from '@lcs/mssql-utility'
import { ParentContextActivity, ParentContextActivityFields, ParentContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/parent-context-activity.js'

export default class ParentContextActivityModel extends Table<ParentContextActivity, ParentContextActivity> {
  public fields: ParentContextActivity

  constructor(fields: ParentContextActivity) {
    super(ParentContextActivityTableName, [
      ParentContextActivityFields.StatementID,
      ParentContextActivityFields.ActivityID
    ])
    this.fields = fields
  }

  public importFromDatabase(record: ParentContextActivity): void {
    this.fields = record
  }

  public exportJsonToDatabase(): ParentContextActivity {
    return this.fields
  }
}
