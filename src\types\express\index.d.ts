/* eslint-disable camelcase */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable no-unused-vars */

import { File } from 'formidable'
import { AgentJson } from '../../models/agents.model.js'

declare global {
  namespace Express {
    export interface Request {
      session: {
        sessionId: string
        userId: string
      }
      token: string
      files?: File[]
      authority: AgentJson
    }
  }
}
