import { Table } from '@lcs/mssql-utility'
import {
  SubStatementParentContextActivity,
  SubStatementParentContextActivityFields,
  SubStatementParentContextActivityTableName
} from '@tess-f/sql-tables/dist/lrs/sub-statement-parent-context-activity.js'

export default class SubStatementParentContextActivityModel extends Table<SubStatementParentContextActivity, SubStatementParentContextActivity> {
  public fields: SubStatementParentContextActivity

  constructor(fields: SubStatementParentContextActivity) {
    super(SubStatementParentContextActivityTableName, [
      SubStatementParentContextActivityFields.ActivityID,
      SubStatementParentContextActivityFields.StatementID
    ])
    this.fields = fields
  }

  public importFromDatabase(record: SubStatementParentContextActivity): void {
    this.fields = record
  }

  public exportJsonToDatabase(): SubStatementParentContextActivity {
    return this.fields
  }
}
