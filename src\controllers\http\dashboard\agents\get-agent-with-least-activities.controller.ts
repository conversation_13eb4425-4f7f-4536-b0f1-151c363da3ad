import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getAgentWithLeastActivitiesService from '../../../../services/mssql/agent/get-agent-with-least-activities.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Dashboard-Get-Least-Active-Agent', httpLogTransformer)

export default async function getLeastActiveAgentController(req: Request, res: Response) {
  try {
    const leastActiveAgent = await getAgentWithLeastActivitiesService()
    log('info', 'Successfully retrieved the least active agent', { req, ID: leastActiveAgent.ID, success: true })
    res.json(leastActiveAgent.fields)
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get least active agent because none was found in the database', { req, success: false })
      res.status(httpStatus.NOT_FOUND).send('Could not find the least active agent')
    } else {
      log('error', 'Failed to get the least active agent', { errorMessage: getErrorMessage(error), req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
