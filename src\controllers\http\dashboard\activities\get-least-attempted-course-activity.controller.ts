import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getCourseActivityWithLeastAttempts from '../../../../services/mssql/activities/get-course-activity-with-least-attempts.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Least-Attempted-Course-Activity', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const activity = await getCourseActivityWithLeastAttempts()
    log('info', 'Successfully found the course activity with the least number of attempts', { req, id: activity.fields.id, success: true })
    res.json(activity.fields)
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get least attempted course activity because none was found in the database', { req, success: false })
      res.status(httpStatus.NOT_FOUND).send('No activity found')
    } else {
      log('error', 'Failed to get least attempted course activity', { errorMessage: getErrorMessage(error), req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
