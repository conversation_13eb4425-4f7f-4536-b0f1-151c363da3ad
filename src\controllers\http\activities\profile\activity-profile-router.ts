import { Router } from 'express'
import createActivityProfileController from './create.controller.js'
import getActivityProfileController from './get.controller.js'
import deleteActivityProfileController from './delete.controller.js'
import updateActivityProfileController from './update.controller.js'
import validateGetProfileMiddleware from './middleware/get-profile.middleware.js'
import validatePostProfileMiddleware from './middleware/post-profile.middleware.js'
import validatePutProfileRequest from './middleware/validate-put-profile.middleware.js'
import validateDeleteProfileMiddleware from './middleware/delete-profile.middleware.js'

const router = Router()

router.post('/', validatePostProfileMiddleware, createActivityProfileController)
router.get('/', validateGetProfileMiddleware, getActivityProfileController)
router.delete('/', validateDeleteProfileMiddleware, deleteActivityProfileController)
router.put('/', validatePutProfileRequest, updateActivityProfileController)

export default router
