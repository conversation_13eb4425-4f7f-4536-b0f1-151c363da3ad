import mssql, { addRow } from '@lcs/mssql-utility'
import ActivityStateModel from '../../../models/activity-state.model.js'
import { ActivityState } from '@tess-f/sql-tables/dist/lrs/activity-state.js'

export default async function createActivityStateService(activityState: ActivityStateModel): Promise<ActivityStateModel> {
  const pool = mssql.getPool()
  const created = await addRow<ActivityState>(pool.request(), activityState)
  const createdActivityState = new ActivityStateModel(created)

  return createdActivityState
}
