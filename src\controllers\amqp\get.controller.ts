import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import getByIdService from '../../services/mssql/statements/get-by-id.service.js'
import { StatementJson } from '../../models/statement.model.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { getErrorMessage } from '@tess-f/backend-utils'
import { AgentJson } from '../../models/agents.model.js'
import getBySearchService from '../../services/mssql/statements/get-by-search.service.js'
import { clamp } from '../../utils/number.utils.js'

export default async function getStatementsController(message: RpcMessage<{
  format?: string
  statementId?: string
  voidedStatementId?: string
  agent?: <PERSON><PERSON><PERSON>
  relatedAgents?: boolean
  verb?: string
  activity?: string
  relatedActivities?: boolean
  registration?: string
  since?: Date
  until?: Date
  limit?: number
  ascending?: boolean
  offset?: number
}>): Promise<RpcResponse<{ statements: StatementJson[], totalRecords: number }>> {
  let format = 'exact'
  if (message.data.format && ['exact', 'ids'].includes(message.data.format)) {
    format = message.data.format
  }

  if (message.data.statementId || message.data.voidedStatementId) {
    const statementId: string = message.data.statementId ?? message.data.voidedStatementId!

    try {
      const statement = await getByIdService(statementId, false, !message.data.statementId)
      return {
        success: true,
        data: { statements: [statement.toJson(undefined, format)], totalRecords: 1 }
      }
    } catch (error) {
      if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
        return {
          success: false,
          data: { statements: [], totalRecords: 0 },
          message: 'Not Found'
        }
      } else {
        return {
          success: false,
          message: getErrorMessage(error)
        }
      }
    }
  } else {
    try {
      const statementsResponse = await getBySearchService(
        message.data.offset ?? 0,
        clamp(message.data.limit ?? 100, 10, 100),
        message.data.agent,
        message.data.verb,
        message.data.activity,
        message.data.registration,
        message.data.relatedActivities,
        message.data.relatedAgents,
        message.data.since,
        message.data.until,
        message.data.ascending
      )

      return {
        success: true,
        data: {
          statements: statementsResponse.statements.map(statement => statement.toJson(undefined, format)),
          totalRecords: statementsResponse.totalRecords
        }
      }
    } catch (error) {
      return {
        success: false,
        message: getErrorMessage(error)
      }
    }
  }

}
