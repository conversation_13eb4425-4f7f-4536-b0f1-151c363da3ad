import { expect } from 'chai'
import validate, { validateVoidStatement } from './validate.service.js'
import { v4 as uuid } from 'uuid'
import Sinon from 'sinon'
import * as getStatementByIdService from '../../mssql/statements/get-by-id.service.js'
import StatementModel from '../../../models/statement.model.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import esmock from 'esmock'

describe('Validate Statement(s)', () => {
  it('throws an error if a statement id is reused', () => {
    expect(validate.bind(validate, [
      {
        id: 'test-id'
      },
      {
        id: 'test-id-2'
      },
      {
        objectType: 'StatementRef'
      },
      {
        id: 'test-id'
      }
    ])).to.throw('Statement batch contains duplicate IDs')
  })

  it('throws an error if the statement is not an array or object', () => {
    expect(validate.bind(validate, 'my statement'))
      .to.throw('There are no statements to validate, payload: my statement')
  })

  it('throws an error when given an empty object to validate', () => {
    expect(validate.bind(validate, {}))
      .to.throw('There are no statements to validate, payload:')
  })
})

describe('Validate Statement', () => {
  it('throws an error if the statement is no a dictionary', () => {
    expect(validate.bind(validate, 'my statement'))
      .to.throw('There are no statements to validate, payload: my statement')
  })

  it('throws an error if the statement contains unallowed fields', () => {
    expect(validate.bind(validate, {
      id: uuid(),
      foo: 'bar',
      bar: 'baz'
    })).to.throw('Invalid field(s) found in Statement - foo, bar')
  })

  it('throws an error if the statement is missing required fields', () => {
    expect(validate.bind(validate, {
      id: uuid(),
      actor: {
        mbox: 'mailto:<EMAIL>'
      }
    })).to.throw('verb is missing in Statement')
  })

  it('throws an error if the supplied version is not a string', () => {
    expect(validate.bind(validate, {
      id: uuid(),
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      version: 32
    })).to.throw('Version must be a string')
  })

  it('throws an error if the supplied version is not valid', () => {
    expect(validate.bind(validate, {
      id: uuid(),
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      version: '3.0'
    })).to.throw('3.0 is not a supported version')
  })

  it('throws an error when the included id is not a uuid', () => {
    expect(validate.bind(validate, {
      id: 'test-id',
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      }
    })).to.throw('Statement id is not a valid uuid')
  })

  it('throws an error when the timestamp is not valid', () => {
    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      timestamp: '08-07-2032T03'
    })).to.throw('Timestamp error - There was an error while parsing the date from 08-07-2032T03 -- Error: Invalid date time stamp: 08-07-2032T03')

    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      timestamp: '2032-08-07T03:08:00.000-00'
    })).to.throw('Invalid date time stamp: 2032-08-07T03:08:00.000-00')

    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      timestamp: '01/011/2015'
    })).to.throw('Timestamp error - There was an error while parsing the date from 01/011/2015 -- Error: Invalid date time stamp: 01/011/2015')

    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      timestamp: '2032-08-07T03:08:00.000-0000'
    })).to.throw('Invalid date time stamp: 2032-08-07T03:08:00.000-0000')

  })

  it('throws an error if a date time cannot be parsed from the stored value', () => {
    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      stored: '2032-08-07T03:08:00.000-00'
    })).to.throw('Invalid date time stamp: 2032-08-07T03:08:00.000-00')
  })

  it('throws an error when the actor is not valid', () => {
    expect(validate.bind(validate, {
      actor: {
        mbox: '<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      }
    })).to.throw('<NAME_EMAIL> did not start with mailto:')
  })

  it('throws an error when the verb is not valid', () => {
    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'ran'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      }
    })).to.throw('Verb id with value ran was not a valid IRI')
  })

  it('throws an error when the object is not valid', () => {
    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: 'statement-1'
      }
    })).to.throw('StatementRef id is not a valid uuid')
  })

  it('throws an error when the result is not valid', () => {
    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      result: 'pass'
    })).to.throw('Result is not a properly formatted dictionary')
  })

  it('throws an error when the context is not valid', () => {
    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      context: 37
    })).to.throw('Context is not a properly formatted dictionary')
  })

  it('throws an error when the authority is not a valid agent', () => {
    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      authority: {
        account: {
          first_name: 'Bob',
          last_name: 'Smith',
          id: 'bob.smith'
        }
      }
    })).to.throw('Invalid field(s) found in Account - first_name, last_name, id')
  })

  it('throws an error when the authority is a group and is not valid', () => {
    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      authority: {
        objectType: 'Group',
        name: 'Team PB',
        mbox: 'mailto:<EMAIL>',
        member: [
          {
            mbox: 'mailto:<EMAIL>'
          }
        ]
      }
    })).to.throw('Groups representing authorities must only contain 2 members')
  })

  it('throws an error when the attachment is not valid', () => {
    expect(validate.bind(validate, {
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      },
      attachments: {
        1: 'file.txt',
        2: 'file.v2.txt'
      }
    })).to.throw('Attachments is not a properly formatted array')
  })

  it('returns void when the statement is valid', () => {
    expect(validate({
      actor: {
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb#1'
      },
      object: {
        objectType: 'StatementRef',
        id: uuid()
      }
    })).to.equal(void 0)
  })

  it('throws an error when "name" property is null', () => {
    expect(validate.bind(validate, {
      actor: {
        objectType: 'Agent',
        name: null,
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://adlnet.gov/expapi/verbs/attended',
        display: {
          'en-GB': 'attended',
          'en-US': 'attended'
        }
      },
      object: {
        objectType: 'Activity',
        id: 'http://www.example.com/meetings/occurances/34534'
      }
    })).to.throw('name property contains a null value.')
  })

  it('throws an error when "registration" property is null', () => {
    expect(validate.bind(validate, {
      actor: {
        objectType: 'Agent',
        name: 'xAPI account',
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://adlnet.gov/expapi/verbs/attended',
        display: {
          'en-GB': 'attended',
          'en-US': 'attended'
        }
      },
      context: {
        registration: null,
        revision: 'rev_10_3_2',
        platform: 'Example virtual meeting software',
        language: 'tlh',
        statement: {
          objectType: 'StatementRef',
          id: '6690e6c9-3ef0-4ed3-8b37-7f3964730bee'
        },
        extensions: {
          'http://example.com/profiles/meetings/contextextensions/airspeed': '600mph',
          'http://example.com/profiles/meetings/contextextensions/pilot': {
            name: 'Thomas',
            id: 'http://openid.com/342'
          }
        }
      },
      object: {
        objectType: 'Activity',
        id: 'http://www.example.com/meetings/occurances/34534'
      }
    })).to.throw('registration property contains a null value.')
  })

})

describe('Validate Voided Statement', () => {
  afterEach(() => Sinon.restore())

  it('throws an error when the statement to be voided has already been voided', async () => {
    const stub = Sinon.stub().resolves(new StatementModel(undefined, {
      ID: uuid(),
      ActorID: uuid(),
      Voided: true,
      Raw: '{"id": "statement-id"}',
      Stored: new Date(),
      Timestamp: new Date(),
      Version: '1.0.0',
      VerbID: 'http://example.com/verb/1'
    }))
    
    const mock = await esmock('./validate.service.js', {
      '../../mssql/statements/get-by-id.service.js': {
        default: stub
      },
      '../../mssql/statements/build.service.ts': {
        default: () => {}
      }
    })
    
    try {
      await mock.validateVoidStatement('test')
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.include('Statement with ID:')
      expect(error.message).to.include('is already voided, cannot unvoid. Please re-issue the statement under a new ID.')
    }
  })

  it('throws an error when attempting to void a voiding statement', async () => {
    const stub = Sinon.stub().resolves(new StatementModel(undefined, {
      ID: uuid(),
      ActorID: uuid(),
      Voided: false,
      Raw: '{"id": "statement-id"}',
      Stored: new Date(),
      Timestamp: new Date(),
      Version: '1.0.0',
      VerbID: 'http://adlnet.gov/expapi/verbs/voided'
    }))
    
    const mock = await esmock('./validate.service.js', {
      '../../mssql/statements/get-by-id.service.js': {
        default: stub
      },
      '../../mssql/statements/build.service.ts': {
        default: () => {}
      }
    })

    try {
      await mock.validateVoidStatement('test')
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.include('Statement with ID:')
      expect(error.message).to.include('is a voiding statement and cannot be voided')
    }
  })

  it('throws an error when the service encounters an error other than a not found error', async () => {
    const mock = await esmock('./validate.service.js', {
      '../../mssql/statements/get-by-id.service.js': {
        default: Sinon.stub().rejects(new Error('service error'))
      },
      '../../mssql/statements/build.service.ts': {
        default: () => {}
      }
    })

    try {
      await mock.validateVoidStatement('test')
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.include('service error')
    }
  })

  it('returns void when the statement to be voided cannot be found', async () => {
    const mock = await esmock('./validate.service.js', {
      '../../mssql/statements/get-by-id.service.js': {
        default: Sinon.stub().rejects(new Error(dbErrors.default.NOT_FOUND_IN_DB))
      },
      '../../mssql/statements/build.service.ts': {
        default: () => {}
      }
    })

    expect(await mock.validateVoidStatement('test')).to.be.undefined
  })

  it('returns void when the voiding statement is valid', async () => {
    const mock = await esmock('./validate.service.js', {
      '../../mssql/statements/get-by-id.service.js': {
        default: Sinon.stub().resolves(new StatementModel(undefined, {
          ID: uuid(),
          ActorID: uuid(),
          Voided: false,
          Raw: '{"id": "statement-id"}',
          Stored: new Date(),
          Timestamp: new Date(),
          Version: '1.0.0',
          VerbID: 'http://example.com/verbs/1'
        }))
      },
      '../../mssql/statements/build.service.ts': {
        default: () => {}
      }
    })
    expect(await mock.validateVoidStatement('test')).to.be.undefined
  })
})
