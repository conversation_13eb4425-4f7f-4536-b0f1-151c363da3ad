import { expect } from 'chai'
import validateResult from './validate.service.js'

describe('Validate Result', () => {
  it('throws an error if the result is not a dictionary', () => {
    expect(validateResult.bind(validateResult, 'success'))
      .to.throw('Result is not a properly formatted dictionary')
  })

  it('throws an error if the result contains unallowed fields', () => {
    expect(validateResult.bind(validateResult, {
      success: true,
      foo: 'bar'
    })).to.throw('Invalid field(s) found in Result - foo')
  })

  it('throws an error if the result contains an invalid duration', () => {
    expect(validateResult.bind(validateResult, {
      duration: '3Y4M2DT4H35M'
    })).to.throw('Error with result duration')
  })

  it('throws an error if the result success property is not a boolean', () => {
    expect(validateResult.bind(validateResult, {
      success: 'true'
    })).to.throw('Result success must be a boolean value')
  })

  it('throws an error if the result completion property is not a boolean', () => {
    expect(validateResult.bind(validateResult, {
      completion: 'true'
    })).to.throw('Result completion must be a boolean value')
  })

  it('throws an error if the result response is not a string', () => {
    expect(validateResult.bind(validateResult, {
      response: { foo: 'bar' }
    })).to.throw('Result response must be a string')
  })

  it('throws an error if the result extension is invalid', () => {
    expect(validateResult.bind(validateResult, {
      extensions: 'my-extension'
    })).to.throw('result extensions extensions is not a properly formatted dictionary')
  })

  describe('Score', () => {
    it('throws an error if the score is not a dictionary', () => {
      expect(validateResult.bind(validateResult, {
        score: 42
      })).to.throw('Score is not a properly formatted dictionary')
    })

    it('throws an error if the score contains unallowed fields', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          foo: 'bar',
          bar: 'baz'
        }
      })).to.throw('Invalid field(s) found in Score - foo, bar')
    })

    it('throws an error if the raw score is not a number', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          raw: '30.3'
        }
      })).to.throw('Score raw is not a number')
    })

    it('throws an error if the min score is not a number', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          min: '40.2',
          max: 20.3
        }
      })).to.throw('Score minimum is not a decimal')
    })

    it('throws an error if the max score is not a number', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          min: 30.2,
          max: '50'
        }
      })).to.throw('Score maximum is not a decimal')
    })

    it('throws an error if the min score is greater than the max score', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          min: 100,
          max: 80
        }
      })).to.throw('Score minimum in statement result must be less than the maximum')
    })

    it('throws an error if the min score is equal to the max score', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          min: 100,
          max: 100
        }
      })).to.throw('Score minimum in statement result must be less than the maximum')
    })

    it('throws an error if the raw score is les than the min score', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          min: 50,
          max: 100,
          raw: 30
        }
      })).to.throw('Score raw value in statement result must be between minimum and maximum')
    })

    it('throws an error if the raw score is greater than the max score', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          min: 50,
          max: 89,
          raw: 100
        }
      })).to.throw('Score raw value in statement result must be between minimum and maximum')
    })

    it('throws an error if the score max is not a number and min is 0', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          min: 0,
          max: "one hundred",
          raw: 95
        }
      })).to.throw('Score maximum is not a decimal')
    })

    it('throws an error if the scaled property is not a number', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          scaled: '0.98'
        }
      })).to.throw('Score scaled is not a decimal')
    })

    it('throws an error if the scaled property is less than -1', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          scaled: -2
        }
      })).to.throw('Score scaled value in statement result must be between -1 and 1')
    })

    it('throws an error if the scaled property is greater than 1', () => {
      expect(validateResult.bind(validateResult, {
        score: {
          scaled: 3.5
        }
      })).to.throw('Score scaled value in statement result must be between -1 and 1')
    })
  })

  it('returns void when valid', () => {
    expect(validateResult({
      score: {
        raw: 75,
        min: 10,
        max: 100,
        scaled: 0.9
      },
      success: true,
      completion: true,
      response: 'pass',
      duration: 'PT3M',
      extensions: {
        'http://exmaple.com/result#93': 'pass'
      }
    })).to.equal(void 0)
  })
})
