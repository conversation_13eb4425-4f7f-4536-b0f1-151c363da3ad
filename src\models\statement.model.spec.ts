import { expect } from 'chai'
import Model, { StatementJson } from './statement.model.js'
import { fail } from 'assert'
import SubStatementModel, { StatementRef, SubStatementJson } from './sub-statement.model.js'
import AgentModel, { AgentJson } from './agents.model.js'
import ActivityModel, { ActivityJSON } from './activity.model.js'
import VerbDisplayModel from './verb-display.model.js'
import { v4 as uuid } from 'uuid'

describe('Statement Model', () => {
  it('throws an error when no data is provided to the model', () => {
    try {
      new Model()
      fail('Model should not create')
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('You must provide statement fields or database record')
    }
  })

  it('can import statement fields', () => {
    const model = new Model({
      id: 'test-id',
      verb: {
        id: 'test-verb'
      },
      timestamp: 'my-time-stamp'
    })
    expect(model.fields).to.exist
    expect(model.fields.id).to.equal('test-id')
    expect(model.fields.verb?.id).to.equal('test-verb')
    expect(model.fields.timestamp).to.equal('my-time-stamp')
  })

  it('sets the stored time when importing statement fields', () => {
    const model = new Model({
      id: 'test-id'
    })
    expect(model.fields).to.exist
    expect(model.fields.stored).to.exist
    const now = new Date()
    const timestamp = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}T`
    expect(model.fields.stored).to.contain(timestamp)
  })

  it('sets the timestamp equal to the stored time when no timestamp is provided', () => {
    const model = new Model({
      id: 'test-id'
    })
    expect(model.fields).to.exist
    expect(model.fields.timestamp).to.exist
    const now = new Date()
    const timestamp = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}T`
    expect(model.fields.stored).to.contain(timestamp)
    expect(model.fields.stored)
    expect(model.fields.timestamp).to.contain(timestamp)
    expect(model.fields.timestamp)
  })

  describe('Import Database Record', () => {
    it('sets the internal lookup ids', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id'
      })

      expect(model.fields).to.exist
      expect(model.fields.id).to.equal('statement-id')
      expect(model.actorID).to.equal('actor-id')
      expect(model.objectActivityID).to.equal('object-activity')
      expect(model.objectAgentID).to.equal('object-agent')
      expect(model.objectSubStatementID).to.equal('object-sub-statement')
      expect(model.contextInstructorID).to.equal('context-instructor')
      expect(model.contextTeamID).to.equal('context-team')
      expect(model.voided).to.be.true
    })

    it('attaches result completion', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ResultCompletion: true
      })

      expect(model.fields).to.exist
      expect(model.fields.result).to.exist
      expect(model.fields.result?.completion).to.be.true
    })

    it('attaches result duration', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ResultDuration: '5M'
      })

      expect(model.fields).to.exist
      expect(model.fields.result).to.exist
      expect(model.fields.result?.duration).to.equal('5M')
    })

    it('attaches result extensions', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ResultExtensions: '{"http://example.com/extension/1":"123"}'
      })

      expect(model.fields).to.exist
      expect(model.fields.result).to.exist
      expect(model.fields.result?.extensions).to.exist
      expect(JSON.stringify(model.fields.result?.extensions)).to.equal('{"http://example.com/extension/1":"123"}')
    })

    it('attaches result response', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ResultResponse: 'Lesson Completed'
      })

      expect(model.fields).to.exist
      expect(model.fields.result).to.exist
      expect(model.fields.result?.response).to.equal('Lesson Completed')
    })

    it('attaches result max score', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ResultScoreMax: 100
      })

      expect(model.fields).to.exist
      expect(model.fields.result).to.exist
      expect(model.fields.result?.score).to.exist
      expect(model.fields.result?.score?.max).to.equal(100)
    })

    it('attaches result min score', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ResultScoreMin: 10
      })

      expect(model.fields).to.exist
      expect(model.fields.result).to.exist
      expect(model.fields.result?.score).to.exist
      expect(model.fields.result?.score?.min).to.equal(10)
    })

    it('attaches result raw score', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ResultScoreRaw: 70
      })

      expect(model.fields).to.exist
      expect(model.fields.result).to.exist
      expect(model.fields.result?.score).to.exist
      expect(model.fields.result?.score?.raw).to.equal(70)
    })

    it('attaches result score scale', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ResultScoreScaled: 0.75
      })

      expect(model.fields).to.exist
      expect(model.fields.result).to.exist
      expect(model.fields.result?.score).to.exist
      expect(model.fields.result?.score?.scaled).to.equal(0.75)
    })

    it('attaches result success', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ResultSuccess: true
      })

      expect(model.fields).to.exist
      expect(model.fields.result).to.exist
      expect(model.fields.result?.success).to.be.true
    })

    it('attaches context statement', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ContextStatement: 'context-statement-id'
      })

      expect(model.fields).to.exist
      expect(model.fields.context).to.exist
      expect(model.fields.context?.statement).to.exist
      expect(model.fields.context?.statement?.objectType).to.equal('StatementRef')
      expect(model.fields.context?.statement?.id).to.equal('context-statement-id')
    })

    it('attaches context extensions', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ContextExtensions: '{"http://example.com/extensions/1": "pass"}'
      })

      expect(model.fields).to.exist
      expect(model.fields.context).to.exist
      expect(model.fields.context?.extensions).to.exist
      expect(JSON.stringify(model.fields.context?.extensions)).to.equal('{"http://example.com/extensions/1":"pass"}')
    })

    it('attaches context language', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ContextLanguage: 'en-US'
      })

      expect(model.fields).to.exist
      expect(model.fields.context).to.exist
      expect(model.fields.context?.language).to.equal('en-US')
    })

    it('attaches context platform', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ContextPlatform: 'linux'
      })

      expect(model.fields).to.exist
      expect(model.fields.context).to.exist
      expect(model.fields.context?.platform).to.equal('linux')
    })

    it('attaches context registration', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ContextRegistration: 'context-registration-id'
      })

      expect(model.fields).to.exist
      expect(model.fields.context).to.exist
      expect(model.fields.context?.registration).to.equal('context-registration-id')
    })

    it('attaches context revision', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ContextRevision: '3.0.0'
      })

      expect(model.fields).to.exist
      expect(model.fields.context).to.exist
      expect(model.fields.context?.revision).to.equal('3.0.0')
    })

    it('attaches Object Statement Ref', () => {
      const model = new Model(undefined, {
        ActorID: 'actor-id',
        ContextInstructorID: 'context-instructor',
        ContextTeamID: 'context-team',
        ObjectActivityID: 'object-activity',
        ObjectAgentID: 'object-agent',
        ObjectSubStatementID: 'object-sub-statement',
        Voided: true,
        Raw: '{"id": "orig-statement-id"}',
        Stored: new Date(),
        Timestamp: new Date(),
        Version: '2.0.0',
        VerbID: 'verb-id',
        ID: 'statement-id',
        ObjectStatementRef: 'statement-ref-id'
      })

      expect(model.fields).to.exist
      expect(model.fields.object).to.exist
      expect(model.fields.object?.objectType).to.equal('StatementRef')
      expect((model.fields.object as StatementRef).id).to.equal('statement-ref-id')
    })
  })

  it('attaches an agent to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachObjectAgent(new AgentModel({
      mbox: 'mailto:<EMAIL>',
      objectType: 'Agent'
    }))

    expect(model.fields.object).to.exist
    expect(model.fields.object?.objectType).to.equal('Agent')
    expect((model.fields.object as AgentJson).mbox).to.equal('mailto:<EMAIL>')
  })

  it('attaches an activity to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachObjectActivity(new ActivityModel({
      objectType: 'Activity',
      id: 'activity-1'
    }))

    expect(model.fields.object).to.exist
    expect(model.fields.object?.objectType).to.equal('Activity')
    expect((model.fields.object as ActivityJSON).id).to.equal('activity-1')
  })

  it('attaches a sub statement to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachObjectSubStatement(new SubStatementModel({
      actor: {
        mbox: 'mailto:<EMAIL>',
        objectType: 'Agent'
      },
      verb: {
        id: 'http://example.com/test/verb/1'
      },
      object: {
        objectType: 'Activity',
        id: 'http://example.com/activity/1'
      },
      objectType: 'SubStatement'
    }))

    expect(model.fields.object).to.exist
    expect(model.fields.object?.objectType).to.equal('SubStatement')
    expect((model.fields.object as SubStatementJson).actor).to.exist
    expect((model.fields.object as SubStatementJson).object).to.exist
    expect((model.fields.object as SubStatementJson).verb).to.exist
  })

  it('attaches an actor to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachActor(new AgentModel({
      mbox: 'mailto:<EMAIL>'
    }))

    expect(model.fields.actor).to.exist
    expect(model.fields.actor?.mbox).to.equal('mailto:<EMAIL>')
  })

  it('attaches an authority to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachAuthority(new AgentModel({
      mbox: 'mailto:<EMAIL>'
    }))

    expect(model.fields.authority).to.exist
    expect(model.fields.authority?.mbox).to.equal('mailto:<EMAIL>')
  })

  it('attaches a context instructor to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachContextInstructor(new AgentModel({
      mbox: 'mailto:<EMAIL>'
    }))

    expect(model.fields.context).to.exist
    expect(model.fields.context?.instructor).to.exist
    expect(model.fields.context?.instructor?.mbox).to.equal('mailto:<EMAIL>')

    // we should be able to replace the instructor as well
    model.attachContextInstructor(new AgentModel({
      mbox: 'mailto:<EMAIL>'
    }))

    expect(model.fields.context).to.exist
    expect(model.fields.context?.instructor).to.exist
    expect(model.fields.context?.instructor?.mbox).to.equal('mailto:<EMAIL>')
  })

  it('attaches a context team to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachContextTeam(new AgentModel({
      mbox: 'mailto:<EMAIL>'
    }))

    expect(model.fields.context).to.exist
    expect(model.fields.context?.team).to.exist
    expect(model.fields.context?.team?.mbox).to.equal('mailto:<EMAIL>')

    // we should be able to replace the team as well
    model.attachContextTeam(new AgentModel({
      mbox: 'mailto:<EMAIL>'
    }))

    expect(model.fields.context).to.exist
    expect(model.fields.context?.team).to.exist
    expect(model.fields.context?.team?.mbox).to.equal('mailto:<EMAIL>')
  })

  it('attaches a context parent activities to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachContextParentActivities([
      new ActivityModel({ id: 'http://example.com/activity/1' }),
      new ActivityModel({ id: 'http://example.com/activity/2' })
    ])

    expect(model.fields.context).to.exist
    expect(model.fields.context?.contextActivities).to.exist
    expect(model.fields.context?.contextActivities?.parent).to.exist
    expect(model.fields.context?.contextActivities?.parent?.length).to.equal(2)
    const ids = model.fields.context?.contextActivities?.parent?.map(act => act.id)
    expect(ids).to.contain('http://example.com/activity/1')
    expect(ids).to.contain('http://example.com/activity/2')
  })

  it('attaches a context other activities to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachContextOtherActivities([
      new ActivityModel({ id: 'http://example.com/activity/1' }),
      new ActivityModel({ id: 'http://example.com/activity/2' })
    ])

    expect(model.fields.context).to.exist
    expect(model.fields.context?.contextActivities).to.exist
    expect(model.fields.context?.contextActivities?.other).to.exist
    expect(model.fields.context?.contextActivities?.other?.length).to.equal(2)
    const ids = model.fields.context?.contextActivities?.other?.map(act => act.id)
    expect(ids).to.contain('http://example.com/activity/1')
    expect(ids).to.contain('http://example.com/activity/2')
  })

  it('attaches a context grouping activities to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachContextGroupingActivities([
      new ActivityModel({ id: 'http://example.com/activity/1' }),
      new ActivityModel({ id: 'http://example.com/activity/2' })
    ])

    expect(model.fields.context).to.exist
    expect(model.fields.context?.contextActivities).to.exist
    expect(model.fields.context?.contextActivities?.grouping).to.exist
    expect(model.fields.context?.contextActivities?.grouping?.length).to.equal(2)
    const ids = model.fields.context?.contextActivities?.grouping?.map(act => act.id)
    expect(ids).to.contain('http://example.com/activity/1')
    expect(ids).to.contain('http://example.com/activity/2')
  })

  it('attaches a context category activities to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachContextCategoryActivities([
      new ActivityModel({ id: 'http://example.com/activity/1' }),
      new ActivityModel({ id: 'http://example.com/activity/2' })
    ])

    expect(model.fields.context).to.exist
    expect(model.fields.context?.contextActivities).to.exist
    expect(model.fields.context?.contextActivities?.category).to.exist
    expect(model.fields.context?.contextActivities?.category?.length).to.equal(2)
    const ids = model.fields.context?.contextActivities?.category?.map(act => act.id)
    expect(ids).to.contain('http://example.com/activity/1')
    expect(ids).to.contain('http://example.com/activity/2')
  })

  it('attaches a verb with its canonical display to the fields object', () => {
    const model = new Model(undefined, {
      ID: 'statement-id',
      Stored: new Date(),
      Timestamp: new Date(),
      VerbID: 'verb-id',
      ActorID: 'actor-id',
      Raw: '{"id": "orig-statement-id"}'
    })

    model.attachVerbName([
      new VerbDisplayModel({ VerbID: 'http://example.com/verb/1', Display: 'verb', Lang: 'en-US' }),
      new VerbDisplayModel({ VerbID: 'http://example.com/verb/1', Display: 'verb', Lang: 'es-MX' }),
      new VerbDisplayModel({ VerbID: 'http://example.com/verb/1', Display: 'verb', Lang: 'en-GB' })
    ])

    expect(model.fields.verb).to.exist
    expect(model.fields.verb?.display).to.exist
    expect(model.fields.verb?.display!['en-US']).to.equal('verb')
    expect(model.fields.verb?.display!['en-GB']).to.equal('verb')
    expect(model.fields.verb?.display!['es-MX']).to.equal('verb')
  })

  it('returns the statement exactly as it was originally submitted', () => {
    const statementId = uuid()
    const stored = (new Date()).toISOString()

    const orig: StatementJson = {
      actor: {
        objectType: 'Agent',
        mbox: 'mailto:<EMAIL>'
      },
      verb: {
        id: 'http://example.com/verb/1',
        display: {
          'en-US': 'verb'
        }
      },
      object: {
        id: 'http://example.com/activity/1'
      },
      stored,
      timestamp: stored
    }

    const model = new Model(undefined, {
      ID: statementId,
      Stored: new Date(stored),
      Timestamp: new Date(stored),
      VerbID: 'http://example.com',
      ObjectActivityID: 'http://example.com/activity/1',
      Raw: JSON.stringify(orig)
    })

    const formatted = model.toJson(['es-MX'], 'exact')
    expect(JSON.stringify(formatted.actor)).to.contain(JSON.stringify(orig.actor))
    expect(JSON.stringify(formatted.verb)).to.contain(JSON.stringify(orig.verb))
    expect(JSON.stringify(formatted.object)).to.contain(JSON.stringify(orig.object))
    expect(formatted.id).to.equal(statementId)
  })

  it('returns the statement with only id data', () => {
    const statementId = uuid()
    const stored = (new Date()).toISOString()
    const registration = uuid()
    const contextStatement = uuid()

    const model = new Model(undefined, {
      ID: statementId,
      Stored: new Date(stored),
      Timestamp: new Date(stored),
      VerbID: 'http://example.com/verb/1',
      ObjectActivityID: 'http://example.com/activity/1',
      Raw: '{"id":"test-1"}',
      ResultCompletion: true,
      ResultResponse: 'passed',
      ResultScoreMax: 100,
      ResultScoreMin: 10,
      ResultScoreRaw: 80,
      ResultScoreScaled: 1,
      ResultSuccess: true,
      Version: '1.0.0',
      ContextExtensions: '{"http://example.com/extension/1":"pass"}',
      ContextLanguage: 'en-US',
      ContextPlatform: 'linux',
      ContextRegistration: registration,
      ContextRevision: '1.0.0',
      ContextStatement: contextStatement
    })

    model.attachActor(new AgentModel({
      mbox: 'mailto:<EMAIL>',
      objectType: 'Group',
      member: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      name: 'Bob\'s test group'
    }))

    model.attachVerbName([
      new VerbDisplayModel({ VerbID: 'http://example.com/verb/1', Display: 'verb', Lang: 'en-US' })
    ])

    model.attachObjectActivity(new ActivityModel({
      id: 'http://example.com/activity/1',
      definition: {
        name: {
          'en-US': 'activity'
        },
        description: {
          'en-US': 'activity'
        }
      }
    }))

    model.attachAuthority(new AgentModel({
      mbox: 'mailto:<EMAIL>'
    }))

    model.attachContextInstructor(new AgentModel({
      mbox: 'mailto:<EMAIL>'
    }))

    model.attachContextTeam(new AgentModel({
      mbox: 'mailto:<EMAIL>'
    }))

    const formatted = model.toJson(['en-US'], 'ids')

    expect(formatted.id).to.equal(statementId)
    expect(formatted.actor).to.exist
    expect(formatted.actor.mbox).to.equal('mailto:<EMAIL>')
    expect(formatted.actor.objectType).to.equal('Group')
    expect(formatted.actor.name).not.to.exist
    expect(formatted.actor.member).not.to.exist
    expect(formatted.verb).to.exist
    expect(formatted.verb.id).to.equal('http://example.com/verb/1')
    expect(formatted.verb.display).not.to.exist
    expect(formatted.object).to.exist
    expect(formatted.object.id).to.equal('http://example.com/activity/1')
    expect(formatted.object.objectType).not.to.exist
    expect(formatted.result).to.exist
    expect(formatted.result.completion).to.be.true
    expect(formatted.result.response).to.equal('passed')
    expect(formatted.result.score).to.exist
    expect(formatted.result.score.max).to.equal(100)
    expect(formatted.result.score.min).to.equal(10)
    expect(formatted.result.score.raw).to.equal(80)
    expect(formatted.result.score.scaled).to.equal(1)
    expect(formatted.result.success).to.be.true
    expect(formatted.version).to.equal('1.0.0')
    expect(formatted.context).to.exist
    expect(formatted.context.extensions).to.exist
    expect(formatted.context.extensions['http://example.com/extension/1']).to.exist
    expect(formatted.context.language).to.equal('en-US')
    expect(formatted.context.platform).to.equal('linux')
    expect(formatted.context.registration).to.equal(registration)
    expect(formatted.context.revision).to.equal('1.0.0')
    expect(formatted.context.statement).to.exist
    expect(formatted.context.statement.objectType).to.equal('StatementRef')
    expect(formatted.context.statement.id).to.equal(contextStatement)
    expect(formatted.context.instructor).to.exist
    expect(formatted.context.instructor.mbox).to.equal('mailto:<EMAIL>')
    expect(formatted.context.team).to.exist
    expect(formatted.context.team.mbox).to.equal('mailto:<EMAIL>')
    expect(formatted.id).to.equal(statementId)
  })

  it('returns the statement with the requested canonical data', () => {
    const statementId = uuid()
    const stored = (new Date()).toISOString()
    const registration = uuid()
    const contextStatement = uuid()

    const model = new Model(undefined, {
      ID: statementId,
      Stored: new Date(stored),
      Timestamp: new Date(stored),
      VerbID: 'http://example.com/verb/1',
      ObjectActivityID: 'http://example.com/activity/1',
      Raw: '{"id":"test-1"}',
      ResultCompletion: true,
      ResultResponse: 'passed',
      ResultScoreMax: 100,
      ResultScoreMin: 10,
      ResultScoreRaw: 80,
      ResultScoreScaled: 1,
      ResultSuccess: true,
      Version: '1.0.0',
      ContextExtensions: '{"http://example.com/extension/1":"pass"}',
      ContextLanguage: 'en-US',
      ContextPlatform: 'linux',
      ContextRegistration: registration,
      ContextRevision: '1.0.0',
      ContextStatement: contextStatement
    })

    const actor = new AgentModel({
      mbox: 'mailto:<EMAIL>',
      objectType: 'Group',
      name: 'Bob\'s test group'
    })
    actor.attachMembers([new AgentModel({
      mbox: 'mailto:<EMAIL>'
    })])
    model.attachActor(actor)

    model.attachVerbName([
      new VerbDisplayModel({ VerbID: 'http://example.com/verb/1', Display: 'verb', Lang: 'en-US' }),
      new VerbDisplayModel({ VerbID: 'http://example.com/verb/1', Display: 'verb', Lang: 'es-MX' })
    ])

    model.attachObjectAgent(new AgentModel({
      mbox: 'mailto:<EMAIL>'
    }))

    model.attachContextCategoryActivities([
      new ActivityModel({ id: 'http://example.com/activity/1', definition: { name: { 'en-US': 'activity', 'es-MX': 'activity' } } })
    ])
    model.attachContextOtherActivities([
      new ActivityModel({ id: 'http://example.com/activity/1', definition: { name: { 'en-US': 'activity', 'es-MX': 'activity' } } })
    ])
    model.attachContextGroupingActivities([
      new ActivityModel({ id: 'http://example.com/activity/1', definition: { name: { 'en-US': 'activity', 'es-MX': 'activity' } } })
    ])
    model.attachContextParentActivities([
      new ActivityModel({ id: 'http://example.com/activity/1', definition: { name: { 'en-US': 'activity', 'es-MX': 'activity' } } })
    ])

    const formatted = model.toJson(['es-MX'], 'canonical')

    expect(formatted.id).to.equal(statementId)
    expect(formatted.actor).to.exist
    expect(formatted.actor.mbox).to.equal('mailto:<EMAIL>')
    expect(formatted.actor.objectType).to.equal('Group')
    expect(formatted.actor.name).to.exist
    expect(formatted.actor.member).to.exist
    expect(formatted.verb).to.exist
    expect(formatted.verb.id).to.equal('http://example.com/verb/1')
    expect(formatted.verb.display).to.exist
    expect(formatted.verb.display['es-MX']).to.exist
    expect(formatted.verb.display['en-US']).not.to.exist
    expect(formatted.object).to.exist
    expect(formatted.object.mbox).to.equal('mailto:<EMAIL>')
    expect(formatted.object.objectType).not.to.exist
    expect(formatted.result).to.exist
    expect(formatted.result.completion).to.be.true
    expect(formatted.result.response).to.equal('passed')
    expect(formatted.result.score).to.exist
    expect(formatted.result.score.max).to.equal(100)
    expect(formatted.result.score.min).to.equal(10)
    expect(formatted.result.score.raw).to.equal(80)
    expect(formatted.result.score.scaled).to.equal(1)
    expect(formatted.result.success).to.be.true
    expect(formatted.version).to.equal('1.0.0')
    expect(formatted.context).to.exist
    expect(formatted.context.extensions).to.exist
    expect(formatted.context.extensions['http://example.com/extension/1']).to.exist
    expect(formatted.context.language).to.equal('en-US')
    expect(formatted.context.platform).to.equal('linux')
    expect(formatted.context.registration).to.equal(registration)
    expect(formatted.context.revision).to.equal('1.0.0')
    expect(formatted.context.statement).to.exist
    expect(formatted.context.statement.objectType).to.equal('StatementRef')
    expect(formatted.context.statement.id).to.equal(contextStatement)
    expect(formatted.id).to.equal(statementId)
    expect(formatted.context.contextActivities).to.exist
    expect(formatted.context.contextActivities.parent).to.exist
    expect(formatted.context.contextActivities.parent.length).to.equal(1)
    expect(formatted.context.contextActivities.parent[0].id).to.equal('http://example.com/activity/1')
    expect(formatted.context.contextActivities.parent[0].definition).to.exist
    expect(formatted.context.contextActivities.parent[0].definition.name).to.exist
    expect(formatted.context.contextActivities.parent[0].definition.name['es-MX']).to.exist
    expect(formatted.context.contextActivities.parent[0].definition.name['en-US']).not.to.exist
    expect(formatted.context.contextActivities.category).to.exist
    expect(formatted.context.contextActivities.category.length).to.equal(1)
    expect(formatted.context.contextActivities.category[0].id).to.equal('http://example.com/activity/1')
    expect(formatted.context.contextActivities.category[0].definition).to.exist
    expect(formatted.context.contextActivities.category[0].definition.name).to.exist
    expect(formatted.context.contextActivities.category[0].definition.name['es-MX']).to.exist
    expect(formatted.context.contextActivities.category[0].definition.name['en-US']).not.to.exist
    expect(formatted.context.contextActivities.grouping).to.exist
    expect(formatted.context.contextActivities.grouping.length).to.equal(1)
    expect(formatted.context.contextActivities.grouping[0].id).to.equal('http://example.com/activity/1')
    expect(formatted.context.contextActivities.grouping[0].definition).to.exist
    expect(formatted.context.contextActivities.grouping[0].definition.name).to.exist
    expect(formatted.context.contextActivities.grouping[0].definition.name['es-MX']).to.exist
    expect(formatted.context.contextActivities.grouping[0].definition.name['en-US']).not.to.exist
    expect(formatted.context.contextActivities.other).to.exist
    expect(formatted.context.contextActivities.other.length).to.equal(1)
    expect(formatted.context.contextActivities.other[0].id).to.equal('http://example.com/activity/1')
    expect(formatted.context.contextActivities.other[0].definition).to.exist
    expect(formatted.context.contextActivities.other[0].definition.name).to.exist
    expect(formatted.context.contextActivities.other[0].definition.name['es-MX']).to.exist
    expect(formatted.context.contextActivities.other[0].definition.name['en-US']).not.to.exist
  })
})
