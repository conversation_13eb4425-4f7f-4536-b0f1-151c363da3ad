import flushService from '../../../services/mssql/statements/flush.service.js'
import { Request, Response } from 'express'
import logger from '@lcs/logger'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.flush-db', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    await flushService()
    log('info', 'Successfully flushed database', { req, success: true })
    res.sendStatus(httpStatus.NO_CONTENT)
  } catch (error) {
    log('error', 'Failed to flush database', { errorMessage: getErrorMessage(error), success: false, req })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
