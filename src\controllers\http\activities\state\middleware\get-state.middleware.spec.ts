import { expect } from 'chai'
import Sinon from 'sinon'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import middleware from './get-state.middleware.js'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus

describe('Middleware validate get state request', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())


  it('should return bad request when unexpected parameter', () => {
    const mock = httpMocks.createMocks({ query: { agents: '{"mbox":"mailto:<EMAIL>"}' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('The get activity state request contained unexpected parameters: agents')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activityId is not present in the query params', () => {
    const mock = httpMocks.createMocks()
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Error -- activity_state - method = GET, but activityId parameter is missing.')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activityId is not a valid IRI', () => {
    const mock = httpMocks.createMocks({ query: { activityId: '(@website.com)' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('activityId with value (@website.com) was not a valid IRI')
    expect(next.called).to.be.false
  })

  it('should return bad request when the registration is not a valid UUID', () => {
    const mock = httpMocks.createMocks({
      query: {
        activityId: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
        agent: '{"mbox":"mailto:<EMAIL>"}',
        stateId: '1',
        registration: '06x75030-4e66-11ee-be56-0242ac120002'
      }
    })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('registration is not a valid uuid')
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent is not present in the query params', () => {
    const mock = httpMocks.createMocks({
      query: {
        activityId: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
        stateId: '1'
      }
    })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Error -- activity_state - method = GET, but agent parameter is missing.')
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent is not JSON', () => {
    const mock = httpMocks.createMocks({
      query: {
        activityId: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
        agent: 'mbox:"mailto:<EMAIL>"}',
        stateId: '1',
        registration: '06e75030-4e66-11ee-be56-0242ac120002'
      }
    })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('agent query param is not valid')
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent is not valid', () => {
    const mock = httpMocks.createMocks({
      query: {
        activityId: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
        agent: '{"mbix":"<EMAIL>"}',
        stateId: '1'
      }
    })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Invalid field(s) found in Agent/Group - mbix')
    expect(next.called).to.be.false
  })

  it('should return bad request when since parameter is a valid date', () => {
    const mock = httpMocks.createMocks({
      query: {
        activityId: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
        agent: '{"mbox":"mailto:<EMAIL>"}',
        stateId: '1',
        since: '08-07-2032T03'
      }
    })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Since parameter was not a valid RFC3339 timestamp')
    expect(next.called).to.be.false
  })

})
