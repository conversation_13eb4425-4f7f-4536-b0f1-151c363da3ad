import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivitySession, { TABLE_NAME as ActivityTable, ActivitySessionDatabaseRecord } from '../../../models/dashboard/activity-session.model.js'
import getActivityDefinitionDescription from './description/get.service.js'
import getActivityDefinitionName from './name/get.service.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'

export default async function getPaginatedCourseActivitiesWithSessionData(offset = 0, limit = 10, search?: string): Promise<{ totalRecords: number, activities: ActivitySession[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  let query = `
    WITH AttemptCounts AS (
      SELECT COUNT(DISTINCT [ContextRegistration]) AS [AttemptCount], [ObjectActivityID]
      FROM [${StatementTableName}]
      WHERE [VerbID] = 'https://adlnet.gov/expapi/verbs/initialized'
      GROUP BY [ObjectActivityID]
    )

    SELECT [${ActivityTable}].*,
           TotalRecords = COUNT(*) OVER(),
           COALESCE([AttemptCounts].[AttemptCount], 0) AS [Attempts]
    FROM [${ActivityTable}]
      LEFT JOIN [AttemptCounts] ON [AttemptCounts].[ObjectActivityID] = [${ActivityTable}].[ID]
    WHERE [ID] IN (
      SELECT DISTINCT [ObjectActivityID]
      FROM [${StatementTableName}]
      WHERE [VerbID] = 'https://adlnet.gov/expapi/verbs/initialized'
      AND [ContextRegistration] IS NOT NULL
      AND [ObjectActivityID] IS NOT NULL
    )
  `

  if (search) {
    // TODO: build search query
    // need to look at the id of the activity and
    // the name in the definition name if there is one for the given activity
    // the parse search term query will help... but it can only be used once per request
    // otherwise it will duplicate query params
  }

  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    ORDER BY [ID]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<ActivitySessionDatabaseRecord>(query)

  return {
    totalRecords: results.recordset.length > 0 && results.recordset[0].TotalRecords ? results.recordset[0].TotalRecords : 0,
    activities: await Promise.all(results.recordset.map(async record => {
      const activity = new ActivitySession(undefined, record)

      try {
        activity.attachDefinitionDescription(await getActivityDefinitionDescription(activity.fields.id))
      } catch (error) {
        if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
          throw error
        }
      }

      try {
        activity.attachDefinitionName(await getActivityDefinitionName(activity.fields.id))
      } catch (error) {
        if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
          throw error
        }
      }

      return activity
    }))
  }
}
