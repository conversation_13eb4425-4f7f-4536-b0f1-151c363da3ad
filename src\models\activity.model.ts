import { Table } from '@lcs/mssql-utility'
import ActivityDescriptionModel from './activity-descriptions.model.js'
import ActivityName from './activity-names.model.js'
import { Activity, ActivityFields, ActivityTableName } from '@tess-f/sql-tables/dist/lrs/activity.js'
import getLangMap from '../utils/get-lang-map.js'

interface InteractionComponent {
  id: string,
  description?: { [key: string]: string }
}

export interface ActivityJSON {
  id: string
  objectType?: string
  definition?: {
    name?: { [key: string]: string },
    description?: { [key: string]: string },
    type?: string,
    moreInfo?: string,
    extensions?: { [key: string]: any },
    interactionType?: string,
    correctResponsesPattern?: string[],
    choices?: InteractionComponent[],
    scale?: InteractionComponent[],
    source?: InteractionComponent[],
    target?: InteractionComponent[],
    steps?: InteractionComponent[]
  }
}

export default class ActivityModel extends Table<ActivityJSON, Activity> {
  fields: ActivityJSON

  constructor(fields?: ActivityJSON, record?: Activity) {
    super(ActivityTableName, [ActivityFields.ID])
    if (fields) {
      this.fields = fields
    } else {
      this.fields = { objectType: 'Activity', id: '' }
    }

    if (record) {
      this.importFromDatabase(record)
    }
  }

  importFromDatabase(record: Activity): void {
    this.fields.id = record.ID!
    this.fields.objectType = record.ObjectType
    if (record.DefinitionType ||
      record.DefinitionMoreInfo ||
      record.InteractionType ||
      record.CorrectResponsePattern ||
      record.Choice ||
      record.Scale ||
      record.Sources ||
      record.Targets ||
      record.Steps ||
      record.DefinitionExtension
    ) {
      this.fields.definition = {
        type: record.DefinitionType ? record.DefinitionType : undefined,
        moreInfo: record.DefinitionMoreInfo ? record.DefinitionMoreInfo : undefined,
        interactionType: record.InteractionType ? record.InteractionType : undefined,
        correctResponsesPattern: record.CorrectResponsePattern ? JSON.parse(record.CorrectResponsePattern) : undefined,
        choices: record.Choice ? JSON.parse(record.Choice) : undefined,
        scale: record.Scale ? JSON.parse(record.Scale) : undefined,
        source: record.Sources ? JSON.parse(record.Sources) : undefined,
        target: record.Targets ? JSON.parse(record.Targets) : undefined,
        steps: record.Steps ? JSON.parse(record.Steps) : undefined,
        extensions: record.DefinitionExtension ? JSON.parse(record.DefinitionExtension) : undefined
      }
    }
  }

  exportJsonToDatabase(): Activity {
    return {
      ID: this.fields.id,
      ObjectType: this.fields.objectType ? this.fields.objectType : 'Activity', // if object type is not present then it is an activity
      DefinitionType: this.fields.definition?.type || null,
      DefinitionMoreInfo: this.fields.definition?.moreInfo || null,
      InteractionType: this.fields.definition?.interactionType || null,
      CorrectResponsePattern: this.fields.definition?.correctResponsesPattern ? JSON.stringify(this.fields.definition.correctResponsesPattern) : null,
      Choice: this.fields.definition?.choices ? JSON.stringify(this.fields.definition.choices) : null,
      Scale: this.fields.definition?.scale ? JSON.stringify(this.fields.definition.scale) : null,
      Sources: this.fields.definition?.source ? JSON.stringify(this.fields.definition.source) : null,
      Targets: this.fields.definition?.target ? JSON.stringify(this.fields.definition.target) : null,
      Steps: this.fields.definition?.steps ? JSON.stringify(this.fields.definition.steps) : null,
      DefinitionExtension: this.fields.definition?.extensions ? JSON.stringify(this.fields.definition.extensions) : null
    }
  }

  attachDefinitionName(names: ActivityName[]): void {
    if (!this.fields.definition) {
      this.fields.definition = {}
    }
    this.fields.definition.name = names.reduce((a, v) => ({ ...a, [v.fields.Lang!]: v.fields.Display }), {})
  }

  attachDefinitionDescription(descriptions: ActivityDescriptionModel[]): void {
    if (!this.fields.definition) {
      this.fields.definition = {}
    }
    this.fields.definition.description = descriptions.reduce((a, v) => ({ ...a, [v.fields.Lang!]: v.fields.Display }), {})
  }

  getDefinitionName(): ActivityName[] {
    if (this.fields.definition?.name) {
      const ret: ActivityName[] = []
      for (const key in this.fields.definition.name) {
        ret.push(new ActivityName({
          ActivityID: this.fields.id,
          Lang: key,
          Display: this.fields.definition.name[key]
        }))
      }
      return ret
    }
    return []
  }

  getDefinitionDescription(): ActivityDescriptionModel[] {
    if (this.fields.definition?.description) {
      const ret: ActivityDescriptionModel[] = []
      for (const key in this.fields.definition.description) {
        ret.push(new ActivityDescriptionModel({
          ActivityID: this.fields.id,
          Lang: key,
          Display: this.fields.definition.description[key]
        }))
      }
      return ret
    }
    return []
  }

  public toJson(language?: string[], idsOnly = false): { [key: string]: any } {
    const ret: { [key: string]: any } = {
      id: this.fields.id
    }

    if (idsOnly) {
      return ret
    }

    if (!this.fields.objectType) {
      ret.objectType = 'Activity'
    } else {
      ret.objectType = this.fields.objectType
    }

    if (this.fields.definition) {
      ret.definition = {}
      if (this.getDefinitionName().length > 0) {
        ret.definition.name = getLangMap(this.getDefinitionName().map(lang => { return { Lang: lang.fields.Lang!, Display: lang.fields.Display! } }), language)
      }
      if (this.getDefinitionDescription().length > 0) {
        ret.definition.description = getLangMap(this.getDefinitionDescription().map(description => { return { Lang: description.fields.Lang!, Display: description.fields.Display! } }), language)
      }
      if (this.fields.definition.scale && this.fields.definition.scale.length > 0) {
        ret.definition.scale = this.fields.definition.scale.map(s => this.interactionToJson(s, language))
      }
      if (this.fields.definition.choices && this.fields.definition.choices.length > 0) {
        ret.definition.choices = this.fields.definition.choices.map(c => this.interactionToJson(c, language))
      }
      if (this.fields.definition.steps && this.fields.definition.steps.length > 0) {
        ret.definition.steps = this.fields.definition.steps.map(s => this.interactionToJson(s, language))
      }
      if (this.fields.definition.source && this.fields.definition.source.length > 0) {
        ret.definition.source = this.fields.definition.source.map(s => this.interactionToJson(s, language))
      }
      if (this.fields.definition.target && this.fields.definition.target.length > 0) {
        ret.definition.target = this.fields.definition.target.map(t => this.interactionToJson(t, language))
      }
      if (this.fields.definition.correctResponsesPattern) {
        ret.definition.correctResponsesPattern = this.fields.definition.correctResponsesPattern
      }
      if (this.fields.definition.extensions) {
        ret.definition.extensions = this.fields.definition.extensions
      }
      if (this.fields.definition.interactionType) {
        ret.definition.interactionType = this.fields.definition.interactionType
      }
      if (this.fields.definition.moreInfo) {
        ret.definition.moreInfo = this.fields.definition.moreInfo
      }
      if (this.fields.definition.type) {
        ret.definition.type = this.fields.definition.type
      }
    }

    return ret
  }

  private interactionToJson(component: InteractionComponent, language?: string[]): { [key: string]: any } {
    const ret: { [key: string]: any } = {
      id: component.id
    }
    if (component.description) {
      const langMap: { Lang: string, Display: string }[] = []
      for (const key in component.description) {
        langMap.push({
          Lang: key,
          Display: component.description[key]
        })
      }
      ret.description = getLangMap(langMap, language)
    }
    return ret
  }
}
