import { expect } from 'chai'
import logger from '@lcs/logger'
import controller from './get.controller.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, OK } = httpStatus
import httpMocks from 'node-mocks-http'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import Sinon from 'sinon'
import * as getActivityProfileService from '../../../../services/mssql/activity-profile/get.service.js'
import ActivityProfileModel from '../../../../models/activity-profile.model.js'

describe('HTTP Controller: get activity profile', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  xit('should return not found when the requested profileId does not exist', async () => {
    const mocks = httpMocks.createMocks({ query: { profileId: '-1', activityId: 'xxx' } })
    const stub = Sinon.stub(getActivityProfileService, "getActivityProfileService")
    stub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
    expect(mocks.res._getData()).to.contain('Activity profile not found')
  })

  xit('should return not found when profileId is not provided', async () => {
    const mocks = httpMocks.createMocks({ query: { activityId: 'xxx', since: '2032-08-07T03' } })
    const stub = Sinon.stub(getActivityProfileService, "getActivitiesProfileService")
    stub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
    expect(mocks.res._getData()).to.contain('Activities profile not found')
  })

  xit('should return Internal Server when service fails and profile id is provided', async () => {
    const mocks = httpMocks.createMocks({ query: { activityId: 'xxx', since: '2032-08-07T03', profileId: '1' } })
    const stub = Sinon.stub(getActivityProfileService, "getActivitiesProfileService")
    stub.returns(Promise.reject(new Error('Internal Error')))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })

  xit('should return Internal Server when service fails and profile id is not provided', async () => {
    const mocks = httpMocks.createMocks({ query: { activityId: 'xxx', since: '2032-08-07T03' } })
    const stub = Sinon.stub(getActivityProfileService, "getActivitiesProfileService")
    stub.returns(Promise.reject(new Error('Internal Error')))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })

  xit('Returns a json activity profile when profileId is provided', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        profileId: '1',
        activityId: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
      }
    })
    const stub = Sinon.stub(getActivityProfileService, 'getActivityProfileService')
    stub.returns(Promise.resolve(new ActivityProfileModel({
      ID: '2',
      ActivityID: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
      Profile: '{"test": 1234}',
      ModifiedOn: new Date(),
      Etag: 'test'
    })))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const data = JSON.parse(mocks.res._getData())
    expect(data).to.exist
  })


  xit('returns a list of activity profile ids for the given request', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        activityId: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
      }
    })
    const stub = Sinon.stub(getActivityProfileService, 'getActivitiesProfileService')
    stub.returns(Promise.resolve([
      new ActivityProfileModel({
        ID: '1'
      }),
      new ActivityProfileModel({
        ID: '2'
      })
    ]))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const ids: string[] = JSON.parse(mocks.res._getData())
    expect(ids).to.exist
    expect(ids.length).to.equal(2)
    expect(ids).to.contain('1')
    expect(ids).to.contain('2')
  })
})
