import { expect } from 'chai'
import validateAttachments from './validate.service.js'

describe('Validate Attachments', () => {
  it('throws an error when the object is not a list', () => {
    expect(validateAttachments.bind(validateAttachments, true))
      .to.throw('Attachments is not a properly formatted array')

    expect(validateAttachments.bind(validateAttachments, 'my attachment'))
      .to.throw('Attachments is not a properly formatted array')

    expect(validateAttachments.bind(validateAttachments, 30))
      .to.throw('Attachments is not a properly formatted array')

    expect(validateAttachments.bind(validateAttachments, false))
      .to.throw('Attachments is not a properly formatted array')
  })

  it('throws an error if the an attachment has an unallowed field', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'test',
      display: true,
      name: 'Bob'
    }]))
      .to.throw('Invalid field(s) found in Attachments - name')
  })

  it('throws an error if an attachment is missing a required field', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      display: true,
      contentType: 'image',
      length: 42
    }]))
      .to.throw('usageType is missing in Attachment')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'test',
      contentType: 'image',
      length: 42
    }]))
      .to.throw('display is missing in Attachment')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'test',
      display: true,
      length: 42
    }]))
      .to.throw('contentType is missing in Attachment')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'test',
      display: true,
      contentType: 'image'
    }]))
      .to.throw('length is missing in Attachment')
  })

  it('throws an error if an attachment usage type is invalid', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'example.com/usage',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42
    }]))
      .to.throw('Attachments usageType with value example.com/usage was not a valid IRI')
  })

  it('throws an error if the file url is not valid', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      fileUrl: 'example.com'
    }]))
      .to.throw('Attachments fileUrl with value example.com was not a valid IRI')
  })

  it('throws an error if the sha2 property is missing', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42
    }]))
      .to.throw('Attachment sha2 is required')
  })

  it('throws an error if the sha2 property is not a string', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: 39
    }]))
      .to.throw('Attachment sha2 must be a string')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: [39]
    }]))
      .to.throw('Attachment sha2 must be a string')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: { foo: 'bar' }
    }]))
      .to.throw('Attachment sha2 must be a string')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: true
    }]))
      .to.throw('Attachment sha2 must be a string')
  })

  it('throws an error when the sha2 is invalid', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://exmaple.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789'
    }]))
      .to.throw('Not a valid sha2 inside the statement')
  })

  it('throws an error when the length is not an integer', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 32.98,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
    }]))
      .to.throw('Attachment length must be an integer')
  })

  it('throws an error when the content type is not a string', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 20,
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
    }]))
      .to.throw('Attachment contentType must be a string')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: [20],
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
    }]))
      .to.throw('Attachment contentType must be a string')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: { foo: 'bar' },
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
    }]))
      .to.throw('Attachment contentType must be a string')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: true,
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
    }]))
      .to.throw('Attachment contentType must be a string')
  })

  it('throws an error when the display is not a dictionary', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: 'attachment',
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
    }]))
      .to.throw('Attachment display is not a properly formatted dictionary')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: 30,
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
    }]))
      .to.throw('Attachment display is not a properly formatted dictionary')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: [30],
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
    }]))
      .to.throw('Attachment display is not a properly formatted dictionary')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: true,
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
    }]))
      .to.throw('Attachment display is not a properly formatted dictionary')
  })

  it('throws an error when the display is not a proper lang map', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'mySuperFancyLang': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
    }]))
      .to.throw('language mySuperFancyLang is not valid in attachment display')
  })

  it('throws an error when description is not a dictionary', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789',
      description: 'attachment'
    }]))
      .to.throw('Attachment description is not a properly formatted dictionary')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789',
      description: true
    }]))
      .to.throw('Attachment description is not a properly formatted dictionary')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789',
      description: 39
    }]))
      .to.throw('Attachment description is not a properly formatted dictionary')

    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789',
      description: [39]
    }]))
      .to.throw('Attachment description is not a properly formatted dictionary')
  })

  it('throws an error when the description is not a valid lang map', () => {
    expect(validateAttachments.bind(validateAttachments, [{
      usageType: 'http://example.com/usage#type',
      display: {
        'en-US': 'attachment'
      },
      contentType: 'image',
      length: 42,
      sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789',
      description: {
        'mySuperFancyLang': 'attachment'
      }
    }]))
      .to.throw('language mySuperFancyLang is not valid in attachment description')
  })

  it('returns void for a valid attachment', () => {
    expect(validateAttachments([
      {
        usageType: 'http://example.com/usage#type',
        display: {
          'en-US': 'attachment'
        },
        contentType: 'application/octet-stream',
        length: 42,
        sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789',
        description: {
          'en-US': 'attachment'
        }
      },
      {
        usageType: 'http://example.com/usage#type',
        display: {
          'en-US': 'attachment'
        },
        contentType: 'application/octet-stream',
        length: 42,
        sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789',
        description: {
          'en-US': 'attachment'
        },
        fileUrl: 'http://example.com/1'
      },
      {
        usageType: 'http://example.com/usage#type',
        display: {
          'en-US': 'attachment'
        },
        contentType: 'application/octet-stream',
        length: 42,
        sha2: 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789'
      }
    ])).to.equal(void 0)
  })
})
