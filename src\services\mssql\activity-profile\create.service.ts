import mssql, { addRow } from '@lcs/mssql-utility'
import ActivityProfileModel from '../../../models/activity-profile.model.js'
import { ActivityProfile } from '@tess-f/sql-tables/dist/lrs/activity-profile.js'

export default async function createActivityProfileService(activityProfile: ActivityProfileModel): Promise<ActivityProfileModel> {
  const pool = mssql.getPool()
  const created = await addRow<ActivityProfile>(pool.request(), activityProfile)
  const createdActivityProfile = new ActivityProfileModel(created)

  return createdActivityProfile
}
