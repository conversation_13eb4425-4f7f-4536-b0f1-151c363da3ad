import mssql, { addRow } from '@lcs/mssql-utility'
import ActivityModel from '../../../models/activity.model.js'
import { Activity } from '@tess-f/sql-tables/dist/lrs/activity.js'
import { createActivityDefinitionDescription } from './description/create.service.js'
import createActivityDefinitionName from './name/create.service.js'

export default async function createActivityService(activity: ActivityModel): Promise<ActivityModel> {
  const pool = mssql.getPool()
  const created = await addRow<Activity>(pool.request(), activity)
  const createdActivity = new ActivityModel(undefined, created)

  // save the definition name if it has any
  if (activity.getDefinitionName().length > 0) {
    createdActivity.attachDefinitionName(await Promise.all(activity.getDefinitionName().map(async name => await createActivityDefinitionName(name))))
  }

  // save the definition description if it has any
  if (activity.getDefinitionDescription().length > 0) {
    createdActivity.attachDefinitionDescription(await Promise.all(activity.getDefinitionDescription().map(async description => await createActivityDefinitionDescription(description))))
  }

  return createdActivity
}
