import { Table } from '@lcs/mssql-utility'
import { Attachment, AttachmentFields, AttachmentsTableName } from '@tess-f/sql-tables/dist/lrs/attachments.js'

export interface StatementAttachmentJson {
  usageType?: string
  display?: { [key: string]: string }
  description?: { [key: string]: string }
  contentType?: string
  length?: number
  sha2?: string
  fileUrl?: string
}

export default class StatementAttachment extends Table<Attachment, Attachment> {
  fields: Attachment

  constructor(fields: Attachment) {
    super(AttachmentsTableName, [
      AttachmentFields.ContentType,
      AttachmentFields.Length,
      AttachmentFields.Sha2,
      AttachmentFields.StatementId,
      AttachmentFields.UsageType
    ])
    this.fields = fields
  }

  importFromDatabase(record: Attachment): void {
    this.fields = record
  }

  exportJsonToDatabase(): Attachment {
    // DB will create the id
    return this.fields
  }
}
