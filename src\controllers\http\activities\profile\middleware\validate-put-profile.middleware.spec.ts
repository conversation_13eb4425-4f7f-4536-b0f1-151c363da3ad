import { expect } from 'chai'
import Sinon from 'sinon'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import middleware from './validate-put-profile.middleware.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, OK } = httpStatus

describe('Middleware: Validate PUT activity profile request', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  it('should return bad request when a query param other than (activityId, profileId) is provided', () => {
    const mock = httpMocks.createMocks({ query: { foo: 'bar', baz: 'baz' } })
    const mock2 = httpMocks.createMocks({ query: { activityId: 'test', profileId: 'test', foo: 'bar' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('The put activity profile request contained unexpected parameters: foo, baz')
    expect(next.called).to.be.false

    middleware(mock2.req, mock2.res, next)
    expect(mock2.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock2.res._getData()).to.contain('The put activity profile request contained unexpected parameters: foo')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activity id is missing', () => {
    const mock = httpMocks.createMocks()
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Missing activityId parameter in the request')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activity id is malformed', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'test' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should return bad request when the profile id is missing', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Missing profileId parameter in the request')
    expect(next.called).to.be.false
  })

  it('should return bad request when the content type is application/json and the body is empty', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', profileId: '1', agent: '{"mbox": "mailto:<EMAIL>"}' }, body: {}, headers: { 'content-type': 'application/json' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should call next when the request is valid', () => {
    const mock = httpMocks.createMocks({
      query: {
        activityId: 'http:example.com/activities/1',
        profileId: '1'
      },
      headers: { 'content-type': 'application/json', 'if-none-match': '*' },
      body: { foo: 'bar' }
    })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(OK)
    expect(next.called).to.be.true
  })
})