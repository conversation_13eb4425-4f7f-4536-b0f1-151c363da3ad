import mssql, { getRows } from '@lcs/mssql-utility'
import StatementModel from '../../../models/statement.model.js'
import { Statement, StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import buildStatementService from './build.service.js'

export default async function (statementId: string, attachments = false, voided?: boolean): Promise<StatementModel> {
  const identityFields: Partial<Statement> = { ID: statementId }
  if (voided !== undefined) {
    identityFields.Voided = voided
  }
  const pool = mssql.getPool()
  const records = await getRows<Statement>(StatementTableName, pool.request(), identityFields)
  const statement = new StatementModel(undefined, records[0])
  await buildStatementService(statement, attachments)

  return statement
}
