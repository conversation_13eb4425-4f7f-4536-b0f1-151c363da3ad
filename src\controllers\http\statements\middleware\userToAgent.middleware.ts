import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus
import { Request, Response, NextFunction } from 'express'
import getUserById from '../../../../services/mssql/user/get.service.js'
import settings from '../../../../config/settings.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('Middleware.user-to-agent', httpLogTransformer)

export default async function userToAgent(req: Request, res: Response, next: NextFunction) {
  try {
    const host = req.headers.host || settings.server.address
    const user = await getUserById(req.session.userId)
    req.authority = {
      objectType: 'Agent',
      name: `${user.FirstName} ${user.LastName}`,
      account: { name: user.Username!, homePage: `${req.protocol}://${host}` }
    }
    next()
  } catch (error) {
    log('error', 'Failed to map user to authority agent', { errorMessage: getErrorMessage(error), success: false, req })
    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}