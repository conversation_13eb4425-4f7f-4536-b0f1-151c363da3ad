import { expect } from 'chai'
import logger from '@lcs/logger'
import controller from './get.controller.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, OK } = httpStatus
import httpMocks from 'node-mocks-http'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import Sinon from 'sinon'
import * as getAgentProfileService from '../../../../services/mssql/agent/profile/get.service.js'
import * as getOrCreateAgent from '../../../../services/mssql/agent/get-or-create.service.js'
import AgentModel from '../../../../models/agents.model.js'
// import AgentProfileModel from '../../../../models/agent-profile.model.js'

xdescribe('HTTP Controller: get agent state profile', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  beforeEach(() => {
    const stub = Sinon.stub(getOrCreateAgent, "default")
    stub.returns(Promise.resolve(new AgentModel(undefined, { ID: 'test' })))
  })

  it('should return not found when the requested profileID does not exist', async () => {
    const mocks = httpMocks.createMocks({ query: { profileId: '-1', agent: '{}' } })
    const stub = Sinon.stub(getAgentProfileService, 'getAgentProfileService')
    stub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
    expect(mocks.res._getData()).to.contain('Agent profile not found')
  })

  it('should return not found when profileId is not provided', async () => {
    const mocks = httpMocks.createMocks({ query: { agent: '{"mbox": "mailto:<EMAIL>"}' } })
    const stub = Sinon.stub(getAgentProfileService, 'getAgentProfilesService')
    stub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
    expect(mocks.res._getData()).to.contain('Agent profile not found')
  })
})
