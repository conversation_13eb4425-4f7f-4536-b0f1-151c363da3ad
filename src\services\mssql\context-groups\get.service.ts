import mssql, { getRows } from '@lcs/mssql-utility'
import ContextGroupModel from '../../../models/context-group.model.js'
import { ContextGroups, ContextGroupsTableName } from '@tess-f/sql-tables/dist/lrs/context-groups.js'
import logger from '@lcs/logger'
import getContextGroupRelevantTypes from './context-groups-relevant-types/get-relevant-types-for-context-group.service.js'
import getAgentByID from '../agent/get-by-id.service.js'

const log = logger.create('Service-MSSQL.get-context-groups')

export default async function getContextGroupsForStatement(statementId: string): Promise<ContextGroupModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<ContextGroups>(ContextGroupsTableName, pool.request(), { StatementID: statementId })
  
  log('info', 'Successfully retrieved context groups', { statementId, success: true })

  const contextGroups = await Promise.all(records.map(async(record) => {
    const contextGroup = new ContextGroupModel(undefined, record)
    contextGroup.attachGroup(await getAgentByID(record.GroupID!))
    contextGroup.attachRelevantTypes(await getContextGroupRelevantTypes(record.ID!))
    return contextGroup
  }))

  return contextGroups
}