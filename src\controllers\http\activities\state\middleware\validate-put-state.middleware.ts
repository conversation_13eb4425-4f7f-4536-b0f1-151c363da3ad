import logger from '@lcs/logger'
import { NextFunction, Request, Response } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import { validateIri, validateUUID } from '../../../../../utils/validate-data-type.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import validateAgent from '../../../../../services/validators/agent/validate.service.js'

const log = logger.create('HTTP-Middleware:put-activity-state-validator', httpLogTransformer)

const validParams = ['activityId', 'agent', 'stateId', 'registration']

export default async function validatePutStatementRequest(req: Request, res: Response, next: NextFunction) {
  const rogueParams = Object.keys(req.query).filter(key => !validParams.includes(key))
  if (rogueParams.length > 0) {
    log('warn', 'Failed to put activity state: unallowed query params', { rogueParams, success: false, req })
    res.status(BAD_REQUEST).send(`The put activity state request contained unexpected parameters: ${rogueParams.join(', ')}`)
    return
  }

  if (!req.query.activityId) {
    log('warn', 'Failed to put activity state: missing activityId', { success: false, req })
    res.status(BAD_REQUEST).send('Missing activityId parameter in the request')
    return
  }


  try {
    validateIri(req.query.activityId.toString(), 'activityId query param for activity state')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'Failed to put activity state: malformed activity id', { errorMessage, success: false, req })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  if (!req.query.stateId) {
    log('warn', 'Failed to put activity state: missing stateId', { success: false, req })
    res.status(BAD_REQUEST).send('Missing stateId parameter in the request')
    return
  }

  if (req.query.registration) {
    try {
      validateUUID(req.query.registration, 'registration param for activity state')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      log('warn', 'Failed to put activity state: malformed registration id', { errorMessage, success: false, req })
      res.status(BAD_REQUEST).send(errorMessage)
      return
    }
  }

  if (!req.query.agent) {
    log('warn', 'Failed to put activity state: missing agent param', { success: false, req })
    res.status(BAD_REQUEST).send('Missing agent parameter in the request')
    return
  }

  let agent: any
  try {
    agent = JSON.parse(req.query.agent.toString())
  } catch (error) {
    log('warn', 'Failed to put activity state: malformed agent query parma, failed to parse as JSON', { success: false, req })
    res.status(BAD_REQUEST).send(`agent query parameter is not valid`)
    return
  }

  try {
    validateAgent(agent, 'Agent parameter')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'Failed to put activity state: malformed agent', { success: false, errorMessage, req })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  // check json body for application/json content
  if (req.headers['content-type'] === 'application/json' && Object.keys(req.body).length <= 0) {
    log('warn', 'Failed to put activity state: missing request body', { success: false, req })
    res.status(BAD_REQUEST).send('Could not find the state')
    return
  }

  // TODO: extra validation for oauth (if present)

  // go to the next function in the chain
  next()
}