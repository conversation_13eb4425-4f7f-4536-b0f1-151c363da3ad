import { Request, Response, NextFunction } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
import { validateIri } from '../../../../../utils/validate-data-type.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import { getActivityProfileService } from '../../../../../services/mssql/activity-profile/get.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'

const log = logger.create('HTTP-Middleware.Get-Activity-Profile', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction) {
  const validParams = ['activityId', 'profileId']
  const badParams: Array<string> = []
  Object.keys(req.query).forEach(key => {
    if (!validParams.includes(key)) {
      badParams.push(key)
    }
  })

  if (badParams.length > 0) {
    log('warn', 'Failed to parse request: unexpected parameter', { success: false, badParams })
    res.status(BAD_REQUEST).send(`The post activity profile request contained unexpected parameters: ${badParams.join(', ')}`)
    return
  }

  if (req.query.activityId) {
    try {
      validateIri(req.query.activityId.toString(), 'activityId')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      log('warn', 'invalid activityId iri: ', { success: false, errorMessage })
      res.status(BAD_REQUEST).send(errorMessage)
      return
    }
  } else {
    log('warn', 'Failed to parse request: activityId parameter is missing', { success: false })
    res.status(BAD_REQUEST).send('Error -- activity_profile - method = POST, but activityId parameter is missing.')
    return
  }

  if (!req.query.profileId) {
    log('warn', 'Failed to parse request: profileId parameter is missing', { success: false })
    res.status(BAD_REQUEST).send('Error -- activity_profile - method = POST, but profileId parameter is missing.')
    return
  }

  // Extra validation if oauth, req_validate.py line 383


  // Check json body for incoming POSTed document has properties
  if (req.headers['content-type'] === 'application/json' && Object.keys(req.body).length <= 0) {
    log('warn', 'Failed to parse request: Content-Type is application/json but empty', { success: false })
    res.status(BAD_REQUEST).send('Activity profile document to be posted is an empty JSON')
    return
  }

  // content-type must be application/json 
  if (req.headers['content-type'] !== 'application/json') {
    log('warn', 'Failed to parse request: Content-Type is not application/json', { success: false })
    res.status(BAD_REQUEST).send('Activity profile Content-Type is not application/json')
    return
  }

  // Check the content type if the document already exists
  try {
    const activityProfile = await getActivityProfileService(req.query.profileId!.toString(), req.query.activityId!.toString())
    if (activityProfile && activityProfile.fields.ContentType !== req.headers['content-type']) {
      log('warn', 'Failed to update activity profile: Content-Type is different from existing document', { success: false })
      res.status(BAD_REQUEST).send('Activity profile already exists and Content-Type is not JSON, cannot update it with new JSON document')
      return
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage !== dbErrors.default.NOT_FOUND_IN_DB) {
      log('error', 'Failed to check activity profile existence', { errorMessage, success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
      return
    }
  }

  // go to next function in the chain
  next()
}