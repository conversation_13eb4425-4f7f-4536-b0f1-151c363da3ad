import { Duration } from 'moment'

export default function formatDuration (duration: Duration): string {
  const parts: string[] = []

  if (!duration || !duration.isValid()) {
    return duration.humanize()
  }

  if (duration.years() >= 1) {
    const years = Math.floor(duration.years())
    parts.push(`${years} ${years > 1 ? 'years ' : 'year '}`)
  }

  if (duration.months() >= 1) {
    const months = Math.floor(duration.months())
    parts.push(`${months} ${months > 1 ? 'months ' : 'month '}`)
  }

  if (duration.days() >= 1) {
    const days = Math.floor(duration.days())
    parts.push(`${days} ${days > 1 ? 'days ' : 'day '}`)
  }

  if (duration.hours() >= 1) {
    const hours = Math.floor(duration.hours())
    parts.push(`${hours.toString().padStart(2, '0')}:`)
  } else {
    parts.push('00:')
  }

  if (duration.minutes() >= 1) {
    const minutes = Math.floor(duration.minutes())
    parts.push(`${minutes.toString().padStart(2, '0')}:`)
  } else {
    parts.push('00:')
  }

  if (duration.seconds() >= 1) {
    const seconds = Math.floor(duration.seconds())
    parts.push(`${seconds.toString().padStart(2, '0')}`)
  } else {
    parts.push('00')
  }

  if (duration.milliseconds() >= 1) {
    const milliseconds = Math.floor(duration.milliseconds())
    parts.push(`.${milliseconds}`)
  }

  return parts.join('')
}
