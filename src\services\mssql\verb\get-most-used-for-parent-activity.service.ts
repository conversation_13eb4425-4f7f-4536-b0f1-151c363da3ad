import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import VerbModel from '../../../models/verb.model.js'
import { Verb, VerbTableName } from '@tess-f/sql-tables/dist/lrs/verb.js'
import getVerbDisplay from './display/get.service.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { ParentContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/parent-context-activity.js'

export default async function getMostUsedVerbsForParentActivity(parentActivityID: string, limit = 3): Promise<VerbModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('activity', parentActivityID)

  const query = `
    WITH TopVerbs AS (
      SELECT TOP ${limit} COUNT([ID]) AS [UseCount], [VerbID]
      FROM [${StatementTableName}]
      WHERE [ID] IN (
        SELECT [StatementID]
        FROM [${ParentContextActivityTableName}]
        WHERE [ActivityID] = @activity
      )
      GROUP BY [VerbID]
      ORDER BY [UseCount] DESC
    )
    SELECT *
    FROM [${VerbTableName}]
    WHERE [ID] IN (
      SELECT [VerbID]
      FROM [TopVerbs]
    )
  `

  const results = await request.query<Verb>(query)

  if (results.recordset.length > 0) {
    return Promise.all(results.recordset.map(async record => {
      const verb = new VerbModel(undefined, record)

      try {
        verb.attachDisplay(await getVerbDisplay(verb.fields.id))
      } catch (error) {
        if ((error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) || !(error instanceof Error)) {
          throw error
        }
      }

      return verb
    }))
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
