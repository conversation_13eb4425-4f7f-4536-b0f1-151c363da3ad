import { expect } from 'chai'
import logger from '@lcs/logger'
import controller from './get-agents.controller.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, OK } = httpStatus
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import * as getAgentService from '../../../services/mssql/agent/get.service.js'
import AgentModel from '../../../models/agents.model.js'

describe('HTTP Controller: get agents', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  xit('should return internal server error when there is a problem with the internal service', async () => {
    const mocks = httpMocks.createMocks({ query: { agent: '{"account": {"homePage": "example.com", "name": "user"}}' } })
    const stub = Sinon.stub(getAgentService, 'default')
    stub.returns(Promise.reject(new Error('Service Error')))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })

  xit('returns a person object', async () => {
    const mocks = httpMocks.createMocks({ query: { agent: '{"mbox_sha1sum": "sha1sum", "openid": "user:openid"}' } })
    const stub = Sinon.stub(getAgentService, 'default')
    stub.returns(Promise.resolve(new AgentModel(undefined, { MboxSHA1Sum: 'sha1sum', OpenID: 'user:openid' })))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const data = JSON.parse(mocks.res._getData())
    expect(data).to.exist
    expect(data.objectType).to.equal('Person')
  })
})
