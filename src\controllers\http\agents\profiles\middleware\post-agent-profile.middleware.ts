import { Request, Response, NextFunction } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
import getOrCreateAgent from '../../../../../services/mssql/agent/get-or-create.service.js'
import { getAgentProfileService } from '../../../../../services/mssql/agent/profile/get.service.js'
import AgentModel from '../../../../../models/agents.model.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'

const log = logger.create('HTTP-Controller.Post-Agent-Profile', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction) {
  const validParams = ['agent', 'profileId', 'since']
  const badParams: Array<string> = []
  Object.keys(req.query).forEach(key => {
    if (!validParams.includes(key)) {
      badParams.push(key)
    }
  })

  if (badParams.length > 0) {
    log('warn', 'Failed to parse request: unexpected parameter', { success: false, badParams, req })
    res.status(BAD_REQUEST).send(`The post agent profile request contained unexpected parameters: ${badParams.join(', ')}`)
    return
  }

  if (!req.query.profileId) {
    log('warn', 'Failed to parse request: profileId parameter is missing', { success: false, req })
    res.status(BAD_REQUEST).send(`Error -- agent_profile - method = POST, but profileId parameter is missing.`)
    return
  }

  if (req.query.agent) {
    let agent: any
    try {
      agent = JSON.parse(req.query.agent.toString())
    } catch (error) {
      log('warn', 'Failed to get agents: malformed agent query param, failed to parse as JSON', { success: false, req })
      res.status(BAD_REQUEST).send('Agent query param is not valid')
      return
    }

  } else {
    log('warn', 'Failed to parse request: agent parameter is missing', { success: false, req })
    res.status(BAD_REQUEST).send('Error -- agent_profile - method = POST, but agent parameter is missing.')
    return
  }

  // Extra validation if oauth, req_validate.py line 383


  // Check json body for incoming POSTed document has properties
  if (req.headers['content-type'] === 'application/json' && Object.keys(req.body).length <= 0) {
    log('warn', 'Failed to parse request: Content-Type is application/json but empty', { success: false, req })
    res.status(BAD_REQUEST).send('Agent profile document to be posted is an empty JSON')
    return
  }

  // content-type header must be application/json 
  if (req.headers['content-type'] !== 'application/json') {
    log('warn', 'Failed to parse request: Content-Type is not application/json', { success: false, req })
    res.status(BAD_REQUEST).send('Agent profile Content-Type is not application/json')
    return
  }

  // Check the content type if the document already exists
  const agent = await getOrCreateAgent(new AgentModel(JSON.parse(req.query.agent!.toString())))
  try {
    const agentProfile = await getAgentProfileService(req.query.profileId!.toString(), agent.ID)
    if (agentProfile && agentProfile.fields.ContentType !== req.headers['content-type']) {
      log('warn', 'Failed to update agent profile: Content-Type is different from existing document', { success: false, req })
      res.status(BAD_REQUEST).send('Agent profile already exists and Content-Type is not JSON, cannot update it with new JSON document')
      return
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage !== dbErrors.default.NOT_FOUND_IN_DB) {
      log('error', 'Failed to check agent profile existence', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
      return
    }
  }

  // go to next function in the chain
  next()
}