import { expect } from 'chai'
import logger from '@lcs/logger'
import controller from './get.controller.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, OK } = httpStatus
import httpMocks from 'node-mocks-http'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import Sinon from 'sinon'
import * as getActivityService from '../../../services/mssql/activities/get.service.js'
import { v4 as uuid } from 'uuid'
import ActivityModel from '../../../models/activity.model.js'

describe('HTTP Controller: get activities', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  xit('should return not found when the requested activityId does not exist', async () => {
    const activity_id = uuid()
    const mocks = httpMocks.createMocks({ query: { activityId: activity_id } })
    const stub = Sinon.stub(getActivityService, 'default')
    stub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))
    await controller(mocks.req, mocks.res)
    const data = JSON.parse(mocks.res._getData())
    expect(data.id).to.eq(activity_id)
    expect(data.objectType).to.eq("Activity")
  })

  xit('should return internal server error when there is a problem with the internal service', async () => {
    const mocks = httpMocks.createMocks({ query: { activityId: uuid() } })
    const stub = Sinon.stub(getActivityService, 'default')
    stub.returns(Promise.reject(new Error('Service Error')))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })

  xit('returns a activity object', async () => {
    const activity_id = uuid()
    const mocks = httpMocks.createMocks({ query: { activityId: activity_id } })
    const stub = Sinon.stub(getActivityService, 'default')
    stub.returns(Promise.resolve(new ActivityModel(undefined, { ID: activity_id })))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const data = JSON.parse(mocks.res._getData())
    expect(data).to.exist
  })
})
