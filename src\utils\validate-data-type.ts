import { validate } from 'uuid'
import { validateIri as iriValidate, IriValidationStrategy } from 'validate-iri'

export function checkIfDict(obj: any, field: string): void {
  // checks if object is dict (line 190)
  if (typeof obj === 'number' || typeof obj === 'string' || typeof obj === 'boolean' || Array.isArray(obj)) {
    throw new Error(`${field} is not a properly formatted dictionary`)
  }
}

export function checkIfList(obj: any, field: string): void {
  // checks if object is an array (line 196)
  if (!Array.isArray(obj)) {
    throw new Error(`${field} is not a properly formatted array`)
  }
}

export function validateDictValues(values: any, field: string): void {
  // validate that all values are not null or undefined (line 147)
  Object.values(values).forEach(value => {
    if (value === null || value === undefined) {
      throw new Error(`${field} contains a null value`)
    }
  })
}

export function validateIri(iriValue: any, field: string): void {
  // validate an IRI (line 166)
  // use package https://www.npmjs.com/package/validate-iri
  if (typeof iriValue === 'string') {
    try {
      const isValid = iriValidate(iriValue, IriValidationStrategy.Pragmatic)
      if (isValid instanceof Error) {
        throw isValid
      }
    } catch {
      throw new Error(`${field} with value ${iriValue} was not a valid IRI`)
    }
  }
  else {
    throw new Error(`${field} must be a string type`)
  }
}

export function validateUUID(uuid: any, field: string): void {
  if (typeof uuid === 'string' && !validate(uuid)) {
    throw new Error(`${field} is not a valid uuid`)
  } else if (typeof uuid !== 'string') {
    throw new Error(`${field} must be a string type`)
  }
}

export function checkAllowedFields(allowed: string[], obj: any, objName: string): void {
  // checks if an object has fields that are NOT allowed (line 201)
  const failedList: Array<string> = []
  Object.keys(obj).forEach((element: string) => {
    if (!allowed.includes(element)) {
      failedList.push(element)
    }
  });

  if (failedList.length > 0) {
    throw new Error(`Invalid field(s) found in ${objName} - ${failedList.join(', ')}`)
  }
}

export function checkRequiredFields(required: string[], obj: any, objName: string): void {
  // checks if an object has fields that are required (line 209)
  required.forEach((requiredElement: string) => {
    if (!Object.keys(obj).includes(requiredElement)) {
      throw new Error(`${requiredElement} is missing in ${objName}`)
    }
  });
}
