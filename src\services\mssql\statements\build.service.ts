import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import StatementModel from '../../../models/statement.model.js'
import getActivityService from '../activities/get.service.js'
import getAgentService from '../agent/get.service.js'
import getVerbDisplay from '../verb/display/get.service.js'
import getContextActivities from '../activities/context/get-activities.service.js'
import getSubStatement from '../sub-statements/get.service.js'
import { CategoryContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/category-context-activity.js'
import { GroupingContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/grouping-context-activity.js'
import { OtherContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/other-context-activity.js'
import { ParentContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/parent-context-activity.js'
import getStatementAttachments from '../attachments/get-for-statement.service.js'
import getAgentByID from '../agent/get-by-id.service.js'
import getContextAgentsForStatement from '../context-agents/get.service.js'
import getContextGroupsForStatement from '../context-groups/get.service.js'
/**
 * Adds all of the reference data to rebuild an xAPI statement
 * @param statement the statement that we need to build
 */
export default async function buildStatementService(statement: StatementModel, attachments = false) {
  // Attach the actor
  statement.attachActor(await getAgentService({ ID: statement.actorID }))

  // Attach the object
  // NOTE: if object is a statement ref the statement class will handle it
  if (statement.objectActivityID) {
    // object is an activity
    statement.attachObjectActivity(await getActivityService(statement.objectActivityID))
  } else if (statement.objectAgentID) {
    // object is an agent
    statement.attachObjectAgent(await getAgentService({ ID: statement.objectAgentID }))
  } else if (statement.objectSubStatementID) {
    // object is a sub statement
    statement.attachObjectSubStatement(await getSubStatement(statement.objectSubStatementID))
  }

  // Attach the verb name
  try {
    statement.attachVerbName(await getVerbDisplay(statement.fields.verb!.id))
  } catch (error) {
    // if no ver displays move on
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  // Attach context instructor
  if (statement.contextInstructorID) {
    statement.attachContextInstructor(await getAgentService({ ID: statement.contextInstructorID }))
  }

  //attach context agents
  try {
    statement.attachContextAgents(await getContextAgentsForStatement(statement.fields.id!))
  } catch(error) {
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  //attach context groups
  try {
    statement.attachContextGroups(await getContextGroupsForStatement(statement.fields.id!))
  } catch(error) {
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  // Attach authority
  if (statement.authorityID) {
    statement.attachAuthority(await getAgentByID(statement.authorityID))
  }

  // Attach context team
  if (statement.contextTeamID) {
    statement.attachContextTeam(await getAgentService({ ID: statement.contextTeamID }))
  }

  // Attach context activities
  try {
    // parent
    statement.attachContextParentActivities(await getContextActivities(statement.fields.id!, ParentContextActivityTableName))
  } catch (error) {
    // if not found move on
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  try {
    // category
    statement.attachContextCategoryActivities(await getContextActivities(statement.fields.id!, CategoryContextActivityTableName))
  } catch (error) {
    // if not found move on
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  try {
    // grouping
    statement.attachContextGroupingActivities(await getContextActivities(statement.fields.id!, GroupingContextActivityTableName))
  } catch (error) {
    // if not found move on
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  try {
    // other
    statement.attachContextOtherActivities(await getContextActivities(statement.fields.id!, OtherContextActivityTableName))
  } catch (error) {
    // if not found move on
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  if (attachments) {
    // Attachment metadata
    try {
      statement.attachAttachmentMetadata(await getStatementAttachments(statement.fields.id!))
    } catch (error) {
      // if not found move on
      if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
        throw error
      }
    }
  }
}
