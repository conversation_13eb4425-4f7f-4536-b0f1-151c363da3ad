import mssql, { updateRow } from '@lcs/mssql-utility'
import AgentProfileModel from '../../../../models/agent-profile.model.js'
import { AgentProfile } from '@tess-f/sql-tables/dist/lrs/agent-profiles.js'

export default async function updateAgentProfileService(agentProfile: AgentProfileModel): Promise<AgentProfileModel> {
  const identityFields: Partial<AgentProfile> = {
    ProfileID: agentProfile.fields.ProfileID
  }

  const updated = await updateRow<AgentProfile>(mssql.getPool().request(), agentProfile, identityFields)
  return new AgentProfileModel(updated[0])
}
