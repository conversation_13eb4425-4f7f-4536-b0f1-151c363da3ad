import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { AgentTableName } from '@tess-f/sql-tables/dist/lrs/agent.js'

export default async function getUniqueAgentCountService(): Promise<number> {
  const pool = mssql.getPool()
  const result = await pool.request().query<{ TotalAgents: number }>(`SELECT COUNT (*) AS TotalAgents FROM [${AgentTableName}]`)
  if (result.recordset.length > 0) {
    return result.recordset[0].TotalAgents
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
