import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import logger from '@lcs/logger'
import { httpLogTransformer } from '@tess-f/backend-utils'
import getMostUsedActivities from '../../../../services/mssql/activities/get-most-used.service.js'
import Activity from '../../../../models/activity.model.js'
import getMostUsedActivitiesForParent from '../../../../services/mssql/activities/get-most-used-for-parent-activity.service.js'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Most-Used-Activity', httpLogTransformer)

export default async function getMostUsedActivitiesController(req: Request, res: Response) {
  try {
    let activities: Activity[]
    if (req.query && req.query.activity) {
      activities = await getMostUsedActivitiesForParent(req.query.activity.toString())
    } else {
      activities = await getMostUsedActivities()
    }
    log('info', 'Successfully retrieved the most used activities', { req, count: activities.length, success: true })
    res.json(activities.map(activity => activity.fields))
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get most used activities because none were found in the database', { req, success: false })
      res.status(httpStatus.NOT_FOUND).send('No activities found')
    } else {
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
