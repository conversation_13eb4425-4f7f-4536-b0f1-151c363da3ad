import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import AgentModel from '../../../models/agents.model.js'
import { Agent, AgentTableName } from '@tess-f/sql-tables/dist/lrs/agent.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'

/**
 * Returns the Agent with the least activities (sessions/context registrations)
 *  Number of unique registrations an agent has
 */
export default async function getAgentWithLeastActivitiesService() {
  const pool = mssql.getPool()
  const query = `
    WITH LeastActiveAgent AS (
      SELECT TOP 1 [ActorID], COUNT(DISTINCT [ContextRegistration]) AS [Sessions]
      FROM [${StatementTableName}]
      GROUP BY [ActorID]
      ORDER BY [Sessions] ASC
    )
    SELECT *
    FROM [${AgentTableName}]
    WHERE [ID] IN (
      SELECT [ActorID]
      FROM [LeastActiveAgent]
    )
  `

  const results = await pool.request().query<Agent>(query)

  if (results.recordset.length === 1) {
    return new AgentModel(undefined, results.recordset[0])
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
