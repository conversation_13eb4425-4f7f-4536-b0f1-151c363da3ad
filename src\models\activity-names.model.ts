import { Table } from '@lcs/mssql-utility'
import { ActivityName, ActivityNameFields, ActivityNameTableName } from '@tess-f/sql-tables/dist/lrs/activity-name.js'

export default class ActivityNameModel extends Table<ActivityName, ActivityName> {
  fields: ActivityName

  constructor(fields: ActivityName) {
    super(ActivityNameTableName, [
      ActivityNameFields.ActivityID,
      ActivityNameFields.Display,
      ActivityNameFields.Lang
    ])
    this.fields = fields
  }

  importFromDatabase(record: ActivityName): void {
    this.fields = record
  }

  exportJsonToDatabase(): ActivityName {
    return this.fields
  }
}
