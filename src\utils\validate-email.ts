export function validateEmail (email: any): void {
  // validates an email address (line 109)
  if (typeof email === 'string') {
    if (email.startsWith('mailto:')) {
      // validate email, validation as per RFC2822 standards.
      const regExp = new RegExp(/^((?!\.)[\w\-_.]*[^.])(@\w+)(\.\w+(\.\w+)?[^.\W])$/g)
      if (!regExp.test(email.replace('mailto:',''))) {
        throw new Error(`mbox value ${email} is not a valid email`)
      }
    }
    else {
      throw new Error(`mbox value ${email} did not start with mailto:`)
    }
  }
    else {
      throw new Error(`mbox value must be a string type`)
    }
}

export function validateSha1sumEmail (sha1Sum: any): void {
  // validate an email sha1sum (line 154)
  if (typeof sha1Sum === 'string') {
    const regExp = new RegExp(/^[a-fA-F\d]{40}$/)
    if (!regExp.test(sha1Sum)) {
      throw new Error(`mbox_sha1sum value [${sha1Sum}] is not a valid sha1sum`)
    }
  }
  else {
    throw new Error(`mbox_sha1sum value must be a string type`)
  }
}
