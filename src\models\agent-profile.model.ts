import { Table } from '@lcs/mssql-utility'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentProfileFields, ActivityStateTableName } from '@tess-f/sql-tables/dist/lrs/agent-profiles.js'

export default class AgentProfileModel extends Table<AgentProfile, AgentProfile> {
  fields: AgentProfile

  constructor(fields: AgentProfile) {
    super(ActivityStateTableName, [
      AgentProfileFields.AgentID,
      AgentProfileFields.ContentType,
      AgentProfileFields.Etag,
      AgentProfileFields.JsonProfile,
      AgentProfileFields.ProfileID,
      AgentProfileFields.Updated
    ])
    this.fields = fields
  }

  importFromDatabase(record: AgentProfile): void {
    this.fields = record
  }

  exportJsonToDatabase(): AgentProfile {
    return this.fields
  }

}