import mssql, { getRows, DB_Errors as dbErrors } from '@lcs/mssql-utility'
import AgentModel from '../../../models/agents.model.js'
import getGroupMembers from './member/get-group-members.service.js'
import { Agent, AgentTableName } from '@tess-f/sql-tables/dist/lrs/agent.js'

export default async function getAgentService(search: Partial<Agent>): Promise<AgentModel> {
  const pool = mssql.getPool()
  const res = await getRows<Agent>(AgentTableName, pool.request(), search)
  const agent = new AgentModel(undefined, res[0])
  if (agent.fields.objectType === 'Group') {
    try {
      const members = await getGroupMembers(agent.ID)
      agent.attachMembers(members)
    } catch (error) {
      if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
        throw error
      }
    }
  }
  return agent
}
