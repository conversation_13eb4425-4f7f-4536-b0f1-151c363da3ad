import mssql, { DB_Errors as dbErrors, getRows } from '@lcs/mssql-utility'
import { getErrorMessage } from '@tess-f/backend-utils'
import { RelevantTypes, RelevantTypesFields, RelevantTypesTableName } from '@tess-f/sql-tables/dist/lrs/relevant-types.js'

export default async function getOrCreateRelevantType (iri: string): Promise<RelevantTypes> {
  const pool = mssql.getPool()
  try {
    const relevantTypes = await getRows<RelevantTypes>(RelevantTypesTableName, pool.request(), { IRI: iri })
    return relevantTypes[0]
  } catch (error) {
    if (getErrorMessage(error) === dbErrors.default.NOT_FOUND_IN_DB) {
      const request = pool.request()
      request.input('iri', iri)
      const response = await request.query<RelevantTypes>(`
        INSERT INTO [${RelevantTypesTableName}] ([${RelevantTypesFields.IRI}])
        OUTPUT INSERTED.*
        VALUES (@iri)
      `)
      
      if (response.recordset.length <= 0) {
        throw new Error(dbErrors.default.DB_INSERT_FAILED_TO_OUTPUT)
      }

      return response.recordset[0]
    }
    throw error
  }
}
