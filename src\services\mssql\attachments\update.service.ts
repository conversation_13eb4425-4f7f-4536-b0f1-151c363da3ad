import mssql, { updateRow } from '@lcs/mssql-utility'
import { Attachment } from '@tess-f/sql-tables/dist/lrs/attachments.js'
import StatementAttachment from '../../../models/attachment.model.js'

export default async function updateStatementAttachment(attachment: StatementAttachment): Promise<StatementAttachment> {
  const updated = await updateRow<Attachment>(mssql.getPool().request(), attachment, { Id: attachment.fields.Id })
  return new StatementAttachment(updated[0])
}
