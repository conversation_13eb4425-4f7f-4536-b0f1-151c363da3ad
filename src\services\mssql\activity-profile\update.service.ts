import mssql, { updateRow } from '@lcs/mssql-utility'
import ActivityProfileModel from '../../../models/activity-profile.model.js'
import { ActivityProfile } from '@tess-f/sql-tables/dist/lrs/activity-profile.js'

export default async function updateActivityProfile(activityProfile: ActivityProfileModel): Promise<ActivityProfileModel> {
  const identityFields: Partial<ActivityProfile> = {
    ID: activityProfile.fields.ID,
    ActivityID: activityProfile.fields.ActivityID
  }

  const updated = await updateRow<ActivityProfile>(mssql.getPool().request(), activityProfile, identityFields)
  return new ActivityProfileModel(updated[0])
}
