import { Router } from 'express'
import validateGetAgentsMiddleware from './middleware/get-agents.validator.js'
import getAgentsController from './get-agents.controller.js'
import agentProfileRouter from './profiles/agent-profile-router.js'

const router = Router()

router.get('/', validateGetAgentsMiddleware, getAgentsController)
// agent profile
router.use('/profile', agentProfileRouter)
export default router
