import { checkIfDict, validateIri } from "../../../utils/validate-data-type.js";

export default function validateExtensions (extensions: any, field: string): void {
  // validates extensions (line 353)
  // Ensure incoming extensions is a dictionary
  checkIfDict(extensions, `${field} extensions`)

  // Ensure each key in extensions is a valid IRI
  Object.keys(extensions).forEach((extension: string) => {
    validateIri(extension, field)
  });

}
