import mssql, { deleteRow } from '@lcs/mssql-utility'
import { ActivityProfile, ActivityProfileTableName } from '@tess-f/sql-tables/dist/lrs/activity-profile.js'

export default async function deleteActivityProfile(profileId: string, activityId: string): Promise<number> {
  const identityFields: Partial<ActivityProfile> = {
    ID: profileId,
    ActivityID: activityId
  }

  return deleteRow<ActivityProfile>(mssql.getPool().request(), ActivityProfileTableName, identityFields)
}