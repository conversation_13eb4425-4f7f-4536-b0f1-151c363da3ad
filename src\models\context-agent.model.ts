import { Table } from '@lcs/mssql-utility'
import { ContextAgent, ContextAgentTableName } from '@tess-f/sql-tables/dist/lrs/context-agent.js'
import AgentModel, { AgentJson } from './agents.model.js'

export interface ContextAgentJson {
  // ID?: string,
  objectType?: string,
  // statementID?: string,
  agent?: AgentJson,
  relevantTypes?: string[]
}

export default class ContextAgentModel extends Table<ContextAgentJson, ContextAgent> {
  fields: ContextAgentJson
  Id!: string
  agentId!: string
  statementId!: string
  private _agent: AgentModel = new AgentModel()

  constructor(fields?: ContextAgentJson, record?: ContextAgent) {
    super(ContextAgentTableName)
    if (fields) {
      this.fields = fields
    } else {
      this.fields = {}
    }

    if (record) this.importFromDatabase(record)
  }

  importFromDatabase(record: ContextAgent): void {
    this.Id = record.ID!
    this.agentId = record.AgentID!
    this.statementId = record.StatementID!
    this.fields = {
      objectType: record.ObjectType
    }
  }

  exportJsonToDatabase(): ContextAgent {
    return {
      ID: this.Id,
      StatementID: this.statementId,
      AgentID: this.agentId,
      ObjectType: this.fields.objectType
    }
  }

  attachAgent (agent: AgentModel): void {
    this._agent = agent
    this.agentId = agent.ID
    this.fields.agent = agent.fields
  }

  attachRelevantTypes (types: string[]) {
    this.fields.relevantTypes = types
  }

  toJson (idsOnly = false): Record<string, any> {
    return {
      objectType: this.fields.objectType,
      agent: this._agent.toJson(idsOnly),
      relevantTypes: this.fields.relevantTypes
    }
  }
}