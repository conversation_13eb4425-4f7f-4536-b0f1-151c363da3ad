import { Table } from '@lcs/mssql-utility'
import Activity, { ActivityJSON } from './activity.model.js'
import Agent, { AgentJson } from './agents.model.js'
import SubStatementCategoryContextActivity from './sub-statement-category-context-activities.model.js'
import SubStatementGroupingContextActivity from './sub-statement-grouping-context-activity.model.js'
import SubStatementOtherContextActivity from './sub-statement-other-context-activities.model.js'
import SubStatementParentContextActivity from './sub-statement-parent-context-activities.model.js'
import VerbDisplay from './verb-display.model.js'
import { VerbJson } from './verb.model.js'
import { SubStatement, SubStatementFields, StatementTableName } from '@tess-f/sql-tables/dist/lrs/sub-statement.js'
import getLangMap from '../utils/get-lang-map.js'

export interface StatementRef {
  objectType: string,
  id: string
}

export interface SubStatementJson {
  objectType: string
  actor?: AgentJson
  verb?: VerbJson
  object?: ActivityJSON | AgentJson | StatementRef
  result?: {
    score?: {
      scaled?: number
      raw?: number
      min?: number
      max?: number
    },
    success?: boolean
    completion?: boolean
    response?: string
    duration?: string
    extensions?: { [key: string]: any }
  },
  context?: {
    registration?: string
    instructor?: AgentJson
    team?: AgentJson
    contextActivities?: {
      parent?: ActivityJSON[]
      grouping?: ActivityJSON[]
      category?: ActivityJSON[]
      other?: ActivityJSON[]
    },
    revision?: string
    platform?: string
    language?: string
    statement?: {
      objectType: string
      id: string
    }
    extensions?: { [key: string]: any }
  },
  timestamp?: string
}

export default class SubStatementModel extends Table<SubStatementJson, SubStatement> {
  public fields!: SubStatementJson
  public ID!: string

  public readonly actorID: string
  public readonly contextInstructorID?: string
  public readonly contextTeamID?: string
  public readonly objectAgentID?: string
  public readonly objectActivityID?: string

  private _objectAgent?: Agent
  private _objectActivity?: Activity
  private _actor?: Agent
  private _contextInstructor?: Agent
  private _contextTeam?: Agent
  private _verbDisplay?: VerbDisplay[]
  private _statementRefId?: string
  private _contextCategoryActivities?: Activity[]
  private _contextParentActivities?: Activity[]
  private _contextGroupingActivities?: Activity[]
  private _contextOtherActivities?: Activity[]

  constructor(fields?: SubStatementJson, record?: SubStatement) {
    super(StatementTableName, [
      SubStatementFields.ActorID,
      SubStatementFields.VerbID
    ])
    if (fields) {
      this.fields = fields
      this.actorID = ''
    } else if (record) {
      this.importFromDatabase(record)
      this.actorID = record.ActorID!
      this.contextInstructorID = record.ContextInstructorID
      this.contextTeamID = record.ContextTeamID
      this.objectActivityID = record.ObjectActivityID
      this.objectAgentID = record.ObjectAgentID
    } else {
      throw new Error('You must provide statement fields or database record')
    }
  }

  public importFromDatabase(record: SubStatement): void {
    this.ID = record.ID!
    this.fields = {
      objectType: 'SubStatement',
      verb: {
        id: record.VerbID!
      }
    }

    if (record.ResultCompletion !== null) {
      this.ensureResultField()
      this.fields.result!.completion = record.ResultCompletion
    }

    if (record.ResultDuration) {
      this.ensureResultField()
      this.fields.result!.duration = record.ResultDuration
    }

    if (record.ResultExtensions) {
      this.ensureResultField()
      this.fields.result!.extensions = JSON.parse(record.ResultExtensions)
    }

    if (record.ResultResponse) {
      this.ensureResultField()
      this.fields.result!.response = record.ResultResponse
    }

    if (record.ResultScoreMax !== null) {
      this.ensureResultField(true)
      this.fields.result!.score!.max = record.ResultScoreMax
    }

    if (record.ResultScoreMin !== null) {
      this.ensureResultField(true)
      this.fields.result!.score!.min = record.ResultScoreMin
    }

    if (record.ResultScoreRaw !== null) {
      this.ensureResultField(true)
      this.fields.result!.score!.raw = record.ResultScoreRaw
    }

    if (record.ResultScoreScaled !== null) {
      this.ensureResultField(true)
      this.fields.result!.score!.scaled = record.ResultScoreScaled
    }

    if (record.ResultSuccess !== null) {
      this.ensureResultField()
      this.fields.result!.success = record.ResultSuccess
    }

    if (record.ContextStatement) {
      this.ensureContextField()
      this.fields.context!.statement = {
        id: record.ContextStatement,
        objectType: 'StatementRef'
      }
    }

    if (record.ContextExtensions) {
      this.ensureContextField()
      this.fields.context!.extensions = JSON.parse(record.ContextExtensions)
    }

    if (record.ContextLanguage) {
      this.ensureContextField()
      this.fields.context!.language = record.ContextLanguage
    }

    if (record.ContextPlatform) {
      this.ensureContextField()
      this.fields.context!.platform = record.ContextPlatform
    }

    if (record.ContextRegistration) {
      this.ensureContextField()
      this.fields.context!.registration = record.ContextRegistration
    }

    if (record.ContextRevision) {
      this.ensureContextField()
      this.fields.context!.revision = record.ContextRevision
    }

    if (record.ObjectStatementRef) {
      this.fields.object = {
        objectType: 'StatementRef',
        id: record.ObjectStatementRef
      }
      this._statementRefId = record.ObjectStatementRef
    }
  }

  public exportJsonToDatabase(): SubStatement {
    return {
      ObjectAgentID: this._objectAgent ? this._objectAgent.ID : undefined,
      ObjectActivityID: this._objectActivity ? this._objectActivity.fields.id : undefined,
      ObjectStatementRef: this.fields.object && this.fields.object.objectType === 'StatementRef' && 'id' in this.fields.object ? this.fields.object.id : undefined,
      ActorID: this._actor!.ID,
      VerbID: this.fields.verb!.id,
      ResultSuccess: this.fields.result?.success,
      ResultCompletion: this.fields.result?.completion,
      ResultResponse: this.fields.result?.response,
      ResultDuration: this.fields.result?.duration,
      ResultScoreScaled: this.fields.result?.score?.scaled,
      ResultScoreMax: this.fields.result?.score?.max,
      ResultScoreMin: this.fields.result?.score?.min,
      ResultScoreRaw: this.fields.result?.score?.raw,
      ResultExtensions: this.fields.result?.extensions ? JSON.stringify(this.fields.result.extensions) : undefined,
      ContextRegistration: this.fields.context?.registration,
      ContextInstructorID: this._contextInstructor ? this._contextInstructor.ID : undefined,
      ContextTeamID: this._contextTeam ? this._contextTeam.ID : undefined,
      ContextRevision: this.fields.context?.revision,
      ContextPlatform: this.fields.context?.platform,
      ContextLanguage: this.fields.context?.language,
      ContextExtensions: this.fields.context?.extensions ? JSON.stringify(this.fields.context.extensions) : undefined,
      ContextStatement: this.fields.context?.statement?.id
    }
  }

  public attachObjectAgent(agent: Agent) {
    this._objectAgent = agent
    this.fields.object = agent.fields
  }

  public attachObjectActivity(activity: Activity) {
    this._objectActivity = activity
    this.fields.object = activity.fields
  }

  public attachActor(actor: Agent) {
    this._actor = actor
    this.fields.actor = actor.fields
  }

  public attachContextInstructor(instructor: Agent) {
    this._contextInstructor = instructor
    if (!this.fields.context) {
      this.fields.context = {
        instructor: instructor.fields
      }
    } else {
      this.fields.context.instructor = instructor.fields
    }
  }

  public attachContextTeam(team: Agent) {
    this._contextTeam = team
    if (!this.fields.context) {
      this.fields.context = {
        team: team.fields
      }
    } else {
      this.fields.context.team = team.fields
    }
  }

  public attachContextParentActivities(activities: Activity[]) {
    this._contextParentActivities = activities
    this.ensureContextActivitiesFieldExists()
    this.fields.context!.contextActivities!.parent = activities.map(activity => activity.fields)
  }

  public getContextParentJoins(): SubStatementParentContextActivity[] {
    if (this.fields.context?.contextActivities?.parent) {
      return this.fields.context.contextActivities.parent.map(activity => new SubStatementParentContextActivity({
        StatementID: this.ID,
        ActivityID: activity.id
      }))
    }
    return []
  }

  public attachContextOtherActivities(activities: Activity[]) {
    this._contextOtherActivities = activities
    this.ensureContextActivitiesFieldExists()
    this.fields.context!.contextActivities!.other = activities.map(activity => activity.fields)
  }

  public getContextOtherJoins(): SubStatementOtherContextActivity[] {
    if (this.fields.context?.contextActivities?.other) {
      return this.fields.context.contextActivities.other.map(activity => new SubStatementOtherContextActivity({
        StatementID: this.ID,
        ActivityID: activity.id
      }))
    }
    return []
  }

  public attachContextGroupingActivities(activities: Activity[]) {
    this._contextGroupingActivities = activities
    this.ensureContextActivitiesFieldExists()
    this.fields.context!.contextActivities!.grouping = activities.map(activity => activity.fields)
  }

  public getContextGroupingJoins(): SubStatementGroupingContextActivity[] {
    if (this.fields.context?.contextActivities?.grouping) {
      return this.fields.context.contextActivities.grouping.map(activity => new SubStatementGroupingContextActivity({
        StatementID: this.ID,
        ActivityID: activity.id
      }))
    }
    return []
  }

  public attachContextCategoryActivities(activities: Activity[]) {
    this._contextCategoryActivities = activities
    this.ensureContextActivitiesFieldExists()
    this.fields.context!.contextActivities!.category = activities.map(activity => activity.fields)
  }

  public getContextCategoryJoins(): SubStatementCategoryContextActivity[] {
    if (this.fields.context?.contextActivities?.category) {
      return this.fields.context.contextActivities.category.map(activity => new SubStatementCategoryContextActivity({
        StatementID: this.ID,
        ActivityID: activity.id
      }))
    }
    return []
  }

  private ensureContextActivitiesFieldExists() {
    this.ensureContextField()
    if (!this.fields.context!.contextActivities) {
      this.fields.context!.contextActivities = {}
    }
  }

  public attachVerbDisplay(display: VerbDisplay[]) {
    this._verbDisplay = display
    if (display.length <= 0) {
      return
    }
    if (!this.fields.verb) {
      this.fields.verb = {
        id: display[0].fields.VerbID!
      }
    }
    this.fields.verb.display = display.reduce((a, v) => ({ ...a, [v.fields.Lang!]: v.fields.Display }), {})
  }

  private ensureResultField(ensureScore = false): void {
    if (!this.fields.result) {
      this.fields.result = {}
    }
    if (ensureScore && !this.fields.result.score) {
      this.fields.result.score = {}
    }
  }

  private ensureContextField(): void {
    if (!this.fields.context) {
      this.fields.context = {}
    }
  }

  public toJson(language?: string[], idsOnly = false): { [key: string]: any } {
    const ret: { [key: string]: any } = {}

    ret.actor = this._actor?.toJson(idsOnly)
    ret.verb = {
      id: this.fields.verb?.id
    }
    if (!idsOnly) {
      if (this._verbDisplay) {
        ret.verb.display = getLangMap(this._verbDisplay.map(dis => { return { Lang: dis.fields.Lang!, Display: dis.fields.Display! } }), language)
      } else {
        ret.verb.display = {}
      }
    }

    if (this._objectAgent) {
      ret.object = this._objectAgent.toJson(idsOnly)
    } else if (this._objectActivity) {
      ret.object = this._objectActivity.toJson(language, idsOnly)
    } else {
      ret.object = {
        id: this._statementRefId,
        objectType: 'StatementRef'
      }
    }

    if (this.fields.result) {
      ret.result = this.fields.result
    }

    if (this.fields.context) {
      ret.context = {}
      if (this.fields.context.contextActivities) {
        ret.context.contextActivities = {}
        if (this._contextCategoryActivities && this._contextCategoryActivities.length > 0) {
          ret.context.contextActivities.category = this._contextCategoryActivities.map(activity => activity.toJson(language, idsOnly))
        }
        if (this._contextGroupingActivities && this._contextGroupingActivities.length > 0) {
          ret.context.contextActivities.grouping = this._contextGroupingActivities.map(activity => activity.toJson(language, idsOnly))
        }
        if (this._contextOtherActivities && this._contextOtherActivities.length > 0) {
          ret.context.contextActivities.other = this._contextOtherActivities.map(activity => activity.toJson(language, idsOnly))
        }
        if (this._contextParentActivities && this._contextParentActivities.length > 0) {
          ret.context.contextActivities.parent = this._contextParentActivities.map(activity => activity.toJson(language, idsOnly))
        }
      }
      if (this.fields.context.extensions) {
        ret.context.extensions = this.fields.context.extensions
      }
      if (this._contextInstructor) {
        ret.context.instructor = this._contextInstructor.toJson(idsOnly)
      }
      if (this._contextTeam) {
        ret.context.team = this._contextTeam.toJson(idsOnly)
      }
      if (this.fields.context.registration) {
        ret.context.registration = this.fields.context.registration
      }
      if (this.fields.context.revision) {
        ret.context.revision = this.fields.context.revision
      }
      if (this.fields.context.platform) {
        ret.context.platform = this.fields.context.platform
      }
      if (this.fields.context.language) {
        ret.context.language = this.fields.context.language
      }
      if (this.fields.context.statement) {
        ret.context.statement = this.fields.context.statement
      }
    }

    if (this.fields.timestamp) {
      ret.timestamp = this.fields.timestamp
    }
    ret.objectType = 'SubStatement'

    return ret
  }

}
