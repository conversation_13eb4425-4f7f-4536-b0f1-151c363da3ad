// GET, gets Profile Resources
import logger from '@lcs/logger'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import { getActivityProfileService, getActivitiesProfileService } from '../../../../services/mssql/activity-profile/get.service.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import getFile from '../../../../services/amqp/file/get.service.js'

const log = logger.create('HTTP-Controller.Get-Activity-Profile', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // profile id means we want only 1 item
  const profileId = req.query.profileId?.toString()
  const activityId = req.query.activityId!.toString()
  const since = req.query.since?.toString()
  if (profileId) {
    try {
      const activityProfile = await getActivityProfileService(profileId, activityId)
      log('info', 'Successfully retrieved activity profile', { id: activityProfile.fields.ID, success: true })
      if (activityProfile.fields.Etag) {
        res.setHeader('ETag', `"${activityProfile.fields.Etag}"`)
      }
      res.setHeader('Last-Modified', activityProfile.fields.ModifiedOn!.toISOString())

      if (activityProfile.fields.FileID) {
        // Retrieve file from FDS and send back to caller
        const file = await getFile(activityProfile.fields.FileID)
        res.setHeader('Content-Type', activityProfile.fields.ContentType!)
        res.set('Content-disposition', `filename=${file.filename}`)
        res.send(file.buffer.toString())
      } else if (activityProfile.fields.ContentType === 'application/json') {
        res.json(JSON.parse(activityProfile.fields.Profile!))
      } else {
        res.contentType(activityProfile.fields.ContentType ?? 'text/html')
        res.send(activityProfile.fields.Profile)
      }

    } catch (error) {
      const errorMessage = getErrorMessage(error)
      if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
        log('warn', 'Failed to get activity profile because was not found in the database', { success: false, profileId, errorMessage })
        res.status(NOT_FOUND).send('Activity profile not found')
      } else {
        log('error', 'Failed to get activity profile', { success: false, errorMessage })
        res.sendStatus(INTERNAL_SERVER_ERROR)
      }
    }
  } else {
    // No profile id means we want an array of profile ids
    try {
      const activitiesProfile = await getActivitiesProfileService(activityId, since)
      log('info', 'Successfully retrieved activities profile', { activitiesProfileIds: activitiesProfile.map(profile=>profile.fields.ActivityID), success: true })
      res.json(activitiesProfile.map(profile => profile.fields.ID))

    } catch (error) {
      if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
        log('warn', 'Failed to get activities profile because was not found in the database', { id: profileId, success: false })
        res.status(NOT_FOUND).send('Activities profile not found')
      } else {
        log('error', 'Failed to get activity profiles', { errorMessage: getErrorMessage(error), success: false })
        res.sendStatus(INTERNAL_SERVER_ERROR)
      }
    }
  }
}
