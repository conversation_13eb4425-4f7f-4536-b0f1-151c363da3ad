import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import AgentProfile from '../../../../models/agent-profile.model.js'
import createAgentProfileService from './create.service.js'
import { getAgentProfileService } from './get.service.js'

export default async function getOrCreateAgentProfileService(agentProfile: AgentProfile): Promise<{ agentProfile: AgentProfile, created: boolean }> {
  if (!agentProfile.fields.ProfileID || !agentProfile.fields.AgentID) {
    throw new Error('Missing required fields')
  }

  try {
    const profile = await getAgentProfileService(agentProfile.fields.ProfileID, agentProfile.fields.AgentID)
    return {
      agentProfile: profile,
      created: false
    }
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      const created = await createAgentProfileService(agentProfile)
      return {
        agentProfile: created,
        created: true
      }
    } else {
      throw error
    }
  }

}