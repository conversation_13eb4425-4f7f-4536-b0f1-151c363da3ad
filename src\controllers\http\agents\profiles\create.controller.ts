import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getOrCreateAgentProfileService from '../../../../services/mssql/agent/profile/get-or-create.js'
import getOrCreateAgent from '../../../../services/mssql/agent/get-or-create.service.js'
import AgentProfileModel from '../../../../models/agent-profile.model.js'
import AgentModel from '../../../../models/agents.model.js'
import { File } from 'formidable'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NO_CONTENT, PRECONDITION_FAILED } = httpStatus
import parseForm from '../../../../utils/parse-form.js'
import fsExtra from 'fs-extra'
import saveFile from '../../../../services/amqp/file/save.service.js'
import { createHash } from 'crypto'
import deleteFile from '../../../../services/amqp/file/delete.service.js'
import { RESOURCE_DOES_NOT_EXIST, checkModificationConditions } from '../../../../utils/etag.utils.js'
import { getErrorMessage, getErrorStackTrace, httpLogTransformer } from '@tess-f/backend-utils'
import { EtagPreconditionFail } from '../../../../utils/error.utils.js'
import deleteAgentProfile from '../../../../services/mssql/agent/profile/delete.service.js'
import updateAgentProfileService from '../../../../services/mssql/agent/profile/update.service.js'

const log = logger.create('HTTP-Controller.Create-Agent-Profile', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    let file: File | undefined
    let base64Contents: string | undefined
    let profile: string | undefined
    const hasher = createHash('sha256')
    let etag: string | undefined

    if (req.is('multipart/*')) {
      // get the file from the request
      const { files } = await parseForm(req)
      let fileCount = 0
      for (const fileKey in files) {
        if (files[fileKey]!.length > 1) {
          fileCount += files[fileKey]!.length
        }
        fileCount++
        file = files[fileKey]![0]
      }
      if (fileCount > 1) {
        // cannot have more than 1 file
        res.status(BAD_REQUEST).send('Invalid form: request cannot contain more than 1 file')
        // remove the temp uploaded files
        for (const fileKey in files) {
          for (const f of files[fileKey]!) {
            await fsExtra.remove(f.filepath)
          }
        }
        return
      }

      if (file) {
        base64Contents = await fsExtra.readFile(file.filepath, 'base64')
        await fsExtra.remove(file.filepath)
        hasher.update(base64Contents)
        etag = hasher.digest('hex')
      }

    } else {
      profile = JSON.stringify(req.body)
      hasher.update(profile)
      etag = hasher.digest('hex')
    }


    const agent = await getOrCreateAgent(new AgentModel(JSON.parse(req.query.agent!.toString())))

    // /get or create the agent profile
    const { agentProfile, created } = await getOrCreateAgentProfileService(new AgentProfileModel({
      AgentID: agent.ID,
      ProfileID: req.query.profileId!.toString(),
      FileID: null,
      Etag: etag
    }))


    try {
      checkModificationConditions(req, agentProfile.fields.Etag, created, true)
    } catch (error) {
      if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DOES_NOT_EXIST) {
        log('warn', 'Failed to create activity profile: ETag Precondition failed with resource does not exist. Removing created profile record', { success: false, req })
        await deleteAgentProfile(agentProfile.fields.ProfileID!, agent.ID)
        if (agentProfile.fields.FileID) {
          await deleteFile([agentProfile.fields.FileID])
        }
      }
      throw error
    }

    if (created) {
      // this is a new profile
      if (file && base64Contents) {
        // we have a file being uploaded
        // now that we have the content lets save it in fds
        agentProfile.fields.ContentType = file.mimetype ?? 'text/plain'
        log('verbose', 'Activity profile is a file, uploading to FDS')
        agentProfile.fields.FileID = await saveFile(base64Contents, file.originalFilename ?? 'agent-profile-file.txt', agentProfile.fields.ContentType)
      } else if (profile) {
        // we have a json profile object
        agentProfile.fields.JsonProfile = profile
        agentProfile.fields.ContentType = 'application/json'
      }
    } else if (file && base64Contents) {

      if (agentProfile.fields.FileID) {
        log('verbose', 'Activity has new file, removing previous file from FDS')
        await deleteFile([agentProfile.fields.FileID])
      }
      agentProfile.fields.JsonProfile = null // wipe out previous JSON profile if it had one
      agentProfile.fields.ContentType = file.mimetype ?? 'text/plain'
      log('verbose', 'Saving activity profile file in FDS')
      agentProfile.fields.FileID = await saveFile(base64Contents, file.originalFilename ?? 'agent-profile-file.txt', agentProfile.fields.ContentType)
    } else if (profile) {

      let originalProfile: any = {}
      if (agentProfile.fields.JsonProfile) {
        originalProfile = JSON.parse(agentProfile.fields.JsonProfile)
      }
      const keys = Object.keys(originalProfile).concat(Object.keys(req.body))
      // merge the objects together
      const merged: any = {}
      for (const key of keys) {

        if (req.body[key]) {
          merged[key] = req.body[key]
        } else if (originalProfile[key]) {
          merged[key] = originalProfile[key]
        }
      }
      agentProfile.fields.JsonProfile = JSON.stringify(merged)
      const updateHasher = createHash('sha256')
      updateHasher.update(agentProfile.fields.JsonProfile)
      agentProfile.fields.Etag = updateHasher.digest('hex')
      agentProfile.fields.ContentType = 'application/json'
    }

    if (req.header('updated') && Date.parse(req.header('updated')!)) {
      agentProfile.fields.Updated = new Date(req.header('updated')!)
    } else {
      agentProfile.fields.Updated = new Date()
    }

    // update the activity profile
    await updateAgentProfileService(agentProfile)
    log('info', 'Successfully set agent profile', {
      profileID: agentProfile.fields.ProfileID,
      success: true, req
    })

    res.sendStatus(NO_CONTENT)
  } catch (error) {
    if (error instanceof EtagPreconditionFail) {
      log('warn', 'Failed to save agent profile: Etag precondition failed', { errorMessage: error.message, success: false, req })
      res.status(PRECONDITION_FAILED).send(error.message)
    } else {
      log('error', 'Failed to save agent profile: unknown error', { errorMessage: getErrorMessage(error), req, errorStack: getErrorStackTrace(error), success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
