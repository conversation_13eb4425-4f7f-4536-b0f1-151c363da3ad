export default class UrlParam {
  private readonly _key: string
  public get key (): string {
    return this._key
  }

  private readonly _value: string
  public get value (): string {
    return this._value
  }

  constructor (key: string, value: any) {
    this._key = key
    if (typeof value === 'string') {
      this._value = value
    } else if (typeof value === 'boolean') {
      this._value = value ? 'true' : 'false'
    } else if (typeof value === 'number') {
      this._value = value.toString()
    } else if (value instanceof Date) {
      this._value = value.toISOString()
    } else if (Array.isArray(value)) {
      this._value = value.map(val => val.toString()).join(',')
    } else {
      this._value = JSON.stringify(value)
    }
  }
}
