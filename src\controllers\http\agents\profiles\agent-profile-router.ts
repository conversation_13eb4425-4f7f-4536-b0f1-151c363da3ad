import { Router } from 'express'
import createAgentProfileController from './create.controller.js'
import getAgentProfileController from './get.controller.js'
import deleteAgentProfileController from './delete.controller.js'
import updateAgentProfileController from './update.controller.js'
import validateGetAgentProfileMiddleware from './middleware/get-agent-profile.middleware.js'
import validatePostAgentProfileMiddleware from './middleware/post-agent-profile.middleware.js'
import validatePutAgentProfileMiddleware from './middleware/validate-put-agent-profile.middleware.js'
import validateDeleteAgentProfileMiddleware from './middleware/delete-agent-profile.middleware.js'

const router = Router()

router.post('/', validatePostAgentProfileMiddleware, createAgentProfileController)
router.get('/', validateGetAgentProfileMiddleware, getAgentProfileController)
router.delete('/', validateDeleteAgentProfileMiddleware, deleteAgentProfileController)
router.put('/', validatePutAgentProfileMiddleware, updateAgentProfileController)

export default router
