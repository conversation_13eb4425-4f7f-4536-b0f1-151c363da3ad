import { Table } from '@lcs/mssql-utility'
import { AgentTableName } from '@tess-f/sql-tables/dist/lrs/agent.js'

// TODO: Course Count???? This is LMS specific data... what is a course?? could a course be an AU in the dashboard??

export interface AgentSessionJson {
  objectType?: string
  name?: string
  mbox?: string
  mbox_sha1sum?: string
  openid?: string
  account?: {
    homePage: string
    name: string
  },
  sessionCount?: number,
  courseCount?: number,
  id?: string
}

export interface AgentSessionDatabaseRecord {
  ObjectType: string
  Name: string | null
  Mbox: string | null
  MboxSHA1Sum: string | null
  OpenID: string | null
  AccountHomePage: string | null
  AccountName: string | null
  SessionCount: number
  ID: string
  TotalRecords: number
  CourseCount: number
}

export default class AgentSession extends Table<AgentSessionJson, AgentSessionDatabaseRecord> {
  fields: AgentSessionJson
  ID!: string

  constructor(fields?: AgentSessionJson, record?: AgentSessionDatabaseRecord) {
    super(AgentTableName)
    if (fields) {
      this.fields = fields
    } else {
      this.fields = {}
    }

    if (record) {
      this.importFromDatabase(record)
    }
  }

  importFromDatabase(record: AgentSessionDatabaseRecord): void {
    this.ID = record.ID!
    this.fields.id = record.ID
    this.fields.objectType = record.ObjectType
    if (record.Name) {
      this.fields.name = record.Name
    }
    if (record.Mbox) {
      this.fields.mbox = record.Mbox
    }
    if (record.MboxSHA1Sum) {
      this.fields.mbox_sha1sum = record.MboxSHA1Sum
    }
    if (record.OpenID) {
      this.fields.openid = record.OpenID
    }
    if (record.AccountHomePage && record.AccountName) {
      this.fields.account = {
        homePage: record.AccountHomePage,
        name: record.AccountName
      }
    }
    this.fields.sessionCount = record.SessionCount
    this.fields.courseCount = record.CourseCount
  }

  exportJsonToDatabase(): AgentSessionDatabaseRecord {
    throw new Error('Cannot translate readonly model to database record')
  }
}
