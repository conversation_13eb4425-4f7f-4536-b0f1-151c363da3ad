import { expect } from 'chai'
import Sinon from 'sinon'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import middleware from './get-agents.validator.js'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus

describe('Middleware validate get agents request', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  it('should return bad request when a query param other than agent is used', () => {
    const mock1 = httpMocks.createMocks({ query: { foo: 'bar', bar: 'baz' } })
    const mock2 = httpMocks.createMocks({ query: { agent: 'test', baz: 'bar' } })
    const next = Sinon.spy()
    middleware(mock1.req, mock1.res, next)
    expect(mock1.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock1.res._getData()).to.contain('The get agent request contained unexpected parameters: foo, bar')
    expect(next.called).to.be.false
    middleware(mock2.req, mock2.res, next)
    expect(mock2.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock2.res._getData()).to.contain('The get agent request contained unexpected parameters: baz')
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent is not present in the query params', () => {
    const mock = httpMocks.createMocks()
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Error -- agents url, but no agent parameter.. the agent parameter is required')
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent query parameter does not represent an agent object', () => {
    const mock = httpMocks.createMocks({ query: { agent: 'test' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('agent query param is not valid')
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent query parameter is an invalid agent object', () => {
    const mock = httpMocks.createMocks({ query: { agent: '{"mbox": "<EMAIL>"}' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should call next when the request is valid', () => {
    const mock = httpMocks.createMocks({ query: { agent: '{"mbox": "mailto:<EMAIL>"}' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(next.called).to.be.true
  })
})
