import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import AgentModel from '../../../models/agents.model.js'
import { AgentTableName, Agent } from '@tess-f/sql-tables/dist/lrs/agent.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'

/**
 * Returns the Agent with most activities (sessions/context registrations)
 *  Number of unique registrations an agent has
 */
export default async function getAgentWithMostActivitiesService(): Promise<AgentModel> {
  const pool = mssql.getPool()
  const query = `
    WITH MostActiveAgent AS (
      SELECT TOP 1 [ActorID], COUNT(DISTINCT [ContextRegistration]) AS [Sessions]
      FROM [${StatementTableName}]
      GROUP BY [ActorID]
      ORDER BY [Sessions] DESC
    )
    SELECT *
    FROM [${AgentTableName}]
    WHERE [ID] IN (
      SELECT [ActorID]
      FROM [MostActiveAgent]
    )
  `

  const results = await pool.request().query<Agent>(query)

  if (results.recordset.length === 1) {
    return new AgentModel(undefined, results.recordset[0])
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
