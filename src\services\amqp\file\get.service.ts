import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import { GetFile } from '@tess-f/fds/dist/amqp/get.js'

const log = logger.create('Service-File.get-file')

/**
 * Fetches a file from FDS and returns a Buffer of the file contents and the filename
 * @param {string} fileId the id of the file to retrieve from FDS
 */
export default async function getFile(fileId: string): Promise<{ buffer: Buffer, filename: string }> {
  try {
    const response = await GetFile(settings.amqp.serviceQueues.fds, {
      fileId
    }, settings.amqp.rpc_timeout)

    if (response.success && response.data) {
      return {
        buffer: Buffer.from(response.data.fileBase64, 'base64'),
        filename: response.data.filename
      }
    } else {
      log('error', 'Failed to get file', { errorMessage: response.message, success: false })
      throw new Error(getErrorMessage(response.error || response.message || 'Failed to get file'))
    }
  } catch (error) {
    log('error', 'Failed to get file', { success: false, errorMessage: getErrorMessage(error) })
    throw error
  }
}
