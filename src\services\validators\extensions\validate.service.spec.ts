import { expect } from 'chai'
import validateExtensions from './validate.service.js'

describe('Validate extensions', () => {
  it('throws an error when extensions is not a dictionary', () => {
    expect(validateExtensions.bind(validateExtensions, 'test', 'result'))
      .to.throw('result extensions is not a properly formatted dictionary')
  })

  it('throws an error when a key of the extension is not an IRI', () => {
    expect(validateExtensions.bind(validateExtensions, {
      'http://example.com': 'value',
      'foo': 'bar'
    }, 'test'))
      .to.throw('test with value foo was not a valid IRI')
  })

  it('returns void when valid', () => {
    expect(validateExtensions({
      'http://example.com/extesnion#1': 'true'
    }, 'test')).to.equal(void 0)
  })
})
