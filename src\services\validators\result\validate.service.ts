import { checkAllowedFields, checkIfDict } from "../../../utils/validate-data-type.js"
import validateExtensions from "../extensions/validate.service.js"

const RESULT_ALLOWED_FIELDS = ['score', 'success', 'completion', 'response', 'duration', 'extensions']
const SCORE_ALLOWED_FILEDS = ['scaled', 'raw', 'min', 'max']

export default function validateResult(result: any): void {
  // validates a result (line 745)
  // Ensure incoming result is dict and check allowed fields
  checkIfDict(result, 'Result')
  checkAllowedFields(RESULT_ALLOWED_FIELDS, result, 'Result')
  const regExp = new RegExp(/^(-?)P(?=\d|T\d)(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)([DW]))?(?:T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+(?:\.\d+)?)S)?)?$/)

  // If duration included, ensure valid duration can be parsed from it
  if (Object.keys(result).includes('duration')) {
    if (!regExp.test(result.duration)) {
      throw new Error(`Error with result duration`)
    }
  }

  // If success or completion included, ensure they are boolean
  if (Object.keys(result).includes('success')) {
    if (typeof result.success != 'boolean') {
      throw new Error(`Result success must be a boolean value`)
    }
  }
  if (Object.keys(result).includes('completion')) {
    if (typeof result.completion != 'boolean') {
      throw new Error(`Result completion must be a boolean value`)
    }
  }

  // If response in result, ensure it is a string
  if (Object.keys(result).includes('response')) {
    if (typeof result.response != 'string') {
      throw new Error(`Result response must be a string`)
    }
  }

  // If extensions, validate
  if (Object.keys(result).includes('extensions')) {
    validateExtensions(result.extensions, 'result extensions')
  }

  // If score included, validate it
  if (Object.keys(result).includes('score')) {
    validateScore(result.score)
  }
}

function validateScore(score: any): void {
  // validates a score (line 779)
  // Ensure incoming score is a dict and check allowed fields
  checkIfDict(score, 'Score')
  checkAllowedFields(SCORE_ALLOWED_FILEDS, score, 'Score')

  if (Object.keys(score).includes('raw') && typeof score.raw !== 'number') {
    // If raw included with min and max, ensure it is between min and max
    // Check raw type
    throw new Error(`Score raw is not a number`)
  }

  validateMinAndMaxScore(score)
  validateScoreScale(score)
}

function validateMinAndMaxScore(score: any) {
  // If min and max are included, ensure min <= max
  if (Object.keys(score).includes('min') && Object.keys(score).includes('max')) {
    // Check types of min and max
    if (typeof score.min !== 'number') {
      throw new Error(`Score minimum is not a decimal`)
    }

    if (typeof score.max !== 'number') {
      throw new Error(`Score maximum is not a decimal`)
    }

    if (score.min >= score.max) {
      throw new Error(`Score minimum in statement result must be less than the maximum`)
    }

    if (score.raw && (score.raw < score.min || score.raw > score.max)) {
      throw new Error(`Score raw value in statement result must be between minimum and maximum`)
    }
  }
}

function validateScoreScale(score: any) {
  // If scale is included make sure it's between -1 and 1
  if (Object.keys(score).includes('scaled')) {
    // Check scaled type
    if (typeof score.scaled !== 'number') {
      throw new Error(`Score scaled is not a decimal`)
    }

    if (score.scaled < -1 || score.scaled > 1) {
      throw new Error(`Score scaled value in statement result must be between -1 and 1`)
    }
  }
}
