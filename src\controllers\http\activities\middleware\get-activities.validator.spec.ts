import { expect } from 'chai'
import Sinon from 'sinon'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import middleware from './get-activities.validator.js'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus

describe('Middleware validate get activities request', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  it('should return bad request when a query param other than activityId is used', () => {
    const mock1 = httpMocks.createMocks({ query: { foo: 'bar', bar: 'baz' } })
    const mock2 = httpMocks.createMocks({ query: { activityId: 'test', baz: 'bar' } })
    const next = Sinon.spy()
    middleware(mock1.req, mock1.res, next)
    expect(mock1.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock1.res._getData()).to.contain('The get activity request contained unexpected parameters: foo, bar')
    expect(next.called).to.be.false
    middleware(mock2.req, mock2.res, next)
    expect(mock2.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock2.res._getData()).to.contain('The get activity request contained unexpected parameters: baz')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activity is not present in the query params', () => {
    const mock = httpMocks.createMocks()
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain(`Error -- activities - method ${mock.req.method}, but activityId parameter is missing`)
    expect(next.called).to.be.false
  })

  it('should return bad request when the activityId query parameter is not a valid IRI', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'test' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    const data = mock.res._getData()
    expect(data).to.contain('Activity Id param with value test was not a valid IRI')
    expect(next.called).to.be.false
  })

  it('should call next when the request is valid', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http://example.com/activity/1' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(next.called).to.be.true
  })
})
