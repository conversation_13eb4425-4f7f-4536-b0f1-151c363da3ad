import express from 'express'
import statementRouter from '../controllers/http/statements/route-controller.js'
import dashboardRouter from '../controllers/http/dashboard/dashboard-router.js'
import getAboutController from '../controllers/http/get-about.controller.js'
import activitiesRouter from '../controllers/http/activities/activities-router.js'
import agentsRouter from '../controllers/http/agents/agents-router.js'
import xApiProcessVersionHeader from '../controllers/http/middleware/xapi-version-header.middleware.js'
import checkSessionMiddleware from '../controllers/http/middleware/check-session.middleware.js'
import cookieParser from 'cookie-parser'
import bodyParserMiddleware from '../controllers/http/middleware/body-parser.middleware.js'
import settings from '../config/settings.js'
import xApiConsistentThrough from '../controllers/http/middleware/xapi-consistent-through-header.middleware.js'
import { metricsMiddleware } from '@tess-f/backend-utils/metrics'


// Create global router
const router = express.Router()

router.use(express.json())
router.use(express.urlencoded())
router.use(express.raw())
router.use(express.text())
router.use(bodyParserMiddleware)
router.use(cookieParser(settings.sessionAuthority.cookieSecret))
router.use(metricsMiddleware())

// About route
router.get('/about', getAboutController)

// process version headers
router.use(xApiProcessVersionHeader)

// authenticate the request, everything from here down is protected
router.use(checkSessionMiddleware)

router.use('/statements', xApiConsistentThrough, statementRouter)

router.use('/dashboard', dashboardRouter)
router.use('/activities', activitiesRouter)
router.use('/agents', agentsRouter)

export default router
