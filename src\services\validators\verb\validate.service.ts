const VERB_ALLOWED_FIELDS = ['id', 'display']
import { checkAllowedFields, checkIfDict, validateDictValues, validateIri } from '../../../utils/validate-data-type.js'
import { validateLanguageMap } from '../../../utils/validate-language.js'

export default function validateVerb (verb: any, statementObject?: any): void {
  // Validates a verb (line 462)
  // Ensure incoming verb is a dict and check allowed fields
  checkIfDict(verb, 'Verb')
  checkAllowedFields(VERB_ALLOWED_FIELDS, verb, 'Verb')

  // Verb must contain id - then validate it
  if (!Object.keys(verb).includes('id')) {
    throw new Error(`Verb must contain an id`)
  }
  else {
    validateIri(verb.id, 'Verb id')
  }

  if (verb.id === 'http://adlnet.gov/expapi/verbs/voided') {
    if (statementObject && Object.keys(statementObject).includes('objectType') && statementObject.objectType) {
      if (statementObject.objectType !== 'StatementRef') {
        throw new Error(`Statement with voided verb must have StatementRef as objectType`)
      }
    }
    else {
      throw new Error(`Statement with voided verb must have StatementRef as objectType`)
    }
  }

  // If display given, ensure it's a dict (language map)
  if (Object.keys(verb).includes('display')) {
    checkIfDict(verb.display, 'Verb display')
    validateLanguageMap(verb.display, 'verb display')
    validateDictValues(verb.display, 'verb display')
  }

}
