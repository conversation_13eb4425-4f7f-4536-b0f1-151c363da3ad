import logger from '@lcs/logger'
import { NextFunction, Request, Response } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST, CONFLICT } = httpStatus
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import validateAgent from '../../../../../services/validators/agent/validate.service.js'

const log = logger.create('HTTP-Middleware:put-agent-profile-validator', httpLogTransformer)

const validParams = ['agent', 'profileId']

export default async function (req: Request, res: Response, next: NextFunction) {
  const rogueParams = Object.keys(req.query).filter(key => !validParams.includes(key))
  if (rogueParams.length > 0) {
    log('warn', 'Failed to put agent profile: unallowed query params', { rogueParams, success: false, req })
    res.status(BAD_REQUEST).send(`The put agent profile request contained unexpected parameters: ${rogueParams.join(', ')}`)
    return
  }

  if (!req.query.profileId) {
    log('warn', 'Failed to put agent profile: missing profileId', { success: false, req })
    res.status(BAD_REQUEST).send('Missing profileId parameter in the request')
    return
  }

  if (!req.query.agent) {
    log('warn', 'Failed to put agent profile: missing agent param', { success: false, req })
    res.status(BAD_REQUEST).send('Missing agent parameter in the request')
    return
  }

  let agent: any
  try {
    agent = JSON.parse(req.query.agent.toString())
  } catch (error) {
    log('warn', 'Failed to put agent profile: malformed agent query parma, failed to parse as JSON', { success: false, req })
    res.status(BAD_REQUEST).send(`agent query parameter is not valid`)
    return
  }

  try {
    validateAgent(agent, 'Agent parameter')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'Failed to put agent profile: malformed agent', { success: false, errorMessage, req })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  // check json body for application/json content
  if (req.headers['content-type'] === 'application/json' && Object.keys(req.body).length <= 0) {
    log('warn', 'Failed to put agent profile: missing request body', { success: false, req })
    res.status(BAD_REQUEST).send('Could not find the agent profile')
    return
  }

  // check for missing "If-Match" or "If-None-Match header"
  if (!req.headers['if-none-match'] && !req.headers['if-match']) {
    log('warn', 'Failed to put agent profile: missing If-None-Match or if-Match header', { success: false, req })
    res.status(CONFLICT).send('Missing If-None-Match or if-Match header')
    return
  }

  // TODO: extra validation for oauth (if present)

  // go to the next function in the chain
  next()
}