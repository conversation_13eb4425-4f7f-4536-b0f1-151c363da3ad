import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getCourseActivityCompletionAvg from '../../../../services/mssql/stats/get-course-activity-completion-avg.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Course-Activity-Completion-Average', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const average = await getCourseActivityCompletionAvg(req.query.activity ? req.query.activity.toString() : undefined)
    log('info', 'Successfully calculated course activity completion average', { average, req, success: true })
    res.json(average)
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      res.json(0)
    } else {
      log('error', 'Failed to calculate course activity completion average', { errorMessage: getErrorMessage(error), req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
