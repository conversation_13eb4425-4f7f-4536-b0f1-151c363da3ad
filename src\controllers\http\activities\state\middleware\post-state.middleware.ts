import { Request, Response, NextFunction } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
import { validateIri, validateUUID } from '../../../../../utils/validate-data-type.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import validateAgent from '../../../../../services/validators/agent/validate.service.js'
import getOrCreateAgent from '../../../../../services/mssql/agent/get-or-create.service.js'
import AgentModel from '../../../../../models/agents.model.js'
import { getActivityStateService } from '../../../../../services/mssql/activity-state/get.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'

const log = logger.create('HTTP-Controller.Get-Activity-State', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction) {
  const validParams = ['activityId', 'agent', 'stateId', 'registration', 'since']
  const badParams: Array<string> = []
  Object.keys(req.query).forEach(key => {
    if (!validParams.includes(key)) {
      badParams.push(key)
    }
  })

  if (badParams.length > 0) {
    log('warn', 'Failed to parse request: unexpected parameter', { success: false, badParams, req })
    res.status(BAD_REQUEST).send(`The post activity state request contained unexpected parameters: ${badParams.join(', ')}`)
    return
  }

  if (req.query.activityId) {
    try {
      validateIri(req.query.activityId.toString(), 'activityId')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      log('warn', 'Invalid activityId iri: ', { success: false, errorMessage, req })
      res.status(BAD_REQUEST).send(errorMessage)
      return
    }
  } else {
    log('warn', 'Failed to parse request: activityId parameter is missing', { success: false, req })
    res.status(BAD_REQUEST).send('Error -- activity_state - method = POST, but activityId parameter is missing.')
    return
  }

  if (!req.query.stateId) {
    log('warn', 'Failed to parse request: stateId parameter is missing', { success: false, req })
    res.status(BAD_REQUEST).send('Error -- activity_state - method = POST, but stateId parameter is missing.')
    return
  }

  if (req.query.registration) {
    try {
      validateUUID(req.query.registration.toString(), 'registration')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      log('warn', 'Invalid registration UUID', { registrationUuid: req.query.registration.toString(), success: false, errorMessage, req })
      res.status(BAD_REQUEST).send(errorMessage)
      return
    }
  }

  if (req.query.agent) {
    let agent: any
    try {
      agent = JSON.parse(req.query.agent.toString())
    } catch (error) {
      log('warn', 'Failed to get agents: malformed agent query param, failed to parse as JSON', { success: false, req })
      res.status(BAD_REQUEST).send('Agent query param is not valid')
      return
    }

    try {
      validateAgent(agent, 'Agent param')
    } catch {
      log('warn', 'Failed to get agent: malformed agent query param, failed to parse as JSON', { success: false, req })
      res.status(BAD_REQUEST).send(`agent query param is not valid`)
      return
    }

  } else {
    log('warn', 'Failed to parse request: agent parameter is missing', { success: false, req })
    res.status(BAD_REQUEST).send('Error -- activity_state - method = POST, but agent parameter is missing.')
    return
  }

  // Extra validation if oauth, req_validate.py line 383


  // Check json body for incoming POSTed document has properties
  if (req.headers['content-type'] === 'application/json' && Object.keys(req.body).length <= 0) {
    log('warn', 'Failed to parse request: Content-Type is application/json but empty', { success: false, req })
    res.status(BAD_REQUEST).send('Activity state document to be posted is an empty JSON')
    return
  }

  // content-type must be application/json 
  if (req.headers['content-type'] !== 'application/json') {
    log('warn', 'Failed to parse request: Content-Type is not application/json', { success: false, req })
    res.status(BAD_REQUEST).send('Activity state Content-Type is not application/json')
    return
  }

  // Check the content type if the document already exists
  const agent = await getOrCreateAgent(new AgentModel(JSON.parse(req.query.agent!.toString())))
  try {
    const activityState = await getActivityStateService(req.query.stateId!.toString(), agent.ID, req.query.activityId!.toString(), req.query.registration?.toString())
    if (activityState && activityState.fields.ContentType !== req.headers['content-type']) {
      log('warn', 'Failed to update activity state: Content-Type is different from existing document', { success: false, req })
      res.status(BAD_REQUEST).send(`Activity state already exists and Content-Type is not JSON, cannot update it with new JSON document`)
      return
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage !== dbErrors.default.NOT_FOUND_IN_DB) {
      log('error', 'Failed to check activity state existence', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
      return
    }
  }

  // go to next function in the chain
  next()
}