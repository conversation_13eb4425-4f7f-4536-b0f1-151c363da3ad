import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivityState from '../../../models/activity-state.model.js'
import createActivityStateService from './create.service.js'
import { getActivityStateService } from './get.service.js'

export default async function getOrCreateActivityStateService (activityState: ActivityState): Promise<{ activityState: ActivityState, created: boolean }> {
  if (!activityState.fields.ID || !activityState.fields.AgentID || !activityState.fields.ActivityID) {
    throw new Error ('Missing required fields')
  }

  try {
    const state = await getActivityStateService(activityState.fields.ID, activityState.fields.AgentID, activityState.fields.ActivityID, activityState.fields.RegistrationID)
    return {
      activityState: state,
      created: false
    }
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      const created = await createActivityStateService(activityState)
      return {
        activityState: created,
        created: true
      }
    } else {
      throw error
    }
  }

}
