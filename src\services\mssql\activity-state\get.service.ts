import mssql, { getRows, DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivityStateModel from '../../../models/activity-state.model.js'
import { ActivityState, ActivityStateTableName, ActivityStateFields } from '@tess-f/sql-tables/dist/lrs/activity-state.js'

export async function getActivityStateService(id: string, agentId: string, activityId: string, registration?: string): Promise<ActivityStateModel> {
  if (registration) {
    return new ActivityStateModel((await getRows<ActivityState>(ActivityStateTableName, mssql.getPool().request(), {
      ID: id,
      AgentID: agentId,
      ActivityID: activityId,
      RegistrationID: registration
    }))[0])
  }
  return new ActivityStateModel((await getRows<ActivityState>(ActivityStateTableName, mssql.getPool().request(), {
    ID: id,
    AgentID: agentId,
    ActivityID: activityId
  }))[0])
}

export async function getActivitiesStateService(activityId: string, registration?: string, since?: string): Promise<ActivityStateModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('activityId', activityId)
  let query = `
    SELECT *
    FROM [${ActivityStateTableName}]
    WHERE [${ActivityStateFields.ActivityID}] = @activityId 
  `
  if (registration) {
    // Filter by Registration and since
    request.input('registration', registration)
    if (since) {
      request.input('since', since)
      query += `AND [${ActivityStateFields.RegistrationID}] = @registration AND [${ActivityStateFields.ModifiedOn}] >= @since `

      // Registration
    } else {
      query += `AND [${ActivityStateFields.RegistrationID}] = @registration `
    }
  } else {
    // Since
    if (since) {
      request.input('since', since)
      query += `AND [${ActivityStateFields.ModifiedOn}] >= @since `
    }
  }

  const results = await request.query<ActivityState>(query)

  const activitiesState = await Promise.all(results.recordset.map(async record => {
    const activityState = new ActivityStateModel(record)
    return activityState
  }))

  return activitiesState

}