import mssql, { deleteRow } from '@lcs/mssql-utility'
import { ActivityState, ActivityStateTableName } from '@tess-f/sql-tables/dist/lrs/activity-state.js'

export default async function deleteActivityState(stateId: string, agentId: string, activityId: string, registrationId?: string): Promise<number> {
  const identityFields: Partial<ActivityState> = {
    ID: stateId,
    AgentID: agentId,
    ActivityID: activityId
  }

  if (registrationId) {
    identityFields.RegistrationID = registrationId
  }

  return deleteRow<ActivityState>(mssql.getPool().request(), ActivityStateTableName, identityFields)
}