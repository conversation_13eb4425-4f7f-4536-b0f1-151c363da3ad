import { expect } from 'chai'
import logger from '@lcs/logger'
import Sinon from 'sinon'
import userToAgent from './userToAgent.middleware.js'
import * as getUserByIdService from '../../../../services/mssql/user/get.service.js'
import httpMocks from 'node-mocks-http'
import { User } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { v4 as uuid } from 'uuid'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus

const testUser: User = { FirstName: 'Super', LastName: 'Admin', Username: 'admin' }

describe('Validate authority property', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  xit('should expect authority object to be added to request', async () => {
    const mocks = httpMocks.createMocks({ session: { userId: uuid() } })
    const next = Sinon.spy()
    const stub = Sinon.stub(getUserByIdService, 'default')
    stub.returns(Promise.resolve(testUser))
    await userToAgent(mocks.req, mocks.res, next)
    expect(mocks.req.authority).to.exist
    expect(mocks.req.authority.objectType).to.eql('Agent')
    expect(mocks.req.authority.account).to.exist
    expect(mocks.req.authority.account!.name).to.eql('admin')
    expect(mocks.req.authority.account!.homePage).to.exist

    expect(next.called).to.be.true
  })

  xit('should not call next when service fails', async () => {
    const mocks = httpMocks.createMocks({ session: { userId: uuid() } })
    const next = Sinon.spy()
    const stub = Sinon.stub(getUserByIdService, 'default')
    stub.returns(Promise.reject(new Error('Service Error')))
    await userToAgent(mocks.req, mocks.res, next)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
    expect(next.called).to.be.false
  })

})
