import { expect } from 'chai'
import { validateLanguageMap } from './validate-language.js'

describe('Validates a language map', () => {
  it('returns void when the language map is valid', () => {
    expect(validateLanguageMap({
      'en': 'english',
      'en-gb': 'english',
      'en-US': 'english'
    }, 'test lang map')).to.equal(void 0)
  })

  it('throws an error when the lang map key is not a string', () => {
    expect(validateLanguageMap.bind(validateLanguageMap, {
      'en': 'english',
      1: 'test value',
      'en-US': 'english'
    }, 'test lang map'))
      .to.throw('language 1 is not valid in test lang map')
  })

  it('throws an error when the lang map key is more than 8 characters', () => {
    expect(validateLanguageMap.bind(validateLanguageMap, {
      'indonesian': 'indonesian'
    }, 'test lang map'))
      .to.throw('language indonesian is not valid in test lang map')
    expect(validateLanguageMap.bind(validateLanguageMap, {
      'in-indonesian': 'indonesian'
    }, 'test lang map'))
      .to.throw('language in-indonesian is not valid in test lang map')
    expect(validateLanguageMap.bind(validateLanguageMap, {
      'indonesian-in': 'indonesian'
    }, 'test lang map'))
      .to.throw('language indonesian-in is not valid in test lang map')
  })

  it('returns void when the language map is zh-Hant', () => {
    expect(validateLanguageMap({
      'en-GB': 'An example meeting that happened on a specific occasion with certain people present.',
      'en-US': 'An example meeting that happened on a specific occasion with certain people present.',
      'zh-Hant': '所發生的與目前某些人特定場合的一個例子會議。'
    }, 'test lang map')).to.equal(void 0)
  })

  it('returns void when the language map is sr-Latn-RS', () => {
    expect(validateLanguageMap({
      'en-US': "It's OK",
      'sr-Latn-RS': 'U redu je'
    }, 'test lang map')).to.equal(void 0)
  })

  it('returns void when the language map is sr-Cyrl', () => {
    expect(validateLanguageMap({
      'en-GB': 'reported',
      'en-US': 'reported',
      'sr-Cyrl': 'пријавио'
    }, 'test lang map')).to.equal(void 0)

    it('returns void when the language map is zh-Hans-CN', () => {
      expect(validateLanguageMap({
        'en-GB': 'example meeting',
        'en-US': 'example meeting',
        'zh-Hans-CN': '例如会议'
      }, 'test lang map')).to.equal(void 0)
    })
  })
})
