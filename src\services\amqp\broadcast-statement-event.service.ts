import rabbitMQ from '@lcs/rabbitmq'
import settings from '../../config/settings.js'
import logger from '@lcs/logger'

const log = logger.create('Service-AMQP.broadcast-statement-event')

export default async function (route: string, body: any) {
  await rabbitMQ.publish(settings.amqp.statementExchange.name, body, route)
  log('info', 'Successfully broadcast statement event', { route, success: true })
}
