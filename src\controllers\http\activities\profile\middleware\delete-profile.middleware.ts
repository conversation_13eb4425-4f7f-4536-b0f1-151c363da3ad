import { Request, Response, NextFunction } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import { validateIri } from '../../../../../utils/validate-data-type.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('HTTP-Middleware.Delete-Activity-Profile', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction) {
  const validParams = ['activityId', 'profileId']
  const badParams: Array<string> = []
  Object.keys(req.query).forEach(key => {
    if (!validParams.includes(key)) {
      badParams.push(key)
    }
  })

  if (badParams.length > 0) {
    log('warn', 'Failed to parse request: unexpected parameter', { success: false, badParams })
    res.status(BAD_REQUEST).send(`The get activity profile request contained unexpected parameters: ${badParams.join(', ')}`)
    return
  }

  if (!req.query.activityId) {
    log('warn', 'Failed to parse request: activityId parameter is missing', { success: false })
    res.status(BAD_REQUEST).send(`Error -- activity_profile - method = ${req.method}, but activityId parameter is missing.`)
    return
  }

  try {
    validateIri(req.query.activityId.toString(), 'activityId')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'invalid activityId iri: ', { success: false, errorMessage })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  if (!req.query.profileId) {
    log('warn', 'Failed to parse request: profileId parameter is missing', { success: false })
    res.status(BAD_REQUEST).send(`Error -- activity_profile - method = ${req.method}, but profileId parameter is missing.`)
    return
  }

  next()
}