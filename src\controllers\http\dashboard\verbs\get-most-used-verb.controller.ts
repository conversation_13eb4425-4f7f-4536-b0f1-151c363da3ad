import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getMostUsedVerbs from '../../../../services/mssql/verb/get-most-used.service.js'
import Verb from '../../../../models/verb.model.js'
import getMostUsedVerbsForParentActivity from '../../../../services/mssql/verb/get-most-used-for-parent-activity.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Most-User-Verbs', httpLogTransformer)

export default async function getMostUsedVerbsController(req: Request, res: Response) {
  try {
    let verbs: Verb[]
    if (req.query && req.query.activity) {
      verbs = await getMostUsedVerbsForParentActivity(req.query.activity.toString())
    } else {
      verbs = await getMostUsedVerbs()
    }
    log('info', 'Successfully retrieved the most used verbs in the LRS', { req, count: verbs.length, success: true })
    res.json(verbs.map(verb => verb.fields))
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get the most used verbs in the LRS because none were found', { req, success: false })
      res.status(httpStatus.NOT_FOUND).send('No verbs found')
    } else {
      log('error', 'Failed to get the most used verbs in the LRS', { req, errorMessage: getErrorMessage(error), success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
