import { checkAllowed<PERSON><PERSON>s, checkIfDict, checkIfList, validate<PERSON><PERSON>, validateU<PERSON><PERSON>, checkRequiredFields } from "../../../utils/validate-data-type.js"
import { validateLanguageMap } from "../../../utils/validate-language.js"

const REF_FIELDS = ['id', 'objectType']
import validateAgent from "../agent/validate.service.js"
import { validateTimestamp } from "../../../utils/validate-time.js"
import validateVerb from "../verb/validate.service.js"
import validateResult from "../result/validate.service.js"
import validateContext from "../context/validate.service.js"
import validateExtensions from "../extensions/validate.service.js"
const ACTIVITY_ALLOWED_FIELDS = ['objectType', 'id', 'definition']
const ACT_DEF_ALLOWED_FIELDS = ['name', 'description', 'type', 'moreInfo', 'extensions', 'interactionType', 'correctResponsesPattern', 'choices', 'scale', 'source', 'target', 'steps']
const INT_ACT_FIELDS = ['id', 'description']
const SUB_ALLOWED_FIELDS = ['actor', 'verb', 'object', 'result', 'context', 'timestamp', 'objectType']
const SUB_REQUIRED_FIELDS = ['actor', 'verb', 'object']

export default function validateObject(obj: any): void {
  // validates an object (line 487)
  // Ensure incoming object is a dict
  checkIfDict(obj, 'Object')

  // If objectType is not given or is Activity it is an Activity
  // Validate the rest accordingly
  if ((!Object.keys(obj).includes('objectType')) || obj.objectType === 'Activity') {
    validateActivity(obj)
  } else if (obj.objectType === 'Agent' || obj.objectType === 'Group') {
    validateAgent(obj, 'object')
  } else if (obj.objectType === 'SubStatement') {
    validateSubStatement(obj)
  } else if (obj.objectType === 'StatementRef') {
    validateStatementRef(obj)
  } else {
    throw new Error(`The objectType in the statement's object is not valid - ${obj.objectType}`)
  }
}

export function validateActivity(activity: any): void {
  // validates an activity (line 523)
  // Ensure incoming activity is a dict and check allowed fields
  checkIfDict(activity, 'Activity')
  checkAllowedFields(ACTIVITY_ALLOWED_FIELDS, activity, 'Activity')

  // # Id must be present
  if (!Object.keys(activity).includes('id')) {
    throw new Error(`Id field must be present in an Activity`)
  }

  // Id must be valid IRI
  validateIri(activity.id, 'Activity id')

  // If definition included, validate it
  if (Object.keys(activity).includes('definition')) {
    validateActivityDefinition(activity.definition)
  }
}

function validateActivityDefinition(definition: any): void {
  // validates an activities definition (line 541)
  // Ensure incoming def is a dict and check allowed fields
  checkIfDict(definition, 'Activity definition')

  checkAllowedFields(ACT_DEF_ALLOWED_FIELDS, definition, 'Activity definition')

  // If name or description included, ensure it is a dict (language map)
  if (Object.keys(definition).includes('name')) {
    checkIfDict(definition.name, 'Activity definition name')
    validateLanguageMap(definition.name, 'Activity definition name')
  }
  if (Object.keys(definition).includes('description')) {
    checkIfDict(definition.description, 'Activity definition description')
    validateLanguageMap(definition.description, 'Activity definition description')
  }

  // If type or moreInfo included, ensure it is valid IRI
  if (Object.keys(definition).includes('type')) {
    validateIri(definition.type, 'Activity definition type')
  }
  if (Object.keys(definition).includes('moreInfo')) {
    validateIri(definition.moreInfo, 'Activity definition moreInfo')
  }

  // If interactionType included, ensure it is a string
  if (Object.keys(definition).includes('interactionType')) {
    if (typeof definition.interactionType !== 'string') {
      throw new Error(`Activity definition interactionType must be a string`)
    }

    const scormInteractionTypes: Array<string> = ['true-false', 'choice', 'fill-in', 'long-fill-in', 'matching', 'performance', 'sequencing',
      'likert', 'numeric', 'other']

    // Check if valid SCORM interactionType
    if (!scormInteractionTypes.includes(definition.interactionType)) {
      throw new Error(`Activity definition interactionType ${definition.interactionType} is not valid`)
    }
  }

  // If crp included, ensure they are strings in a list
  if (Object.keys(definition).includes('correctResponsesPattern')) {
    if (!definition.interactionType) {
      throw new Error(`interactionType must be given when correctResponsesPattern is used`)
    }
    checkIfList(definition.correctResponsesPattern, 'Activity definition correctResponsesPattern')
    // For each answer, ensure it is a string
    Object.values(definition.correctResponsesPattern).forEach(element => {
      if (typeof element !== 'string') {
        throw new Error(`Activity definition correctResponsesPattern answers must all be strings`)
      }
    })
  }

  if ((Object.keys(definition).includes('choices') || Object.keys(definition).includes('scale') || Object.keys(definition).includes('source') ||
    Object.keys(definition).includes('steps')) && !Object.keys(definition).includes('interactionType')) {
    throw new Error(`interactionType must be given when using interaction components`)
  }

  validateInteractionTypes(definition.interactionType, definition)

  // If extensions, validate it
  if (Object.keys(definition).includes('extensions')) {
    validateExtensions(definition.extensions, 'activity definition extensions')
  }
}

function checkOtherInteractionComponentFields(allowed: string[], definition: any): void {
  // checks if other interaction component fields are included when they shouldn't be. (line 606)
  const interactionComponents = ["choices", "scale", "source", "target", "steps"]
  const keys = Object.keys(definition)
  const both = interactionComponents.filter(b => keys.includes(b))

  const notAllowed = both.filter(x => !allowed.includes(x));

  if (notAllowed.length > 0) {
    throw new Error(`Only interaction component field(s) allowed (${allowed.join(', ')}) - not allowed: ${notAllowed.join(', ')}`)
  }
}

function validateInteractionTypes(interactionType: string, definition: any): void {
  // validates interaction types (line 621)
  if (interactionType === 'choice' || interactionType === 'sequencing') {
    // If choices included, ensure it is an array and validate it
    if (Object.keys(definition).includes('choices')) {
      checkOtherInteractionComponentFields(['choices'], definition)
      checkIfList(definition.choices, 'Activity definition choices')
      validateInteractionActivities(definition.choices, 'choices')
    }
  }
  else if (interactionType === 'likert') {
    // If scale included, ensure it is an array and validate it
    if (Object.keys(definition).includes('scale')) {
      checkOtherInteractionComponentFields(['scale'], definition)
      checkIfList(definition.scale, 'Activity definition scale')
      validateInteractionActivities(definition.scale, 'scale')
    }
  }
  else if (interactionType === 'matching') {
    // If scale included, ensure it is an array and validate it
    if (Object.keys(definition).includes('source')) {
      checkOtherInteractionComponentFields(['target', 'source'], definition)
      checkIfList(definition.source, 'Activity definition source')
      validateInteractionActivities(definition.source, 'source')
    }
    // If target included, ensure it is an array and validate it
    if (Object.keys(definition).includes('target')) {
      checkOtherInteractionComponentFields(['target', 'source'], definition)
      checkIfList(definition.target, 'Activity definition target')
      validateInteractionActivities(definition.target, 'target')
    }
  }
  else if (interactionType === 'performance') {
    // If steps included, ensure it is an array and validate it
    if (Object.keys(definition).includes('steps')) {
      checkOtherInteractionComponentFields(['steps'], definition)
      checkIfList(definition.steps, 'Activity definition steps')
      validateInteractionActivities(definition.steps, 'steps')
    }
  }
}

function validateInteractionActivities(activities: any, field: string): void {
  // validate interaction activities (line 663)
  const idList: Array<string> = []
  activities.forEach((act: any) => {
    // Ensure each interaction activity is a dict and check allowed
    // fields
    checkIfDict(act, `${field} interaction component`)
    checkAllowedFields(INT_ACT_FIELDS, act, `Activity definition ${field}`)
    checkRequiredFields(INT_ACT_FIELDS, act, `Activity definition ${field}`)

    // Ensure id value is string
    if (typeof act.id !== 'string') {
      throw new Error(`Interaction activity in component ${field} has an id that is not a string`)
    }

    idList.push(act.id)
    if (Object.keys(act).includes('description')) {
      // Ensure description is a dict (language map)
      checkIfDict(act.description, `${field} interaction component description`)
      validateLanguageMap(act.description, `${field} interaction component description`)
    }
  })

  // Check and make sure all ids being listed are unique
  const dups: string[] = []
  idList.forEach(id => {
    if (idList.filter(lID => lID === id).length > 1 && !dups.includes(id)) {
      dups.push(id)
    }
  })
  if (dups.length > 0) {
    throw new Error(`Interaction activities shared the same id(s) (${dups.join(' ')}) which is not allowed`)
  }
}

export function validateStatementRef(ref: any): void {
  // validates a statement ref (line 506)
  checkIfDict(ref, 'StatementRef')

  // objectType must be StatementRef
  if (ref.objectType !== 'StatementRef') {
    throw new Error(`StatementRef objectType must be set to 'StatementRef'`)
  }

  checkAllowedFields(REF_FIELDS, ref, 'StatementRef')
  checkRequiredFields(REF_FIELDS, ref, 'StatementRef')

  // Ensure id is a valid UUID
  validateUUID(ref.id, 'StatementRef id')
}

function validateSubStatement(subStatement: any): void {
  // validate sub-statement (line 695)
  // Ensure incoming substmt is a dict and check allowed and required
  // fields
  checkIfDict(subStatement, 'SubStatement')
  checkAllowedFields(SUB_ALLOWED_FIELDS, subStatement, 'SubStatement')
  checkRequiredFields(SUB_REQUIRED_FIELDS, subStatement, 'SubStatement')

  // If timestamp is included, ensure a valid time can be parsed
  if (Object.keys(subStatement).includes('timestamp')) {
    try {
      validateTimestamp(subStatement.timestamp)

    } catch (error) {
      // Reject statements that don't comply with ISO 8601 offsets
      if (subStatement.timestamp.endsWith("-00") || subStatement.timestamp.endsWith("-0000") || subStatement.timestamp.endsWith("-00:00")) {
        throw new Error(`Timestamp error - Statement Timestamp Illegal offset (-00, -0000, or -00:00) ${subStatement.timestamp}`)
      }
      throw new Error(`Timestamp error - There was an error while parsing the date from ${subStatement.timestamp} -- ${error}`)
    }
  }

  // Can't nest substmts in other substmts - if not supplied it is an
  // Activity
  if (subStatement.object?.objectType) {
    if (subStatement.object.objectType === 'SubStatement') {
      throw new Error(`Cannot nest a SubStatement inside of another SubStatement`)
    }
  } else {
    subStatement.object.objectType = 'Activity'
  }

  // Validate agent, verb, and object
  validateAgent(subStatement.actor, 'actor')
  validateObject(subStatement.object)
  validateVerb(subStatement.verb)

  // If result included, validate it
  if (Object.keys(subStatement).includes('result')) {
    validateResult(subStatement.result)
  }

  // If context included, validate it
  if (Object.keys(subStatement).includes('context')) {
    validateContext(subStatement.context, subStatement.object)
  }
}
