kind: Deployment
apiVersion: apps/v1
metadata:
  name: {{ .Values.name }}
spec:
  replicas: {{ .Values.replicas }}
  selector:
    matchLabels:
      app: {{ .Values.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.name }}
    spec:
      volumes:
        - name: config
          configMap:
            name: {{ .Values.configMapName }}
      imagePullSecrets:
        - name: {{ .Values.imagePullSecretName }}
      containers:
        - name: {{ .Values.name }}
          image: {{ .Values.imageRepository }}
          imagePullPolicy: {{ .Values.imagePullPolicy }}
          volumeMounts:
            - name: config
              mountPath: {{ .Values.configMountPath }}
              subPath: {{ .Values.configMapSubPath }}
          ports:
            - containerPort: {{ .Values.containerPort }}
              protocol: TCP
          env:
            - name: CONFIG_PATHS
              value: {{ .Values.configMapSubPath }}
            - name: API_BASE_ROUTE
              value: '{{ .Values.serverBasePath }}'
            - name: PORT
              value: '{{ .Values.containerPort }}'
                