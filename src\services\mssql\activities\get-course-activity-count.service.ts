import mssql from '@lcs/mssql-utility'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'

export default async function getCourseActivityCount(): Promise<number> {
  const pool = mssql.getPool()

  const query = `
    SELECT COUNT(DISTINCT [ObjectActivityID]) AS CourseCount
    FROM [${StatementTableName}]
    WHERE [VerbID] = 'https://adlnet.gov/expapi/verbs/initialized'
    AND [ContextRegistration] IS NOT NULL
  `

  const result = await pool.request().query<{ CourseCount: number }>(query)

  if (result.recordset.length === 1) {
    return result.recordset[0].CourseCount
  } else {
    return 0
  }
}
