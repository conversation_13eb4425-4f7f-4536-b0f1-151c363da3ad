import { NextFunction, Request, Response } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import { URL } from 'url'

const ALLOWED_GET_PARAMS = [
  'method',
  'agent',
  'verb',
  'activity',
  'registration',
  'related_activities',
  'related_agents',
  'since',
  'until',
  'limit',
  'ascending',
  'statementId',
  'voidedStatementId',
  'format',
  'attachments',
  'offset'
]

/**
 * Validates that the incoming query string parameters
 */
export default function validateQueryString(req: Request, res: Response, next: NextFunction) {
  const searchParams = new URL(req.originalUrl, 'http://localhost').searchParams
  const method = searchParams.get('method')
  if (req.method === 'GET' || method?.toLowerCase() === 'get') {
    // processing a get request
    const unAllowedParams = Object.keys(req.query).filter(key => !ALLOWED_GET_PARAMS.includes(key))

    if (unAllowedParams.length > 0) {
      res.status(BAD_REQUEST).send(`The get statements request contained unexpected parameters: ${unAllowedParams.join(', ')}`)
      return
    }

    // validate the query string params
    if (req.query.statementId || req.query.voidedStatementId) {
      if (req.query.statementId && req.query.voidedStatementId) {
        // The LRS MUST reject with an 400 Bad Request error any requests to this
        // resource which contain both statementId and voidedStatementId parameters
        res.status(BAD_REQUEST).send('Cannot request statement by statementId and voidedStatementId')
        return
      }

      if (
        req.query.agent ||
        req.query.verb ||
        req.query.activity ||
        req.query.registration ||
        req.query.related_activities ||
        req.query.related_agents ||
        req.query.since ||
        req.query.until ||
        req.query.limit ||
        req.query.ascending
      ) {
        // THE LRS MUST reject with an 400 Bad Request error any requests to this
        // resource which contain statementId or voidedStatementId parameters, and
        // also contain any other parameters besides "attachments" or "format"
        res.status(BAD_REQUEST).send('Requests to this resource which contains a statementId or voidedStatementId, and also contain any other parameters besides "attachments" or "format" is not allowed.')
        return
      }
    }

  }

  next()
}
