import { expect } from 'chai'
import Sinon from 'sinon'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import middleware from './post-state.middleware.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, OK } = httpStatus
import * as getOrCreateAgent from '../../../../../services/mssql/agent/get-or-create.service.js'
import AgentModel from '../../../../../models/agents.model.js'
import ActivityStateModel from '../../../../../models/activity-state.model.js'
import * as getActivityStateService from '../../../../../services/mssql/activity-state/get.service.js'

describe('Middleware: Validate POST activity state request', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  it('should return bad request when a query param other than (activityId, agent, stateId, registration) is provided', () => {
    const mock = httpMocks.createMocks({ query: { foo: 'bar', baz: 'baz' } })
    const mock2 = httpMocks.createMocks({ query: { agent: 'test', activityId: 'test', stateId: 'test', registration: 'test', foo: 'bar' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('The post activity state request contained unexpected parameters: foo, baz')
    expect(next.called).to.be.false

    middleware(mock2.req, mock2.res, next)
    expect(mock2.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock2.res._getData()).to.contain('The post activity state request contained unexpected parameters: foo')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activity id is missing', () => {
    const mock = httpMocks.createMocks({ query: { agent: 'test' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Error -- activity_state - method = POST, but activityId parameter is missing.')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activity id is malformed', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'test' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should return bad request when the state id is missing', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Error -- activity_state - method = POST, but stateId parameter is missing.')
    expect(next.called).to.be.false
  })

  it('should return bad request when the registration param is malformed', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', stateId: '1', registration: '123' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent param is missing', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', stateId: '1' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Error -- activity_state - method = POST, but agent parameter is missing.')
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent param is not a json object', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', stateId: '1', agent: 'test-1' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Agent query param is not valid')
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent param is not a json object', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', stateId: '1', agent: '{"mbox": "<EMAIL>"}' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should return bad request when the content type is application/json and the body is empty', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', stateId: '1', agent: '{"mbox": "mailto:<EMAIL>"}' }, body: {}, headers: { 'content-type': 'application/json' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  xit('should call next when the request is valid', async () => {
    const mock = httpMocks.createMocks({
      query: {
        activityId: 'http:example.com/activities/1',
        stateId: '1',
        agent: '{"mbox": "mailto:<EMAIL>"}'
      },
      headers: { "content-type": 'application/json' },
      body: { foo: 'bar' }
    })

    const agentStub = Sinon.stub(getOrCreateAgent, "default")
    agentStub.returns(Promise.resolve(new AgentModel(undefined, { ID: 'test xxx' })))

    const getActivityStateStub = Sinon.stub(getActivityStateService, 'getActivityStateService')
    getActivityStateStub.returns(Promise.resolve(new ActivityStateModel({
      Etag: 'test xxx',
      ModifiedOn: new Date(),
      FileID: 'test-file',
      ContentType: 'application/json'
    })))

    const next = Sinon.spy()
    await middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(OK)
    expect(next.called).to.be.true
  })
})