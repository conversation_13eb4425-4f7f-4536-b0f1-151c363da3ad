import mssql, { getRows } from '@lcs/mssql-utility'
import { Attachment, AttachmentsTableName } from '@tess-f/sql-tables/dist/lrs/attachments.js'
import StatementAttachment from '../../../models/attachment.model.js'

export default async function getStatementAttachments(statementId: string): Promise<StatementAttachment[]> {
  const attachments = await getRows<Attachment>(AttachmentsTableName, mssql.getPool().request(), { StatementId: statementId })
  return attachments.map(attachment => new StatementAttachment(attachment))
}
