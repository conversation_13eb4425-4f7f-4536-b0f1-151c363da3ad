import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getCourseActivitiesForAgent from '../../../../services/mssql/activities/get-course-activities-for-agent.service.js'
import getCompletedStatementsForAgentAndActivity from '../../../../services/mssql/statements/get-completed-for-agent-and-activity.service.js'
import moment from 'moment'
import { ActivityJSON } from '../../../../models/activity.model.js'
import { DB_Errors } from '@lcs/mssql-utility'
import formatDuration from '../../../../utils/format-duration.js'
import { getErrorMessage , httpLogTransformer} from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Sessions-For-Agent', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    // TODO: add pagination options
    const courseActivities = await getCourseActivitiesForAgent(req.params.id)
    log('info', 'Successfully fetched course activities for agent', { req, count: courseActivities.activities.length, total: courseActivities.totalRecords, success: true })

    const activities = await Promise.all(courseActivities.activities.map(async activity => {
      try {
        const statements = await getCompletedStatementsForAgentAndActivity(req.params.id, activity.fields.id)
        const sessionActivity: {
          activity: ActivityJSON,
          avgDuration: string,
          sessions: { duration: string, id?: string, timestamp?: string }[]
        } = {
          activity: activity.fields,
          avgDuration: 'Unknown',
          sessions: []
        }
        let runningDuration = 0
        let activityCount = 0
        statements.forEach(statement => {
          const session = {
            duration: 'Unknown',
            timestamp: statement.fields.timestamp,
            id: statement.fields.context?.registration
          }
          if (statement.fields.result && statement.fields.result.duration && moment.duration(statement.fields.result.duration).isValid()) {
            const duration = moment.duration(statement.fields.result.duration)
            runningDuration += duration.asMilliseconds()
            session.duration = formatDuration(duration)
            activityCount++
          }
          sessionActivity.sessions.push(session)
        })
        if (runningDuration > 0 && activityCount > 0) {
          const averageDuration = moment.duration(runningDuration / activityCount)
          sessionActivity.avgDuration = formatDuration(averageDuration)
        }
        return sessionActivity
      } catch (error) {
        if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
          return {
            activity: activity.fields,
            avgDuration: '00:00',
            sessions: []
          }
        } else {
          throw error
        }
      }
    }))
    res.json({
      totalRecords: courseActivities.totalRecords,
      activities
    })
  } catch (error) {
    log('error', 'Failed to get session information for agent', { errorMessage: getErrorMessage(error), req, success: false })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
