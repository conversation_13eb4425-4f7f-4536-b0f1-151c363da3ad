import logger from '@lcs/logger'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import AgentRelationship from '../../../models/agent-relationships.model.js'
import AgentModel, { Agent<PERSON>son } from '../../../models/agents.model.js'
import createAgent from './create.service.js'
import getAgentService from './get.service.js'
import createGroupMember from './member/create-group-member.service.js'
import { Agent } from '@tess-f/sql-tables/dist/lrs/agent.js'

const log = logger.create('Service-MSSQL.get-or-create-agent')

export default async function getOrCreateAgent(agent: AgentModel): Promise<AgentModel> {
  const search: Partial<Agent> = {}
  let hasSearch = false
  if (agent.fields.mbox) {
    search.Mbox = agent.fields.mbox
    hasSearch = true
  }
  if (agent.fields.mbox_sha1sum) {
    search.MboxSHA1Sum = agent.fields.mbox_sha1sum
    hasSearch = true
  }
  if (agent.fields.openid) {
    search.OpenID = agent.fields.openid
    hasSearch = true
  }
  if (agent.fields.account && agent.fields.account.homePage && agent.fields.account.name) {
    search.AccountHomePage = agent.fields.account.homePage
    search.AccountName = agent.fields.account.name
    hasSearch = true
  }

  if (hasSearch) {
    try {
      return await getAgentService(search)
    } catch (error) {
      if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
        throw error
      } else {
        log('verbose', 'Agent not found, creating', { agent: agent.fields })
        // we didn't find the agent in the database let's create it
        const created = await createAgent(agent)
        if (created.fields.objectType === 'Group' && agent.fields.member && agent.fields.member.length > 0) {
          // this is a group we need to add the group members
          log('verbose', 'Creating group members for new agent group', { agent: agent.fields })
          const members = await createMembers(agent.fields.member, created.ID)
          created.attachMembers(members)
        }
        return created
      }
    }
  } else if (agent.fields.member !== undefined) {
    log('debug', 'No agent search found, creating anonymous group', { agent: agent.fields })
    // only way we don't have a search is that this is an anonymous group
    // every statement with an anonymous group get's a fresh group no matter who is in the group
    const created = await createAgent(agent)
    // now we have our anonymous group we need to add the members
    const members = await createMembers(agent.fields.member, created.ID)
    created.attachMembers(members)
    return created
  } else {
    log('warn', 'No agent search criteria found and agent does not have members', { agent: agent.fields })
    throw new Error('Invalid agent')
  }
}

async function createMembers(members: AgentJson[], groupID: string): Promise<AgentModel[]> {
  return Promise.all(members.map(async member => {
    const newMember = await getOrCreateAgent(new AgentModel(member))
    await createGroupMember(new AgentRelationship({
      MemberID: newMember.ID,
      AgentID: groupID
    }))
    return newMember
  }))
}
