import { Table } from '@lcs/mssql-utility'
import {
  SubStatementCategoryContextActivity,
  SubStatementCategoryContextActivityFields,
  SubStatementCategoryContextActivityTableName
} from '@tess-f/sql-tables/dist/lrs/sub-statement-category-context-activity.js'

export default class SubStatementCategoryContextActivityModel extends Table<SubStatementCategoryContextActivity, SubStatementCategoryContextActivity> {
  public fields: SubStatementCategoryContextActivity

  constructor(fields: SubStatementCategoryContextActivity) {
    super(SubStatementCategoryContextActivityTableName, [
      SubStatementCategoryContextActivityFields.ActivityID,
      SubStatementCategoryContextActivityFields.StatementID
    ])
    this.fields = fields
  }

  public importFromDatabase(record: SubStatementCategoryContextActivity): void {
    this.fields = record
  }

  public exportJsonToDatabase(): SubStatementCategoryContextActivity {
    return this.fields
  }
}
