import mssql, { getRows } from '@lcs/mssql-utility'
import AgentModel from '../../../../models/agents.model.js'
import { AgentRelationship, AgentRelationshipTableName } from '@tess-f/sql-tables/dist/lrs/agent-relationships.js'
import { Agent, AgentTableName } from '@tess-f/sql-tables/dist/lrs/agent.js'

export default async function getGroupMembers(AgentID: string): Promise<AgentModel[]> {
  const pool = mssql.getPool()
  const res = await getRows<AgentRelationship>(AgentRelationshipTableName, pool.request(), { AgentID })
  return Promise.all(res.map(async agentRelationship => {
    const agentResponse = await getRows<Agent>(AgentTableName, pool.request(), { ID: agentRelationship.MemberID })
    return new AgentModel(undefined, agentResponse[0])
  }))
}
