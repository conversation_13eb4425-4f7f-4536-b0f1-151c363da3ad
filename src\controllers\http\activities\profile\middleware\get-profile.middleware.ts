import { Request, Response, NextFunction } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import { validateIri } from '../../../../../utils/validate-data-type.js'
import { validateTimestamp } from '../../../../../utils/validate-time.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('HTTP-Middleware.Get-Activity-Profile', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction) {
  const validParams = ['activityId', 'profileId', 'since']
  const badParams: Array<string> = []
  Object.keys(req.query).forEach(key => {
    if (!validParams.includes(key)) {
      badParams.push(key)
    }
  })

  if (badParams.length > 0) {
    log('warn', 'Failed to parse request: unexpected parameter', { success: false, badParams })
    res.status(BAD_REQUEST).send(`The get activity profile request contained unexpected parameters: ${badParams.join(', ')}`)
    return
  }

  if (!req.query.activityId) {
    log('warn', 'Failed to parse request: activityId parameter is missing', { success: false })
    res.status(BAD_REQUEST).send(`Error -- activity_profile - method = ${req.method}, but activityId parameter is missing.`)
    return
  }

  try {
    validateIri(req.query.activityId.toString(), 'activityId')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'invalid activityId iri: ', { success: false, errorMessage })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  if (req.query.since) {
    try {
      validateTimestamp(req.query.since.toString())
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      log('warn', 'Since parameter was not a valid RFC3339 timestamp', { success: false, errorMessage })
      res.status(BAD_REQUEST).send(`Since parameter was not a valid RFC3339 timestamp`)
      return
    }
  }

  next()
}