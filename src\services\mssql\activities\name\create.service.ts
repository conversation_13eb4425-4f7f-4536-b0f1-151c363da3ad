import mssql, { addRow } from '@lcs/mssql-utility'
import ActivityNameModel from '../../../../models/activity-names.model.js'
import { ActivityName } from '@tess-f/sql-tables/dist/lrs/activity-name.js'

export default async function createActivityDefinitionName(name: ActivityNameModel): Promise<ActivityNameModel> {
  const pool = mssql.getPool()
  const record = await addRow<ActivityName>(pool.request(), name)
  return new ActivityNameModel(record)
}
