import { Request, Response, NextFunction } from 'express'
import sessionAuthority from '@lcs/session-authority'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { UNAUTHORIZED } = httpStatus
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import { authenticateUser } from '@tess-f/id-mgmt-npm/dist/amqp/authenticate-user.js'
import settings from '../../../config/settings.js'
import { shouldAllowAlternateRequestSyntax, isUsingAlternateRequestSyntax } from '../../../utils/xapi-version.utils.js'

const log = logger.create('Middleware.check-session', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction) {
  try {
    if (isUsingAlternateRequestSyntax(req) && shouldAllowAlternateRequestSyntax(req) && req.body.Authorization && !req.headers.authorization) {
      req.headers.authorization = req.body.Authorization
    }
    const authResult = await sessionAuthority.authenticate(req, res)

    if (authResult.result === 'valid') {
      log('verbose', 'Retrieved session', { session: authResult.session, success: true, req })
      next()
    } else {
      log('info', 'Invalid session', { ipAddress: authResult.ip, success: false, req })
      res.sendStatus(UNAUTHORIZED)
    }
  } catch (error) {
    let errorMessage = getErrorMessage(error)
    if (errorMessage === 'invalid bearer token in request header' && req.headers.authorization) {
      // we have an invalid bearer token, we may have a basic auth token
      const authorizationParts = req.headers.authorization.split(' ')
      if (authorizationParts.length === 2 && authorizationParts[0].toLowerCase() === 'basic') {
        // we have a basic auth, let's ask identity management to verify this for us
        // first let's get the user name and password out
        const decoded = Buffer.from(authorizationParts[1], 'base64').toString('utf-8')
        if (decoded.indexOf(':') >= 0) {
          // we have a valid formatted credential
          const credentials = decoded.split(':')
          if (credentials.length === 2 && credentials[0].length > 0 && credentials[1].length > 0) {
            // valid credential values, authenticate
            try {
              const userResponse = await authenticateUser(settings.amqp.serviceQueues.identityManagement, { username: credentials[0], password: credentials[1] }, settings.amqp.rpc_timeout)
              if (userResponse.success) {
                log('info', 'Validated basic auth', { success: true, req })
                req.session = { userId: userResponse.data!.id!, sessionId: '' }
                next()
                return
              }
            } catch (error) {
              // failed to authenticate
              errorMessage = getErrorMessage(error)
              log('error', 'Failed to authenticate user with basic auth token', { success: false, req })
            }
          }
        }
      }
    }
    log('error', 'Failed to validate session', { errorMessage, success: false, req })
    res.sendStatus(UNAUTHORIZED)
  }
}
