// PUT, update State Resource
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { File } from 'formidable'
import { createHash } from 'crypto'
import getOrCreateAgent from '../../../../services/mssql/agent/get-or-create.service.js'
import AgentModel from '../../../../models/agents.model.js'
import getOrCreateActivityStateService from '../../../../services/mssql/activity-state/get-or-create.service.js'
import ActivityStateModel from '../../../../models/activity-state.model.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NO_CONTENT, PRECONDITION_FAILED, CONFLICT } = httpStatus
import fsExtra from 'fs-extra'
import parseForm from '../../../../utils/parse-form.js'
import { getErrorMessage, getErrorStackTrace, httpLogTransformer } from '@tess-f/backend-utils'
import deleteFile from '../../../../services/amqp/file/delete.service.js'
import saveFile from '../../../../services/amqp/file/save.service.js'
import updateActivityState from '../../../../services/mssql/activity-state/update.service.js'
import { EtagPreconditionFail } from '../../../../utils/error.utils.js'
import { checkModificationConditions, RESOURCE_DOES_NOT_EXIST, MISSING_ETAG_INFO, RESOURCE_DETECTED_ERROR, NO_RESOURCE_MATCH} from '../../../../utils/etag.utils.js'
import deleteActivityState from '../../../../services/mssql/activity-state/delete.service.js'

const log = logger.create('HTTP-Controller.Put-Activity-State', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    let file: File | undefined
    let base64Contents: string | undefined
    let state: string | undefined
    const hasher = createHash('sha256')
    let etag: string | undefined

    if (req.is('multipart/*')) {
      // get the file from the request
      const { files } = await parseForm(req)
      let fileCount = 0
      for (const fileKey in files) {
        if (files[fileKey]!.length > 1) {
          fileCount += files[fileKey]!.length
        }
        fileCount++
        file = files[fileKey]![0]
      }
      if (fileCount > 1) {
        // cannot have more than 1 file
        res.status(BAD_REQUEST).send('Invalid form: request cannot contain more than 1 file')
        // remove the temp uploaded files
        for (const fileKey in files) {
          for (const f of files[fileKey]!) {
            await fsExtra.remove(f.filepath)
          }
        }
        return
      }

      if (file) {
        base64Contents = await fsExtra.readFile(file.filepath, 'base64')
        await fsExtra.remove(file.filepath)
        hasher.update(base64Contents)
        etag = hasher.digest('hex')
      }

    } else if (req.is('application/octet-stream')) {
      // payload is a buffer
      state = (<Buffer>req.body).toString('utf-8')
      hasher.update(state)
      etag = hasher.digest('hex')
    } else {
      state = req.is('text/plain') ? req.body as string : JSON.stringify(req.body)
      hasher.update(state)
      etag = hasher.digest('hex')
    }

    // get or create the agent
    const agent = await getOrCreateAgent(new AgentModel(JSON.parse(req.query.agent!.toString())))

    // get or create the activity state record
    const { activityState, created } = await getOrCreateActivityStateService(new ActivityStateModel({
      ID: req.query.stateId!.toString(),
      AgentID: agent.ID,
      ActivityID: req.query.activityId!.toString(),
      ContentType: req.headers['content-type'],
      RegistrationID: req.query.registration?.toString()
    }))
    // The State Resource will permit PUT, POST and DELETE requests without concurrency headers, since state conflicts are unlikely
    try {
      checkModificationConditions(req, activityState.fields.Etag, created, true, true)
    } catch (error) {
      if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DOES_NOT_EXIST) {
        log('warn', 'Failed to create activity state: ETag Precondition failed with resource does not exist. Removing created state record', { success: false })
        await deleteActivityState(activityState.fields.ID!, activityState.fields.AgentID!, activityState.fields.ActivityID!, activityState.fields.RegistrationID)
        if (activityState.fields.FileID) {
          await deleteFile([activityState.fields.FileID])
        }
      }
      throw error
    }

    activityState.fields.Etag = etag

    if (!created && activityState.fields.FileID) {
      // we did not create the record, and it had a file
      // delete the old file
      log('verbose', 'Activity state had a file previously, removing old file')
      try {
        await deleteFile([activityState.fields.FileID])
      } catch (error) {
        // something went wrong the file is now orphaned
        log('error', 'Failed to delete previous state file', { fileId: activityState.fields.FileID, errorMessage: getErrorMessage(error), req })
      }
    }

    if (file && base64Contents) {
      // we are updating the file
      activityState.fields.State = null
      activityState.fields.ContentType = file.mimetype ?? 'text/plain'
      log('verbose', 'Activity state is file, uploading to FDS')
      activityState.fields.FileID = await saveFile(base64Contents, file.originalFilename ?? 'state-file.txt', activityState.fields.ContentType)
    } else if (state) {
      activityState.fields.State = state
      activityState.fields.ContentType = req.headers['content-type'] ?? 'application/json'
    }

    if (req.header('updated') && Date.parse(req.header('updated')!)) {
      activityState.fields.ModifiedOn = new Date(req.header('updated')!)
    } else {
      // For updates, ensure the new timestamp is always greater than the existing one
      const now = new Date()
      const existingTimestamp = activityState.fields.ModifiedOn

      if (!created && existingTimestamp) {
        // For existing records, always ensure the new timestamp is at least 1ms newer
        const minNewTime = existingTimestamp.getTime() + 1
        const newTime = Math.max(now.getTime(), minNewTime)
        activityState.fields.ModifiedOn = new Date(newTime)
        log('verbose', 'Set incremented timestamp for state update', {
          existingTime: existingTimestamp.getTime(),
          newTime: newTime,
          nowTime: now.getTime(),
          stateId: activityState.fields.ID,
          req
        })
      } else {
        // For new records, use current time
        activityState.fields.ModifiedOn = now
        log('verbose', 'Set new timestamp for state creation', {
          newTime: now.getTime(),
          stateId: activityState.fields.ID,
          created,
          req
        })
      }
    }

    const updatedActivityState = await updateActivityState(activityState)
    log('info', 'Successfully updated activity state', {
      stateId: updatedActivityState.fields.ID,
      activityId: updatedActivityState.fields.ActivityID,
      agentId: updatedActivityState.fields.AgentID,
      registrationId: updatedActivityState.fields.RegistrationID,
      modifiedOnBeforeUpdate: activityState.fields.ModifiedOn?.getTime(),
      modifiedOnAfterUpdate: updatedActivityState.fields.ModifiedOn?.getTime(),
      success: true, req
    })

    // Set Last-Modified header to indicate when the document was updated
    if (updatedActivityState.fields.ModifiedOn) {
      const lastModifiedHeader = updatedActivityState.fields.ModifiedOn.toISOString()
      res.setHeader('Last-Modified', lastModifiedHeader)
      log('verbose', 'Set Last-Modified header in PUT response', {
        lastModified: lastModifiedHeader,
        timestamp: updatedActivityState.fields.ModifiedOn.getTime(),
        stateId: updatedActivityState.fields.ID,
        req
      })
    }
    res.sendStatus(NO_CONTENT)
  } catch (error) {
    if (error instanceof EtagPreconditionFail && error.message === MISSING_ETAG_INFO) {
      log('warn', 'Failed to update activity state: missing etag header', { errorMessage: error.message, success: false })
      res.status(BAD_REQUEST).send(error.message)

    } else if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DOES_NOT_EXIST) {
      log('warn', 'Failed to update activity state: resource does not exist', { errorMessage: error.message, success: false })
      res.status(PRECONDITION_FAILED).send(error.message)

    } else if (error instanceof EtagPreconditionFail && error.message === RESOURCE_DETECTED_ERROR) {
      log('warn', 'Failed to update activity state: Resource already exists', { errorMessage: error.message, success: false })
      res.status(PRECONDITION_FAILED).send(error.message)

    } else if (error instanceof EtagPreconditionFail && error.message === NO_RESOURCE_MATCH) {
      log('warn', 'No resources matched your etag precondition', { errorMessage: error.message, success: false })
      res.status(PRECONDITION_FAILED).send(error.message)

    } else {
      log('error', 'Failed to save activity state: unknown error', { errorMessage: getErrorMessage(error), errorStack: getErrorStackTrace(error), success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}