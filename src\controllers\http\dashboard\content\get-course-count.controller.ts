import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getCourseActivityCount from '../../../../services/mssql/activities/get-course-activity-count.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Course-Count', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const count = await getCourseActivityCount()
    log('info', 'Successfully retrieved the course activity count', { count, req, success: true })
    res.json(count)
  } catch (error) {
    log('error', 'Failed to retrieve the course activity count', { errorMessage: getErrorMessage(error), req, success: false })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
