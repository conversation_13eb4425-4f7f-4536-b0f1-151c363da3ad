import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { StatementTableName as SubStatementsTable } from '@tess-f/sql-tables/dist/lrs/sub-statement.js'
import VerbModel from '../../../models/verb.model.js'
import { Verb, VerbTableName } from '@tess-f/sql-tables/dist/lrs/verb.js'
import getVerbDisplay from './display/get.service.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'

// TODO: add in log messages

/**
 * Gets the top {limit} most used verbs in the system
 * @param limit number of verbs to get
 */
export default async function getMostUsedVerbsForAgent(agentID: string, limit = 3): Promise<VerbModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('agentID', agentID)

  // Calculate and fetch the most used verb
  const query = `
    SELECT TOP ${limit} [${VerbTableName}].*, COUNT([${StatementTableName}].[ID]) + COUNT([${SubStatementsTable}].[ID]) AS [UseCount]
    FROM [${VerbTableName}]
      LEFT JOIN [${StatementTableName}] ON [${StatementTableName}].[VerbID] = [${VerbTableName}].[ID] AND [${StatementTableName}].[ActorID] = @agentID
      LEFT JOIN [${SubStatementsTable}] ON [${SubStatementsTable}].[VerbID] = [${VerbTableName}].[ID] AND [${SubStatementsTable}].[ActorID] = @agentID
    GROUP BY [${VerbTableName}].[ID]
    ORDER BY [UseCount] DESC
  `

  const results = await request.query<Verb>(query)

  if (results.recordset.length >= 1) {
    // we found the most used verb(s)
    const verbs = results.recordset.map(verb => new VerbModel(undefined, verb))

    await Promise.all(verbs.map(async verb => {
      try {
        // try to add it's display map if it has one
        verb.attachDisplay(await getVerbDisplay(verb.fields.id))
      } catch (error) {
        // if the error is not found in db this verb doesn't have a lang map display
        if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
          // something when wrong
          throw error
        }
      }
    }))

    return verbs
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
