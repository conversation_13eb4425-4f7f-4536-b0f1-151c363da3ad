import mssql, { updateRow } from '@lcs/mssql-utility'
import ActivityStateModel from '../../../models/activity-state.model.js'
import { ActivityState } from '@tess-f/sql-tables/dist/lrs/activity-state.js'

export default async function updateActivityState(activityState: ActivityStateModel): Promise<ActivityStateModel> {
  const identityFields: Partial<ActivityState> = {
    ID: activityState.fields.ID,
    AgentID: activityState.fields.AgentID,
    ActivityID: activityState.fields.ActivityID
  }

  if (activityState.fields.RegistrationID) {
    identityFields.RegistrationID = activityState.fields.RegistrationID
  }

  const updated = await updateRow<ActivityState>(mssql.getPool().request(), activityState, identityFields)
  return new ActivityStateModel(updated[0])
}
