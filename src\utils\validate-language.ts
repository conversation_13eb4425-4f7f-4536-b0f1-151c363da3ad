export function validateLanguage(lang: string, field: string): void {
  // Validate a language code. (line 125)
  const langParts: string[] = lang.split('-')
  langParts.forEach(part => {
    const regExp = new RegExp(/^[A-Za-z]{2,8}$/)
    if (!part || !regExp.test(part)) {
      throw new Error(`language ${lang} is not valid in ${field}`)
    }
  })
}

export function validateLanguageMap(langMap: any, field: string): void {
  // This function will validate language tag based on RFC 5646. (line 141)
  Object.keys(langMap).forEach(key => {
    validateLanguage(key, field)
  })
}
