import logger from '@lcs/logger'
import { expect } from 'chai'
import deleteController from './delete.controller.js'
import * as getOrCreateAgentService from '../../../../services/mssql/agent/get-or-create.service.js'
import * as deleteFileService from '../../../../services/amqp/file/delete.service.js'
import * as deleteActivityStateService from '../../../../services/mssql/activity-state/delete.service.js'
import Sinon from 'sinon'
import AgentModel from '../../../../models/agents.model.js'
import ActivityStateModel from '../../../../models/activity-state.model.js'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
const { NO_CONTENT, INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus
import * as getActivityStateService from '../../../../services/mssql/activity-state/get.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'

xdescribe('HTTP: Delete Activity State Controller', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  beforeEach(() => {
    const agentStub = Sinon.stub(getOrCreateAgentService, 'default')
    agentStub.returns(Promise.resolve(new AgentModel(undefined, { ID: 'test-1' })))
  })

  it('should delete the activity state', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        stateId: '1',
        activityId: '1',
        registration: '1'
      }
    })

    const getStateStub = Sinon.stub(getActivityStateService, 'getActivityStateService')
    getStateStub.returns(Promise.resolve(new ActivityStateModel({
      Etag: 'test',
      ModifiedOn: new Date(),
      FileID: 'test-file'
    })))

    const deleteActivitySub = Sinon.stub(deleteActivityStateService, 'default')
    deleteActivitySub.returns(Promise.resolve(1))

    const deleteFileStub = Sinon.stub(deleteFileService, 'default')
    deleteFileStub.returns(Promise.resolve(1))

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
    expect(deleteActivitySub.called).to.be.true
    expect(deleteFileStub.called).to.be.true
    expect(mocks.res.getHeader('ETag')).to.equal('test')
  })

  it('should return Not Found when the activity state does not exist', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        stateId: '1',
        activityId: '1',
        registration: '1'
      }
    })

    const getStateStub = Sinon.stub(getActivityStateService, 'getActivityStateService')
    getStateStub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))

    const deleteActivitySub = Sinon.stub(deleteActivityStateService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
    expect(deleteActivitySub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })

  it('should return Internal Server Error when the activity state service encounters an error', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        stateId: '1',
        activityId: '1',
        registration: '1'
      }
    })

    const getStateStub = Sinon.stub(getActivityStateService, 'getActivityStateService')
    getStateStub.returns(Promise.reject(new Error('Something Bad')))

    const deleteActivitySub = Sinon.stub(deleteActivityStateService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
    expect(deleteActivitySub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })

  it('should delete the requested activity states', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        activityId: '1',
        registration: '1'
      }
    })

    const getStateStub = Sinon.stub(getActivityStateService, 'getActivitiesStateService')
    getStateStub.returns(Promise.resolve([
      new ActivityStateModel({
        ID: 'test-1',
        AgentID: 'test-agent',
        ActivityID: 'test-123',
        RegistrationID: 'test-reg',
        FileID: 'test-1'
      }),
      new ActivityStateModel({
        ID: 'test-2',
        AgentID: 'test-agent',
        ActivityID: 'test-123'
      })
    ]))

    const deleteActivitySub = Sinon.stub(deleteActivityStateService, 'default')
    deleteActivitySub.returns(Promise.resolve(1))

    const deleteFileStub = Sinon.stub(deleteFileService, 'default')
    deleteFileStub.returns(Promise.resolve(1))

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
    expect(deleteActivitySub.called).to.be.true
    expect(deleteFileStub.called).to.be.true
  })


  it('should return Not Found when no activity state exist for the search', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        activityId: '1',
        registration: '1'
      }
    })

    const getStateStub = Sinon.stub(getActivityStateService, 'getActivitiesStateService')
    getStateStub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))

    const deleteActivitySub = Sinon.stub(deleteActivityStateService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
    expect(deleteActivitySub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })

  it('should return Internal Server Error when the get activities state service encounters an error', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        activityId: '1',
        since: (new Date()).toISOString()
      }
    })

    const getStateStub = Sinon.stub(getActivityStateService, 'getActivitiesStateService')
    getStateStub.returns(Promise.reject(new Error('Something Bad')))

    const deleteActivitySub = Sinon.stub(deleteActivityStateService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
    expect(deleteActivitySub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })
})