import { Router } from 'express'
import createActivityStateController from './create.controller.js'
import getActivityStateController from './get.controller.js'
import deleteActivityStateController from './delete.controller.js'
import updateActivityStateController from './update.controller.js'
import validateGetStateMiddleware from './middleware/get-state.middleware.js'
import validatePostStateMiddleware from './middleware/post-state.middleware.js'
import validatePutStatementRequest from './middleware/validate-put-state.middleware.js'
import validateDeleteStateMiddleware from './middleware/delete-state.middleware.js'

const router = Router()

router.post('/', validatePostStateMiddleware, createActivityStateController)
router.get('/', validateGetStateMiddleware, getActivityStateController)
router.delete('/', validateDeleteStateMiddleware, deleteActivityStateController)
router.put('/', validatePutStatementRequest, updateActivityStateController)

export default router
