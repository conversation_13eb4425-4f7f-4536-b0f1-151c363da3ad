import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getAgentWithMostActivitiesService from '../../../../services/mssql/agent/get-agent-with-most-activities.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Dashboard-Get-Most-Active-Agent', httpLogTransformer)

export default async function getMostActiveAgentController(req: Request, res: Response) {
  try {
    const mostActiveAgent = await getAgentWithMostActivitiesService()
    log('info', 'Successfully retrieved the most active agent', { req, id: mostActiveAgent.ID, success: true })
    res.json(mostActiveAgent.fields)
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get most active agent because none was found in the database', { req, success: false })
      res.status(httpStatus.NOT_FOUND).send('Could not find the most active agent')
    } else {
      log('error', 'Failed to get the most active agent', { errorMessage: getErrorMessage(error), req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
