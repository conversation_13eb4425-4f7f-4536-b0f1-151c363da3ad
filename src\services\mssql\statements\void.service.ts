import mssql from '@lcs/mssql-utility'
import { StatementFields, StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'

export default async function voidStatement(statementId: string): Promise<void> {
  const request = mssql.getPool().request()
  request.input('statementId', statementId)
  await request.query(`
    UPDATE [${StatementTableName}]
    SET [${StatementFields.Voided}] = 1
    WHERE [${StatementFields.ID}] = @statementId
  `)
}
