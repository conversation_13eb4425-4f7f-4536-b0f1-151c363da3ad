import logger from '@lcs/logger'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import getActivityService from '../../../../services/mssql/activities/get.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Activity-By-ID', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    if (!req.query || !req.query.activity) {
      log('warn', 'Failed to fetch activity, no ID provided', { req, success: false })
      res.status(httpStatus.BAD_REQUEST).send('No ID provided')
      return
    }
    const activity = await getActivityService(req.query.activity.toString())
    log('info', 'Successfully retrieved activity', { id: activity.fields.id, req, success: true })
    res.json(activity.fields)
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get activity because none was found in the database', { req, id: req.query.activityId, success: false })
      res.status(httpStatus.NOT_FOUND).send('Activity not found')
    } else {
      log('error', 'Failed to get activity', { errorMessage: getErrorMessage(error), req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
