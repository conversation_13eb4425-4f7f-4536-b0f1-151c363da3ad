import { Table } from '@lcs/mssql-utility'
import { CategoryContextActivity, CategoryContextActivityFields, CategoryContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/category-context-activity.js'

export default class CategoryContextActivityModel extends Table<CategoryContextActivity, CategoryContextActivity> {
  public fields: CategoryContextActivity

  constructor(fields: CategoryContextActivity) {
    super(CategoryContextActivityTableName, [
      CategoryContextActivityFields.ActivityID,
      CategoryContextActivityFields.StatementID
    ])
    this.fields = fields
  }

  public importFromDatabase(record: CategoryContextActivity): void {
    this.fields = record
  }

  public exportJsonToDatabase(): CategoryContextActivity {
    return this.fields
  }
}
