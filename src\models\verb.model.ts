import { Table } from '@lcs/mssql-utility'
import VerbDisplay from './verb-display.model.js'
import { Verb, VerbFields, VerbTableName } from '@tess-f/sql-tables/dist/lrs/verb.js'

export interface VerbJson {
  id: string,
  display?: { [key: string]: string }
}

export default class VerbModel extends Table<VerbJson, Verb> {
  fields!: VerbJson

  constructor(fields?: VerbJson, record?: Verb) {
    super(VerbTableName, [
      VerbFields.ID
    ])
    if (fields) {
      this.fields = fields
    } else if (record) {
      this.importFromDatabase(record)
    } else {
      throw new Error('Must provide fields or record to construct class')
    }
  }

  importFromDatabase(record: Verb): void {
    if (!this.fields) {
      this.fields = { id: record.ID }
    } else {
      this.fields.id = record.ID
    }
  }

  exportJsonToDatabase(): Verb {
    return {
      ID: this.fields.id
    }
  }

  attachDisplay(display: VerbDisplay[]) {
    this.fields.display = display.reduce((a, v) => ({ ...a, [v.fields.Lang!]: v.fields.Display }), {})
  }

  getDisplay(): VerbDisplay[] {
    if (this.fields.display) {
      const ret: VerbDisplay[] = []
      for (const key in this.fields.display) {
        ret.push(new VerbDisplay({
          VerbID: this.fields.id,
          Lang: key,
          Display: this.fields.display[key]
        }))
      }
      return ret
    }
    return []
  }
}
