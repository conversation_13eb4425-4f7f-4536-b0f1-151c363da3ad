import { Request, Response } from 'express'
import logger from '@lcs/logger'
import { httpLogTransformer } from '@tess-f/backend-utils'
import settings from '../../config/settings.js'

const log = logger.create('HTTP-Controller.Get-About', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // Returns JSON Object containing information about this LRS, including the xAPI version supported.
  // https://github.com/adlnet/xAPI-Spec/blob/master/xAPI-Communication.md#28-about-resource
  const response: any = {
    version: settings.XAPI_VERSIONS
  }

  log('info', 'Successfully returned about resource', { req, success: true })
  res.json(response)
  
}