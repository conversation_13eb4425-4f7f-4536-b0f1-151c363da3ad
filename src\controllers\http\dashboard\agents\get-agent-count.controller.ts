import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getUniqueAgentCountService from '../../../../services/mssql/agent/get-count.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Dashboard-Get-Agent-Count', httpLogTransformer)

export default async function getAgentCountController(req: Request, res: Response) {
  try {
    const agentCount = await getUniqueAgentCountService()
    log('info', 'Successfully retrieved unique agent count', { req, count: agentCount, success: true })
    res.json(agentCount)
  } catch (error) {
    let message = 'Server Error'
    if (error instanceof Error) {
      message = error.message
      if (message === dbErrors.default.NOT_FOUND_IN_DB) {
        res.json(0)
        log('warn', 'Failed to get agent count because no records were found in the database', { req, success: false })
        return
      }
    }

    log('error', 'Failed to get agent count', { errorMessage: getErrorMessage(error), req, success: false })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
