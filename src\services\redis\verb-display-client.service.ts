import { createClient, RedisClientType } from 'redis'
import logger from '@lcs/logger'
import settings from '../../config/settings.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import VerbDisplayModel from '../../models/verb-display.model.js'
import type { VerbDisplay } from '@tess-f/sql-tables/dist/lrs/verb-display.js'

const log = logger.create('Service-Redis.client')

const VERB_DISPLAY_CLIENT = 'lrs-verb-display'
const REDIS_NOT_INITIALIZED = 'Redis client is not initialized'

/**
 * A singleton instance of Redis clients for various databases
 */
export class RedisClient {
  // eslint-disable-next-line
  private static _instance?: RedisClient
  private readonly _clients = new Map<string, RedisClientType>()

  private static async getInstance (): Promise<RedisClient> {
    if (!RedisClient._instance) {
      await RedisClient.initInstance()
    }
    return RedisClient._instance!
  }

  private static async initInstance (): Promise<void> {
    // Create client connection
    try {
      RedisClient._instance = new RedisClient()
      const client: RedisClientType = createClient({
        url: settings.redis.url,
        password: settings.redis.password,
        database: settings.redis.verbDisplayDB
      })
      await client.connect().then(() => log('info', 'Redis client connected.', { success: true }))
      client.on('error', (error: unknown) => log('error', 'Redis client error', { errorMessage: getErrorMessage(error) }))
      RedisClient._instance._clients.set(VERB_DISPLAY_CLIENT, client)
    } catch (err) {
      log('error', 'Failed to initialize redis client', { errorMessage: getErrorMessage(err) })
    }
  }

  public static async shutdown (): Promise<void> {
    const instance = await RedisClient.getInstance()
    for (const key of instance._clients.keys()) {
      const client = instance._clients.get(key)
      if (client) {
        await client.quit()
        instance._clients.delete(key)
      }
    }
    RedisClient._instance = undefined
  }

  public static async setVerbDisplay (verbId: string, verbDisplay: VerbDisplayModel[]): Promise<void> {
    const instance = await RedisClient.getInstance()
    const client = instance._clients.get(VERB_DISPLAY_CLIENT)
    if (!client) {
      log('error', REDIS_NOT_INITIALIZED, { client: VERB_DISPLAY_CLIENT, success: false })
      return
    }

    await client.set(verbId, JSON.stringify(verbDisplay.map(verbDisplay => verbDisplay.fields)))
    await client.expire(verbId, 45)
  }

  public static async getVerbDisplay (verbId: string): Promise<undefined | VerbDisplayModel[]> {
    const instance = await RedisClient.getInstance()
    const client = instance._clients.get(VERB_DISPLAY_CLIENT)
    if (!client) {
      log('error', REDIS_NOT_INITIALIZED, { client: VERB_DISPLAY_CLIENT, success: false })
      return
    }

    const result = await client.get(verbId)
    
    if (result === null) return
    log('debug', 'Successfully fetched timer status from redis', { verbId, result })
    const verbDisplays: VerbDisplay[] = JSON.parse(result)
    return verbDisplays.map(verbDisplay => new VerbDisplayModel(verbDisplay))
  }
  
  public static async deleteVerbDisplays (): Promise<void> {
    const instance = await RedisClient.getInstance()
    const client = instance._clients.get(VERB_DISPLAY_CLIENT)

    if (!client) {
      log('error', REDIS_NOT_INITIALIZED, { client: VERB_DISPLAY_CLIENT, success: false })
      return
    }

    await client.flushDb()
  }
}