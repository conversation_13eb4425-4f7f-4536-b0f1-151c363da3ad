import { Table } from '@lcs/mssql-utility'
import { ActivityState, ActivityStateFields, ActivityStateTableName } from '@tess-f/sql-tables/dist/lrs/activity-state.js'

export default class ActivityStateModel extends Table<ActivityState, ActivityState> {
  fields: ActivityState

  constructor(fields: ActivityState) {
    super(ActivityStateTableName, [
      ActivityStateFields.ID,
      ActivityStateFields.ActivityID,
      ActivityStateFields.AgentID
    ])
    this.fields = fields
  }

  importFromDatabase(record: ActivityState): void {
    this.fields = record
  }

  exportJsonToDatabase(): ActivityState {
    return this.fields
  }

}