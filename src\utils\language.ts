import { Request } from 'express'
import settings from '../config/settings.js'

export default function getLanguageFromHeader (req: Request): string[] {
  const headerLanguage = req.get('Accept_Language') ?? req.get('Accept-Language') ?? req.get('HTTP_ACCEPT_LANGUAGE')

  if (headerLanguage) {
    const langList = headerLanguage.split(',')
    if (langList.length > 1) {
      langList.forEach((lang, index) => {
        const parts = lang.split(';')
        if (parts.length === 1 && parts[0] === '*') {
          langList[index] = 'anylanguage;q=0.1'
        } else if (parts.length === 1 && parts[0] !== '*') {
          langList[index] = parts[0] + ';q=1.0'
        }
      })
      langList.sort(compareLangQuality)
      return langList.map(mapLang)
    }
    if (langList[0] === '*') {
      return ['anylanguage']
    } else {
      return langList.map(mapLang)
    }
  }
  return [settings.languageCode]
}

function mapLang (lang: string, index: number, array: string[]): string {
  return lang.indexOf(';') > -1 ? lang.substring(0, lang.indexOf(';')) : lang
}

function compareLangQuality (a: string, b: string): number {
  const aQ = Number(a.substring(a.length - 3))
  const bQ = Number(b.substring(b.length - 3))

  if (aQ > bQ) {
    return -1
  }
  return 1
}
