import { expect } from 'chai'
import Sinon from 'sinon'
import deleteService from './delete.service.js'
import * as fdsDeleteFile from '@tess-f/fds/dist/amqp/delete.js'
import logger from '@lcs/logger'

describe('AMQP Delete File Service', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  xit('should return the number files deleted on success', async () => {
    const stub = Sinon.stub(fdsDeleteFile, 'DeleteFiles')
    stub.returns(Promise.resolve({ success: true, data: { numDeleted: 3 } }))
    const numDeleted = await deleteService(['test-1', 'test-2', 'test-3'])
    expect(numDeleted).to.equal(3)
  })

  xit('should throw an error when the command does not return data', async () => {
    const stub = Sinon.stub(fdsDeleteFile, 'DeleteFiles')
    stub.returns(Promise.resolve({ success: true }))
    try {
      await deleteService(['test-1'])
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('RPC returned success but no delete count')
    }
  })

  xit('should throw an error when the command returns unsuccessful', async () => {
    const stub = Sinon.stub(fdsDeleteFile, 'DeleteFiles')
    stub.returns(Promise.resolve({ success: false }))
    try {
      await deleteService(['test-1'])
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('RPC failed to delete file(s)')
    }
  })

  xit('should throw an error when the command times out', async () => {
    const stub = Sinon.stub(fdsDeleteFile, 'DeleteFiles')
    stub.returns(Promise.reject(new Error('RPC Timeout')))
    try {
      await deleteService(['test-1'])
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('RPC Timeout')
    }
  })
})
