import UrlParam from './url-param.model.js'

export default class UrlParams {
  private readonly _params: {[key: string]: UrlParam} = {}

  private _length = 0
  public get length (): number {
    return this._length
  }

  public add (param: UrlParam): void {
    if (!this._params[param.key]) {
      this._params[param.key] = param
      this._length++
    } else {
      throw new Error('You may only add unique keys to the URL params')
    }
  }

  public remove (param: UrlParam): void {
    if (this._params[param.key]) {
      delete this._params[param.key]
      this._length--
    } else {
      throw new Error('This key does not exist for URL Params')
    }
  }

  public toString (): string {
    let urlParamsString = ''
    for (const key in this._params) {
      if (this._params[key] !== null) {
        const encodedValue = encodeURIComponent(this._params[key].value)
        urlParamsString += `${this._params[key].key}=${encodedValue}&`
      }
    }
    if (urlParamsString.length > 1) {
      // assume at least one entry
      // trim last &
      urlParamsString = urlParamsString.substring(0, urlParamsString.length - 1)
    }

    return urlParamsString
  }
}
