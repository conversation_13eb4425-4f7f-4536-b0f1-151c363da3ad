import mssql from '@lcs/mssql-utility'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'

export default async function getSessionCountForActivity(activityID: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('activity', activityID)

  const query = `
    SELECT COUNT(DISTINCT [ContextRegistration]) AS [AttemptCount]
    FROM [${StatementTableName}]
    WHERE [ObjectActivityID] = @activity
  `

  const results = await request.query<{ AttemptCount: number }>(query)

  return results.recordset.length > 0 ? results.recordset[0].AttemptCount : 0
}
