import { Request } from 'mssql'
import { StatementTableName as SUB_STATEMENT_TABLE } from '@tess-f/sql-tables/dist/lrs/sub-statement.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { CategoryContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/category-context-activity.js'
import { GroupingContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/grouping-context-activity.js'
import { OtherContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/other-context-activity.js'
import { ParentContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/parent-context-activity.js'
import { SubStatementCategoryContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/sub-statement-category-context-activity.js'
import { SubStatementGroupingContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/sub-statement-grouping-context-activity.js'
import { SubStatementOtherContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/sub-statement-other-context-activity.js'
import { SubStatementParentContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/sub-statement-parent-context-activity.js'

// Filter, only return Statements for which the Object of the Statement is an Activity with the specified id.
export default function buildActivityStatementFilter(request: Request, activity: string, relatedActivities = false): string {
  request.input('activityID', activity)

  let query = `
    [ObjectActivityID] = @activityID
  `

  if (relatedActivities) {
    //  Apply the Activity filter broadly.
    //  Include Statements for which the
    //    Object,
    //    any of the context Activities,
    //    or any of those properties in a contained SubStatement match the Activity parameter,
    //  instead of that parameter's normal behavior.
    //  Matching is defined in the same way it is for the "activity" parameter.

    // context activities
    const statementContextActivityQuery = `
      [ID] IN (
        ${getContextStatementQuery(CategoryContextActivityTableName)}
      ) OR [ID] IN (
        ${getContextStatementQuery(GroupingContextActivityTableName)}
      ) OR [ID] IN (
        ${getContextStatementQuery(OtherContextActivityTableName)}
      ) OR [ID] IN (
        ${getContextStatementQuery(ParentContextActivityTableName)}
      )
    `
    query += `OR ${statementContextActivityQuery}`

    //  sub statement:
    //    activity
    //    context activities
    query += `
      OR [ObjectSubStatementID] IN (
        SELECT [ID]
        FROM [${SUB_STATEMENT_TABLE}]
        WHERE [ObjectActivityID] = @activityID
        OR [ID] IN (
          ${getContextStatementQuery(SubStatementCategoryContextActivityTableName)}
        ) OR [ID] IN (
          ${getContextStatementQuery(SubStatementGroupingContextActivityTableName)}
        ) OR [ID] IN (
          ${getContextStatementQuery(SubStatementOtherContextActivityTableName)}
        ) OR [ID] IN (
          ${getContextStatementQuery(SubStatementParentContextActivityTableName)}
        )
      )
    `

    //  statement ref:
    //    activity
    //    context activities
    query += `
      OR [ObjectStatementRef] IN (
        SELECT [ID]
        FROM [${StatementTableName}]
        WHERE [ObjectActivityID] = @activityID
        OR ${statementContextActivityQuery}
      )
    `
  }

  return query
}

function getContextStatementQuery(tableName: string) {
  return `
    SELECT [StatementID]
    FROM [${tableName}]
    WHERE [ActivityID] = @activityID
  `
}
