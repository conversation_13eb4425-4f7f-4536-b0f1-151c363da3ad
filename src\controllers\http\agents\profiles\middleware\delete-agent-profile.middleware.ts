import { Request, Response, NextFunction } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import validateAgent from '../../../../../services/validators/agent/validate.service.js'

const log = logger.create('HTTP-Controller.Delete-Agent-Profile', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction) {
  const validParams = ['agent', 'profileId']
  const badParams: Array<string> = []
  Object.keys(req.query).forEach(key => {
    if (!validParams.includes(key)) {
      badParams.push(key)
    }
  })

  if (badParams.length > 0) {
    log('warn', 'Failed to parse request: unexpected parameter', { success: false, badParams, req })
    res.status(BAD_REQUEST).send(`The get agent profile request contained unexpected parameters: ${badParams.join(', ')}`)
    return
  }

  if (!req.query.agent) {
    log('warn', 'Failed to parse request: agent parameter is missing', { success: false, req })
    res.status(BAD_REQUEST).send(`Error -- agent_profile - method = ${req.method}, but agent parameter is missing.`)
    return
  }

  let agent: any
  const agentParam = req.query.agent.toString()
  try {
    agent = JSON.parse(agentParam)
  } catch (error) {
    log('warn', 'Failed to get agents: malformed agent query param, failed to parse as JSON', { success: false, req })
    res.status(BAD_REQUEST).send('agent query param is not valid')
    return
  }

  try {
    validateAgent(agent, 'Agent param')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'Failed to get agent: malformed agent query param, failed to parse as JSON', { success: false, req })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  if (!req.query.profileId) {
    log('warn', 'Failed to parse request: profileId parameter is missing', { success: false, req })
    res.status(BAD_REQUEST).send(`Error -- agent_profile - method = ${req.method}, but profileId parameter is missing.`)
    return
  }

  // Extra validation if oauth, req_validate.py line 522

  // return
  next()
}