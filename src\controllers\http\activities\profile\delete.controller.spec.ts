import logger from '@lcs/logger'
import { expect } from 'chai'
import deleteController from './delete.controller.js'
import * as deleteFileService from '../../../../services/amqp/file/delete.service.js'
import * as deleteActivityProfileService from '../../../../services/mssql/activity-profile/delete.service.js'
import Sinon from 'sinon'
import ActivityProfileModel from '../../../../models/activity-profile.model.js'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
const { NO_CONTENT, INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus
import * as getActivityProfileService from '../../../../services/mssql/activity-profile/get.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'

describe('HTTP: Delete Activity Profile Controller', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  xit('should delete the activity profile', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        profileId: '1',
        activityId: '1',
      }
    })

    const getProfileStub = Sinon.stub(getActivityProfileService, 'getActivityProfileService')
    getProfileStub.returns(Promise.resolve(new ActivityProfileModel({
      Etag: 'test',
      ModifiedOn: new Date(),
      FileID: 'test-file'
    })))

    const deleteActivitySub = Sinon.stub(deleteActivityProfileService, 'default')
    deleteActivitySub.returns(Promise.resolve(1))

    const deleteFileStub = Sinon.stub(deleteFileService, 'default')
    deleteFileStub.returns(Promise.resolve(1))

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
    expect(deleteActivitySub.called).to.be.true
    expect(deleteFileStub.called).to.be.true
    expect(mocks.res.getHeader('ETag')).to.equal('test')
  })

  xit('should return Not Found when the activity profile does not exist', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        profileId: '1',
        activityId: '1',
      }
    })

    const getProfileStub = Sinon.stub(getActivityProfileService, 'getActivityProfileService')
    getProfileStub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))

    const deleteActivitySub = Sinon.stub(deleteActivityProfileService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
    expect(deleteActivitySub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })

  xit('should return Internal Server Error when the activity profile service encounters an error', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        profileId: '1',
        activityId: '1',
      }
    })

    const getProfileStub = Sinon.stub(getActivityProfileService, 'getActivityProfileService')
    getProfileStub.returns(Promise.reject(new Error('Something Bad')))

    const deleteActivitySub = Sinon.stub(deleteActivityProfileService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
    expect(deleteActivitySub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })

  xit('should delete the requested activity profiles', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        activityId: '1',
      }
    })

    const getProfileStub = Sinon.stub(getActivityProfileService, 'getActivitiesProfileService')
    getProfileStub.returns(Promise.resolve([
      new ActivityProfileModel({
        ID: 'test-1',
        ActivityID: 'test-123',
        FileID: 'test-1'
      }),
      new ActivityProfileModel({
        ID: 'test-2',
        ActivityID: 'test-123'
      })
    ]))

    const deleteActivitySub = Sinon.stub(deleteActivityProfileService, 'default')
    deleteActivitySub.returns(Promise.resolve(1))

    const deleteFileStub = Sinon.stub(deleteFileService, 'default')
    deleteFileStub.returns(Promise.resolve(1))

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
    expect(deleteActivitySub.called).to.be.true
    expect(deleteFileStub.called).to.be.true
  })


  xit('should return Not Found when no activity profile exist for the search', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        activityId: '1',
      }
    })

    const getProfileStub = Sinon.stub(getActivityProfileService, 'getActivitiesProfileService')
    getProfileStub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))

    const deleteActivitySub = Sinon.stub(deleteActivityProfileService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
    expect(deleteActivitySub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })

  xit('should return Internal Server Error when the get activities profile service encounters an error', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        activityId: '1',
        since: (new Date()).toISOString()
      }
    })

    const getProfileStub = Sinon.stub(getActivityProfileService, 'getActivitiesProfileService')
    getProfileStub.returns(Promise.reject(new Error('Something Bad')))

    const deleteActivitySub = Sinon.stub(deleteActivityProfileService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
    expect(deleteActivitySub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })
})