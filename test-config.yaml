# Wolf Microk8s - TESS DEV
elasticsearch:
  host: 'http://***********:443'
  elasticPassword: UElVCxsGHBbX42KNVuso

microsoftSql:
  host: '***********'
  port: 80
  password: Letmein!
  database: TESS_Master_Unit_Test

rabbitmq:
  host: '***********'
  port: 3389
  ## Override these queue names for local testing with FDS:

  #remoteProcedureCalls:
  #  fileDelivery:
  #    queue: 'file-delivery-hwd'
  #  dam:
  #    queue: 'dam-service-hwd'

redis:
  host: 'redis://***********:80'
  password: a-very-complex-password-here

security:
  jwtSecret: '02E89FBC3D0842BAEA94C53F5402BF12FEA9640F1558D2D67DB03A115E476518'
  cookieSecret: '02E89FBC3D0842BAEA94C53F5402BF12FEA9640F1558D2D67DB03A115E476518'
  singleUseTokenSecret: '02E89FBC3D0842BAEA94C53F5402BF12FEA9640F1558D2D67DB03A115E476518'
  singleUseQrKey: '02E89FBC3D0842BAEA94C53F5402BF12FEA9640F1558D2D67DB03A115E476518'
  singleUseQrSalt: '02E89FBC3D0842BAEA94C53F5402BF12FEA9640F1558D2D67DB03A115E476518'