import getPaginatedAgents from '../../../../services/mssql/agent/get-multiple-with-session-data.service.js'
import { clamp } from '../../../../utils/number.utils.js'
import { Request, Response } from 'express'
import logger from '@lcs/logger'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Paginated-Agents-With-Session-Data', httpLogTransformer)

export default async function getAgentsWithSessionDataController(req: Request, res: Response) {
  try {
    let offset: number, limit: number, search: string | undefined
    if (req.query) {
      limit = req.query.limit && !isNaN(Number(req.query.limit)) ? Number(req.query.limit) : 0
      offset = req.query.offset && !isNaN(Number(req.query.offset)) ? Number(req.query.offset) : 0
      search = req.query.search ? req.query.search.toString() : undefined
    } else {
      offset = limit = 0
    }

    limit = clamp(limit, 10, 100)

    const data = await getPaginatedAgents(offset, limit, search)

    log('info', 'Successfully retrieved agents', { count: data.agents.length, total: data.totalRecords, req, success: true })

    res.json({
      totalRecords: data.totalRecords,
      agents: data.agents.map(agent => agent.fields)
    })
  } catch (error) {
    log('error', 'Failed to get agents', { req, errorMessage: getErrorMessage(error), success: false })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
