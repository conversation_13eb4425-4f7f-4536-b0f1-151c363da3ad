import mssql from '@lcs/mssql-utility'
import { StatementTableName as SUB_STATEMENT_TABLE_NAME } from '@tess-f/sql-tables/dist/lrs/sub-statement.js'
import { VerbTableName } from '@tess-f/sql-tables/dist/lrs/verb.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { ActivityTableName } from '@tess-f/sql-tables/dist/lrs/activity.js'
import { AgentTableName } from '@tess-f/sql-tables/dist/lrs/agent.js'
import { AttachmentsTableName } from '@tess-f/sql-tables/dist/lrs/attachments.js'
import { ActivityProfileTableName } from '@tess-f/sql-tables/dist/lrs/activity-profile.js'
import { RedisClient as VerbDisplayCache } from '../../redis/verb-display-client.service.js'
import { RedisClient as VerbCache } from '../../redis/verb-client.service.js'

export default async function () {
  const pool = mssql.getPool()

  // clear statements attachments
  await pool.request().query(`DELETE FROM [${AttachmentsTableName}]`)
  // clear the sub statements
  await pool.request().query(`DELETE FROM [${SUB_STATEMENT_TABLE_NAME}]`)
  // clear the statements
  await pool.request().query(`DELETE FROM [${StatementTableName}]`)
  // clear verbs
  await pool.request().query(`DELETE FROM [${VerbTableName}]`)
  // clear activities
  await pool.request().query(`DELETE FROM [${ActivityTableName}]`)
  // clear agents
  await pool.request().query(`DELETE FROM [${AgentTableName}]`)
  // clear activity profile
  await pool.request().query(`DELETE FROM [${ActivityProfileTableName}]`)
  // clear cache
  await VerbDisplayCache.deleteVerbDisplays()
  await VerbCache.deleteVerbs()
}
