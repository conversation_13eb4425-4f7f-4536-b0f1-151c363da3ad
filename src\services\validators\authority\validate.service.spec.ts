import { expect } from 'chai'
import validateAuthority from './validate.service.js'

describe('Validate Authority Group', () => {
  it('throws an error when the Authority Group has more than 2 members', () => {
    expect(validateAuthority.bind(validateAuthority, {
      member: [
        {
          name: '<PERSON>'
        },
        {
          name: '<PERSON>'
        },
        {
          name: '<PERSON>'
        }
      ]
    }))
      .to.throw('Groups representing authorities must only contain 2 members')
  })

  it('throws an error when the Authority Group has less than 2 members', () => {
    expect(validateAuthority.bind(validateAuthority, {
      member: [
        {
          name: '<PERSON>'
        }
      ]
    })).to.throw('Groups representing authorities must only contain 2 members')
  })

  it('throws an error when the Authority Group is not Anonymous', () => {
    expect(validateAuthority.bind(validateAuthority, {
      objectType: 'Group',
      account: {
        name: 'Admin',
        homePage: 'http://example.com'
      },
      member: [
        {
          mbox: 'mailto:<EMAIL>'
        },
        {
          mbox: 'mailto:<EMAIL>'
        }
      ]
    })).to.throw('Groups representing authorities must not contain an inverse functional identifier')

    expect(validateAuthority.bind(validateAuthority, {
      objectType: 'Group',
      mbox: 'mailto:<EMAIL>',
      member: [
        {
          mbox: 'mailto:<EMAIL>'
        },
        {
          mbox: 'mailto:<EMAIL>'
        }
      ]
    })).to.throw('Groups representing authorities must not contain an inverse functional identifier')

    expect(validateAuthority.bind(validateAuthority, {
      objectType: 'Group',
      mbox_sha1sum: 'abcdefABCDEF0123456789abcdefABCDEF012345',
      member: [
        {
          mbox: 'mailto:<EMAIL>'
        },
        {
          mbox: 'mailto:<EMAIL>'
        }
      ]
    })).to.throw('Groups representing authorities must not contain an inverse functional identifier')

    expect(validateAuthority.bind(validateAuthority, {
      objectType: 'Group',
      openid: 'http://example.com/user#1',
      member: [
        {
          mbox: 'mailto:<EMAIL>'
        },
        {
          mbox: 'mailto:<EMAIL>'
        }
      ]
    })).to.throw('Groups representing authorities must not contain an inverse functional identifier')
  })

  it('returns void when the Authority Group is valid', () => {
    expect(validateAuthority({
      objectType: 'Group',
      member: [
        {
          mbox: 'mailto:<EMAIL>'
        },
        {
          mbox: 'mailto:<EMAIL>'
        }
      ]
    })).to.equal(void 0)
  })
})
