import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivityModel from '../../../models/activity.model.js'
import getActivityDefinitionDescription from './description/get.service.js'
import getActivityDefinitionName from './name/get.service.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { Activity, ActivityTableName } from '@tess-f/sql-tables/dist/lrs/activity.js'

export default async function getCourseActivityWithMostAttempts(): Promise<ActivityModel> {
  const pool = mssql.getPool()

  const result = await pool.request().query<Activity>(`
    WITH TopStatementActivity AS (
      SELECT TOP 1 [ObjectActivityID], COUNT([ID]) AS [UseCount]
      FROM [${StatementTableName}]
      WHERE [VerbID] = 'https://adlnet.gov/expapi/verbs/initialized'
      AND [ContextRegistration] IS NOT NULL
      AND [ObjectActivityID] IS NOT NULL
      GROUP BY [ObjectActivityID]
      ORDER BY [UseCount] DESC
    )
    SELECT *
    FROM [${ActivityTableName}]
    WHERE [ID] IN (
      SELECT [ObjectActivityID]
      FROM [TopStatementActivity]
    )
  `)

  if (result.recordset.length >= 1) {
    const activity = new ActivityModel(undefined, result.recordset[0])

    try {
      activity.attachDefinitionDescription(await getActivityDefinitionDescription(activity.fields.id))
    } catch (error) {
      if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
        throw error
      }
    }

    try {
      activity.attachDefinitionName(await getActivityDefinitionName(activity.fields.id))
    } catch (error) {
      if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
        throw error
      }
    }

    return activity
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
