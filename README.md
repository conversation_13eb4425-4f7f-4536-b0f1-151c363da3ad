# Boilerplate: TypeScript Express Server API

Whats in this boilerplate?
1. [Node.js](https://nodejs.org/en/)
2. [TypeScript](https://www.typescriptlang.org/)
3. [Express](https://expressjs.com/) 
4. [ESLint](https://eslint.org/)
5. [JavaScript Standard Coding Style](https://standardjs.com/)
6. Controller/Service/Model Architecture 

## Commands
```
npm run build // build the project
npm run start // build and start
npm run start:dev // run with nodemon
```

## Architecture
#### Controllers
Responsible for:
- Recieving input from a thrid party
- Utilizing services to fulfill some action
- Handling any errors from services
- Responding to the original request

#### Services

Responsible for:
- Handling the logic required for some action
- Communicating with databases, message brokers etc...

#### Models

Responsible for:
- Storing data
- Transforming data, for example transforming the model class to/from a json object
- Validating data
- Should not contain large amounts of logic, instead services will use models to perform complex tasks


## Folder Structure
- **build/** - Output folder of typescript build
- **node_modules/** - Node packages
- **src/** - Typescript source files
  - **config/** - settings and other configurations
  - **controllers/** - see above
  - **core/** - main server file and other core functionailities
  - **models/** - see above
  - **services/** - see above
  - **index.ts** - Application entry point
- **.eslintignore** - ES lint ignore definitions
- **.eslintrc.js** - ES lint config
- **.gitignore** - Git ignore file
- **nodemon.json** - Nodemon config
- **package-lock.json** - Npm package lock file
- **package.json** - Npm package config
- **README.md** - This file you are reading, right now
- **tsconfig.json** - Typescript config