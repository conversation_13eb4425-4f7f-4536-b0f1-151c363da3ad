import mssql, { addRow } from '@lcs/mssql-utility'
import ContextAgentModel from '../../../models/context-agent.model.js'
import { ContextAgent } from '@tess-f/sql-tables/dist/lrs/context-agent.js'
import createContextAgentRelevantTypes from './context-agent-relevant-types/create.service.js'

export default async function createContextAgent(contextAgent: ContextAgentModel): Promise<ContextAgentModel> {
  const pool = mssql.getPool()
  const record = await addRow<ContextAgent>(pool.request(),  contextAgent)
  const created = new ContextAgentModel(undefined, record)

  if (contextAgent.fields.relevantTypes && contextAgent.fields.relevantTypes.length > 0) {
    created.attachRelevantTypes(await Promise.all(contextAgent.fields.relevantTypes.map(async relevantType => {
      await createContextAgentRelevantTypes(relevantType, created.Id)
      return relevantType
    })))
  }

  return created
}