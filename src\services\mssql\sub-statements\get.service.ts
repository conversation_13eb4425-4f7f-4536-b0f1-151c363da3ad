import mssql, { getRows } from '@lcs/mssql-utility'
import SubStatementModel from '../../../models/sub-statement.model.js'
import buildSubStatementService from './build.service.js'
import { SubStatement, StatementTableName } from '@tess-f/sql-tables/dist/lrs/sub-statement.js'

export default async function getSubStatement(id: string): Promise<SubStatementModel> {
  const pool = mssql.getPool()
  const records = await getRows<SubStatement>(StatementTableName, pool.request(), { ID: id })
  const subStatement = new SubStatementModel(undefined, records[0])
  await buildSubStatementService(subStatement)

  return subStatement
}
