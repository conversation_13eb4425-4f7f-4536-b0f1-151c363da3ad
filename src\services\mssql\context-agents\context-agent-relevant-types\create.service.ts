import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { ContextAgentRelevantTypes, ContextAgentRelevantTypesFields, ContextAgentRelevantTypeTableName } from '@tess-f/sql-tables/dist/lrs/context-agent-relevant-types.js'
import getOrCreateRelevantType from '../../relevant-types/get-or-create.service.js'

export default async function createContextAgentRelevantTypes(iri: string, contextAgentId: string): Promise<ContextAgentRelevantTypes> {
  const request = mssql.getPool().request()
  const relevantType = await getOrCreateRelevantType(iri)
  request.input('relevantTypeId', relevantType.ID)
  request.input('contextAgentId', contextAgentId)

  const response = await request.query<ContextAgentRelevantTypes>(`
    INSERT INTO [${ContextAgentRelevantTypeTableName}] ([${ContextAgentRelevantTypesFields.ContextAgentID}], [${ContextAgentRelevantTypesFields.TypeID}])
    OUTPUT INSERTED.*
    VALUES (@contextAgentId, @relevantTypeId)
  `)

  if (response.recordset.length <= 0) {
    throw new Error(dbErrors.default.DB_INSERT_FAILED_TO_OUTPUT)
  }

  return response.recordset[0]
}
