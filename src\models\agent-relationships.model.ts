import { Table } from '@lcs/mssql-utility'
import { AgentRelationship, AgentRelationshipFields, AgentRelationshipTableName } from '@tess-f/sql-tables/dist/lrs/agent-relationships.js'

export default class AgentRelationshipModel extends Table<AgentRelationship, AgentRelationship> {
  fields: AgentRelationship

  constructor(fields: AgentRelationship) {
    super(AgentRelationshipTableName, [
      AgentRelationshipFields.AgentID,
      AgentRelationshipFields.MemberID
    ])
    this.fields = fields
  }

  importFromDatabase(record: AgentRelationship): void {
    this.fields = record
  }

  exportJsonToDatabase(): AgentRelationship {
    return this.fields
  }
}
