import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivityModel from '../../../models/activity.model.js'
import { StatementTableName as SubStatementsTable } from '@tess-f/sql-tables/dist/lrs/sub-statement.js'
import getActivityDefinitionDescription from './description/get.service.js'
import getActivityDefinitionName from './name/get.service.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { Activity, ActivityTableName } from '@tess-f/sql-tables/dist/lrs/activity.js'

// TODO: add in log messages

/**
 * Gets the top {limit} most used activity in the system
 * @param limit number of activities to limit the query to
 */
export default async function getMostUsedActivitiesForAgent(agentID: string, limit = 3): Promise<ActivityModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('agentID', agentID)

  // Calculate and fetch the most used activity
  const query = `
    WITH Top_Activities AS (
      SELECT TOP ${limit} [${ActivityTableName}].[ID], COUNT([${StatementTableName}].[ID]) + COUNT([${SubStatementsTable}].[ID]) AS [UseCount]
      FROM [${ActivityTableName}]
        LEFT JOIN [${StatementTableName}] ON [${StatementTableName}].[ObjectActivityID] = [${ActivityTableName}].[ID] AND [${StatementTableName}].[ActorID] = @agentID
        LEFT JOIN [${SubStatementsTable}] ON [${SubStatementsTable}].[ObjectActivityID] = [${ActivityTableName}].[ID] AND [${SubStatementsTable}].[ActorID] = @agentID
      GROUP BY [${ActivityTableName}].[ID]
      ORDER BY [UseCount] DESC
    )
    SELECT *
    FROM [${ActivityTableName}]
    WHERE [ID] IN (
      SELECT [ID]
      FROM [Top_Activities]
    )
  `

  const results = await request.query<Activity>(query)

  if (results.recordset.length >= 1) {
    // we found the most used activities
    return Promise.all(results.recordset.map(async record => {
      const activity = new ActivityModel(undefined, record)

      try {
        // map the description
        activity.attachDefinitionDescription(await getActivityDefinitionDescription(activity.fields.id))
      } catch (error) {
        // if the error is not found in db this activity doesn't have a description
        if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
          // something went wrong
          throw error
        }
      }

      try {
        // map the definition name
        activity.attachDefinitionName(await getActivityDefinitionName(activity.fields.id))
      } catch (error) {
        // if the error is not found in db this activity doesn't have a name
        if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
          // something went wrong
          throw error
        }
      }

      return activity
    }))
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
