import { expect } from 'chai'
import httpStatus from 'http-status'
const { BAD_REQUEST, OK } = httpStatus
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import middleware from './xapi-version-header.middleware.js'
import settings from '../../../config/settings.js'

describe('HTTP Middleware: XAPI Header', () => {
  afterEach(() => Sinon.restore())

  it('should reject requests with missing "X-Experience-API-Version" header.', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks()
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.false
    const temp = mocks.res._getData()
    console.log(temp)
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res._getData()).to.eq('X-Experience-API-Version header missing')
    expect(mocks.res.statusCode).to.eq(BAD_REQUEST)
  })

  it('should include the "X-Experience-API-Version" header in every response.', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks({ headers: { 'X-Experience-API-Version': '1.0.3' } })
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res.statusCode).to.eq(OK)
  })

  it('should parse the version number from a url encoded form', () => {
    const next1 = Sinon.spy()
    const mocks1 = httpMocks.createMocks({
      headers: {
        "content-type": 'application/x-www-form-urlencoded'
      },
      body: { 'X-Experience-API-Version': '1.0.2' }
    })
    middleware(mocks1.req, mocks1.res, next1)
    expect(next1.called).to.be.true
    expect(mocks1.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks1.res.statusCode).to.eq(OK)

    const next2 = Sinon.spy()
    const mocks2 = httpMocks.createMocks({
      headers: {
        "content-type": 'application/x-www-form-urlencoded'
      },
      body: { 'X_Experience_API_Version': '1.0.2' }
    })
    middleware(mocks2.req, mocks2.res, next2)
    expect(next2.called).to.be.true
    expect(mocks2.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks2.res.statusCode).to.eq(OK)
  })

  it('should accept requests with a version header of 1.0 as if the version header was 1.0.0.', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks({ headers: { 'X-Experience-API-Version': '1.0' } })
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.true
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res.statusCode).to.eq(OK)
  })

  it('should reject requests with version header prior to version 1.0.0 unless such requests are routed to a fully conformant implementation of the prior version specified in the header.', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks({ headers: { 'X-Experience-API-Version': '0.9' } })
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.false
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res._getData()).to.eq('X-Experience-API-Version is not supported')
    expect(mocks.res.statusCode).to.eq(BAD_REQUEST)
  })

  it('should reject requests with a version header of 1.1.0 or greater.', () => {
    const next = Sinon.spy()
    const mocks = httpMocks.createMocks({ headers: { 'X-Experience-API-Version': '1.1.0' } })
    middleware(mocks.req, mocks.res, next)
    expect(next.called).to.be.false
    expect(mocks.res.header('X-Experience-API-Version')).to.eq(settings.XAPI_VERSION)
    expect(mocks.res._getData()).to.eq('X-Experience-API-Version is not supported')
    expect(mocks.res.statusCode).to.eq(BAD_REQUEST)
  })
})
