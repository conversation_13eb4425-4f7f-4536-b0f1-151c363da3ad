import { Table } from "@lcs/mssql-utility"
import { ContextGroups, ContextGroupsTableName } from "@tess-f/sql-tables/dist/lrs/context-groups.js"
import AgentModel, { Agent<PERSON>son } from "./agents.model.js"

export interface ContextGroupJson {
  objectType?: string,
  group?: AgentJson,
  relevantTypes?: string[]
}

export default class ContextGroupModel extends Table<ContextGroupJson, ContextGroups> {
  fields: ContextGroupJson
  Id!: string
  groupId!: string
  statementId!: string
  private _group: AgentModel = new AgentModel()

  constructor(fields?: ContextGroupJson, record?: ContextGroups) {
    super(ContextGroupsTableName)

    if (fields) {
      this.fields = fields
    } else {
      this.fields = {}
    }

    if (record) this.importFromDatabase(record)
  }

  importFromDatabase(record: ContextGroups): void {
    this.Id = record.ID!
    this.groupId = record.GroupID!
    this.statementId = record.StatementID!
    
    this.fields = {
      objectType: record.ObjectType
    }
  }

  exportJsonToDatabase(): ContextGroups {
    return {
      ID: this.Id,
      StatementID: this.statementId,
      GroupID: this.groupId,
      ObjectType: this.fields.objectType
    }
  }

  attachGroup(group: AgentModel): void {
    this._group = group
    this.groupId = group.ID
    this.fields.group = group.fields
  }

  attachRelevantTypes(types: string[]): void {
    this.fields.relevantTypes = types
  }

  toJson(idsOnly = false): Record<string, any> {
    return {
      objectType: this.fields.objectType,
      group: this._group.toJson(idsOnly),
      relevantTypes: this.fields.relevantTypes
    }
  }
}