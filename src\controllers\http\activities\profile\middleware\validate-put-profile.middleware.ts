import logger from '@lcs/logger'
import { NextFunction, Request, Response } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import { validateIri } from '../../../../../utils/validate-data-type.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('HTTP-Middleware:put-activity-profile-validator', httpLogTransformer)

const validParams = ['activityId', 'profileId']

export default async function validatePutProfileRequest(req: Request, res: Response, next: NextFunction) {
  const rogueParams = Object.keys(req.query).filter(key => !validParams.includes(key))
  if (rogueParams.length > 0) {
    log('warn', 'Failed to put activity profile: unallowed query params', { rogueParams, success: false })
    res.status(BAD_REQUEST).send(`The put activity profile request contained unexpected parameters: ${rogueParams.join(', ')}`)
    return
  }

  if (!req.query.activityId) {
    log('warn', 'Failed to put activity profile: missing activityId', { success: false })
    res.status(BAD_REQUEST).send('Missing activityId parameter in the request')
    return
  }

  try {
    validateIri(req.query.activityId.toString(), 'activityId query param for activity profile')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'Failed to put activity profile: malformed activity id', { errorMessage, success: false })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  if (!req.query.profileId) {
    log('warn', 'Failed to put activity profile: missing profileId', { success: false })
    res.status(BAD_REQUEST).send('Missing profileId parameter in the request')
    return
  }

  // check json body for application/json content
  if (req.headers['content-type'] === 'application/json' && Object.keys(req.body).length <= 0) {
    log('warn', 'Failed to put activity profile: missing request body', { success: false })
    res.status(BAD_REQUEST).send('Could not find the profile')
    return
  }

  // TODO: extra validation for oauth (if present)

  // go to the next function in the chain
  next()
}