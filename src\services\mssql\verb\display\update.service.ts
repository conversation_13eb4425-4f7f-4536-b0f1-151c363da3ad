import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import VerbDisplayModel from "../../../../models/verb-display.model.js"
import { VerbDisplay, VerbDisplayTableName } from '@tess-f/sql-tables/dist/lrs/verb-display.js'

export default async function updateVerbDisplay(verbDisplay: VerbDisplayModel[]): Promise<VerbDisplayModel[]> {
  if (verbDisplay.length <= 0) {
    return []
  }
  await deleteRow<VerbDisplay>(mssql.getPool().request(), VerbDisplayTableName, { VerbID: verbDisplay[0].fields.VerbID })
  const verbDisplays = await Promise.all(verbDisplay.map(async display => addRow<VerbDisplay>(mssql.getPool().request(), display)))
  return verbDisplays.map(record => new VerbDisplayModel(record))
}
