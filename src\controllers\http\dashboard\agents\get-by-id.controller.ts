import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getAgentByID from '../../../../services/mssql/agent/get-by-id.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Agent-By-ID', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const agent = await getAgentByID(req.params.id)
    log('info', 'Successfully retrieved agent by id', { id: req.params.id, success: true })
    res.json(agent.fields)
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get agent because id was not found in the database', { req, success: false })
      res.status(httpStatus.NOT_FOUND).send('Agent not found')
    } else {
      log('error', 'Failed to get agent', { req, errorMessage: getErrorMessage(error), success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
