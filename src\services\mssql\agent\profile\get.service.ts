import mssql, { getRows, DB_Errors as dbErrors } from '@lcs/mssql-utility'
import AgentProfileModel from '../../../../models/agent-profile.model.js'
import { AgentProfile, ActivityStateTableName, AgentProfileFields } from '@tess-f/sql-tables/dist/lrs/agent-profiles.js'
import { map } from 'mssql'

export async function getAgentProfileService(profileID: string, agentId: string): Promise<AgentProfileModel> {
  return new AgentProfileModel((await getRows<AgentProfile>(ActivityStateTableName, mssql.getPool().request(), {
    ProfileID: profileID,
    AgentID: agentId
  }))[0])
}

export async function getAgentProfilesService(agentId: string, since?: string): Promise<AgentProfileModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('agentId', agentId)
  let query = `
    SELECT *
    FROM [${ActivityStateTableName}]
    WHERE [${AgentProfileFields.AgentID}] = @agentId 
  `

  // Since
  if (since) {
    request.input('since', since)
    query += `AND [${AgentProfileFields.Updated}] >= @since `
  }

  const results = await request.query<AgentProfile>(query)
  return results.recordset.map(record => new AgentProfileModel(record))
}