import getPaginatedCourseActivitiesWithSessionData from '../../../../services/mssql/activities/get-paginated-course-activities-with-session-data.service.js'
import { clamp } from '../../../../utils/number.utils.js'
import { Request, Response } from 'express'
import logger from '@lcs/logger'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Paginated-Course-Activities-With-Attempts', httpLogTransformer)

export default async function getCourseActivitiesWithAttemptsController(req: Request, res: Response) {
  try {
    let offset: number, limit: number, search: string | undefined
    if (req.query) {
      limit = req.query.limit && !isNaN(Number(req.query.limit)) ? Number(req.query.limit) : 0
      offset = req.query.offset && !isNaN(Number(req.query.offset)) ? Number(req.query.offset) : 0
      search = req.query.search ? req.query.search.toString() : undefined
    } else {
      offset = limit = 0
    }

    limit = clamp(limit, 10, 100)

    const data = await getPaginatedCourseActivitiesWithSessionData(offset, limit, search)

    log('info', 'Successfully retrieved course activities', { count: data.activities.length, total: data.totalRecords, req, success: true })

    res.json({
      totalRecords: data.totalRecords,
      activities: data.activities.map(activity => activity.fields)
    })
  } catch (error) {
    log('error', 'Failed to get course activities', { req, errorMessage: getErrorMessage(error), success: false })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
