import { expect } from 'chai'
import Sinon from 'sinon'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import middleware from './post-profile.middleware.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, OK } = httpStatus
import ActivityProfileModel from '../../../../../models/activity-profile.model.js'
import * as getActivityProfileService from '../../../../../services/mssql/activity-profile/get.service.js'

describe('Middleware: Validate POST activity profile request', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  it('should return bad request when a query param other than (activityId, profileId) is provided', () => {
    const mock = httpMocks.createMocks({ query: { foo: 'bar', baz: 'baz' } })
    const mock2 = httpMocks.createMocks({ query: { activityId: 'test', profileId: 'test', foo: 'bar' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('The post activity profile request contained unexpected parameters: foo, baz')
    expect(next.called).to.be.false

    middleware(mock2.req, mock2.res, next)
    expect(mock2.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock2.res._getData()).to.contain('The post activity profile request contained unexpected parameters: foo')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activity id is missing', () => {
    const mock = httpMocks.createMocks({ query: { profileId: 'test' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Error -- activity_profile - method = POST, but activityId parameter is missing.')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activity id is malformed', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'test' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should return bad request when the profile id is missing', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Error -- activity_profile - method = POST, but profileId parameter is missing.')
    expect(next.called).to.be.false
  })

  it('should return bad request when the content type is application/json and the body is empty', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', profileId: '1' }, body: {}, headers: { 'content-type': 'application/json' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  xit('should call next when the request is valid', async () => {
    const mock = httpMocks.createMocks({
      query: {
        activityId: 'http:example.com/activities/1',
        profileId: '1'
      },
      headers: { "content-type": 'application/json' },
      body: { foo: 'bar' }
    })

    const getActivityProfileServiceStub = Sinon.stub(getActivityProfileService, 'getActivityProfileService')
    getActivityProfileServiceStub.returns(Promise.resolve(new ActivityProfileModel({
      ID: '05c702db-20ab-480c-9f12-5f2a417fbe2f',
      ActivityID: 'http://www.example.com/activityId/hashset',
      ContentType: 'application/json'
    })))

    const next = Sinon.spy()
    await middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(OK)
    expect(next.called).to.be.true
  })
})