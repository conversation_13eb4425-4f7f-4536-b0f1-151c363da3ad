import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import { CreateFile } from '@tess-f/fds/dist/amqp/create.js'
import { OWNER_LRS } from '@tess-f/fds/dist/owners.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('AMQP-File-Service.Save-File')

/**
 * Stores a file in the FDS
 * @param base64Content Base64 string contents of the file to save
 * @param filename Name of the file to store
 * @param mimeType File Mime Type
 * @returns {string} The id of the file stored in FDS
 */
export default async function saveFile(base64Content: string, filename: string, mimeType: string): Promise<string> {
  try {
    const response = await CreateFile(
      settings.amqp.serviceQueues.fds, {
      createdById: 'anonymous',
      filename,
      mimeType,
      owner: OWNER_LRS,
      isPublic: false,
      isPackage: false,
      createThumbnail: false,
      fileBase64: base64Content
    },
      settings.amqp.rpc_timeout
    )

    if (response.success && response.data) {
      return response.data.id
    } else if (response.success && !response.data) {
      log('warn', 'RPC returned success status but no data', { success: false })
      throw new Error('RPC returned success status but no data')
    } else {
      log('warn', 'RPC returned unsuccessful status', { success: false, rpcMessage: response.message })
      throw new Error(response.message || 'Unknown RPC error')
    }
  } catch (error) {
    log('error', 'Failed to save file', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
