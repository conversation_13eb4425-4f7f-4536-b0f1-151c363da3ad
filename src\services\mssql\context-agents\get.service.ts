import mssql, { getRows } from '@lcs/mssql-utility'
import ContextAgentModel from '../../../models/context-agent.model.js'
import { ContextAgent, ContextAgentTableName } from '@tess-f/sql-tables/dist/lrs/context-agent.js'
import getContextAgentRelevantTypes from './context-agent-relevant-types/get-relevant-types-for-context-agent.service.js'
import getAgentByID from '../agent/get-by-id.service.js'
import logger from '@lcs/logger'

const log = logger.create('Service-MSSQL.get-context-agents')

export default async function getContextAgentsForStatement(statementId: string): Promise<ContextAgentModel[]> {
  const pool = mssql.getPool()

  const records = await getRows<ContextAgent>(ContextAgentTableName, pool.request(), { StatementID: statementId })
  
  log('info', 'Successfully retrieved context agents', { statementId, success: true })
  
  const contextAgents = await Promise.all(records.map(async(record) => {
    const contextAgent = new ContextAgentModel(undefined, record)
    contextAgent.attachAgent(await getAgentByID(record.AgentID!))
    contextAgent.attachRelevantTypes(await getContextAgentRelevantTypes(record.ID!))
    return contextAgent
  }))

  return contextAgents
}