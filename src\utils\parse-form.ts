import { Request } from 'express'
import { IncomingForm, Files, Fields } from 'formidable'
import path from 'path'
import { v4 as uuid } from 'uuid'

export default async function (req: Request): Promise<{ files: Files<string>, fields: Fields, headers: Map<string, { [key: string]: string }> }> {
  return new Promise((resolve, reject) => {
    const form = new IncomingForm({
      keepExtensions: false,
      multiples: false,
      uploadDir: path.resolve('temp'),
      maxFileSize: 8000000,
      hashAlgorithm: 'sha256'
    })

    const headers: Map<string, { [key: string]: string }> = new Map<string, { [key: string]: string }>()

    form.onPart = (part: any) => {
      // if the part is anonymous force it to have a name
      if (part.name === null) {
        part.name = uuid()
      }
      if (part.name && part.headers) {
        headers.set(part.name, part.headers)
      }
      form._handlePart(part)
    }

    form.parse(req, (err, fields, files) => {
      if (err) {
        reject(err)
        return
      }
      resolve({ files, fields, headers })
    })

  })
}