import mssql from '@lcs/mssql-utility'
import { ContextAgentRelevantTypesFields, ContextAgentRelevantTypeTableName } from '@tess-f/sql-tables/dist/lrs/context-agent-relevant-types.js'
import { RelevantTypes, RelevantTypesFields, RelevantTypesTableName } from '@tess-f/sql-tables/dist/lrs/relevant-types.js'

export default async function getContextAgentRelevantTypes(contextAgentId: string): Promise<string[]> {
  const request = mssql.getPool().request()
  request.input('contextAgentId', contextAgentId)

  const results = await request.query<Required<Pick<RelevantTypes, 'IRI'>>>(`
    SELECT [${RelevantTypesFields.IRI}]
    FROM [${RelevantTypesTableName}]
    WHERE [${RelevantTypesFields.ID}] IN (
      SELECT [${ContextAgentRelevantTypesFields.TypeID}]
      FROM [${ContextAgentRelevantTypeTableName}]
      WHERE [${ContextAgentRelevantTypesFields.ContextAgentID}] = @contextAgentId
    )
  `)

  return results.recordset.map(record => record.IRI)
}  