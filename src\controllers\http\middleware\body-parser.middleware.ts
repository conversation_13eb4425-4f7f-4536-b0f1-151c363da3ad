import logger from '@lcs/logger'
import { NextFunction, Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('HTTP-Middleware.router', httpLogTransformer)

// log errors from body parser
export default async function (error: any, req: Request, res: Response, next: NextFunction) {
  if (error instanceof SyntaxError && error.message.indexOf('JSON')) {
    log('error', 'Malformed request received by router.', { req, errorMessage: getErrorMessage(error), success: false })
    res.status(BAD_REQUEST)
    next()
  } else if (error) {
    log('error', 'Unknown error has occurred.', { req, errorMessage: getErrorMessage(error), success: false })
    res.status(error.status ?? INTERNAL_SERVER_ERROR)
    next()
  } else {
    next()
  }
}
