import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import SubStatement from '../../../models/sub-statement.model.js'
import getActivityService from '../activities/get.service.js'
import getAgentService from '../agent/get.service.js'
import getVerbDisplay from '../verb/display/get.service.js'
import getContextActivities from '..//activities/context/get-activities.service.js'
import { SubStatementCategoryContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/sub-statement-category-context-activity.js'
import { SubStatementGroupingContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/sub-statement-grouping-context-activity.js'
import { SubStatementOtherContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/sub-statement-other-context-activity.js'
import { SubStatementParentContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/sub-statement-parent-context-activity.js'

/**
 * Adds all of the reference data to rebuild an xAPI statements sub statement object
 */
export default async function buildSubStatementService(subStatement: SubStatement) {
  // Attach teh actor
  subStatement.attachActor(await getAgentService({ ID: subStatement.actorID }))

  // Attach object
  // NOTE: if object is a statement ref the sub statement class will handle it
  if (subStatement.objectActivityID) {
    // object is an activity
    subStatement.attachObjectActivity(await getActivityService(subStatement.objectActivityID))
  } else if (subStatement.objectAgentID) {
    // object is an agent
    subStatement.attachObjectAgent(await getAgentService({ ID: subStatement.objectAgentID }))
  } // Sub statements do not have nested sub statements

  // Attach the verb names
  try {
    subStatement.attachVerbDisplay(await getVerbDisplay(subStatement.fields.verb!.id))
  } catch (error) {
    // if not found move on
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  // Attach context instructor
  if (subStatement.contextInstructorID) {
    subStatement.attachContextInstructor(await getAgentService({ ID: subStatement.contextInstructorID }))
  }

  // Attach context team
  if (subStatement.contextTeamID) {
    subStatement.attachContextTeam(await getAgentService({ ID: subStatement.contextTeamID }))
  }

  // Attach context activities
  try {
    // parent
    subStatement.attachContextParentActivities(await getContextActivities(subStatement.ID, SubStatementParentContextActivityTableName))
  } catch (error) {
    // if not found move on
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  try {
    // category
    subStatement.attachContextCategoryActivities(await getContextActivities(subStatement.ID, SubStatementCategoryContextActivityTableName))
  } catch (error) {
    // if not found move on
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  try {
    // grouping
    subStatement.attachContextGroupingActivities(await getContextActivities(subStatement.ID, SubStatementGroupingContextActivityTableName))
  } catch (error) {
    // if not found move on
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  try {
    // other
    subStatement.attachContextOtherActivities(await getContextActivities(subStatement.ID, SubStatementOtherContextActivityTableName))
  } catch (error) {
    // if not found move on
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }
}
