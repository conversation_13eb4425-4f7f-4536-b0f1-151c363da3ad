import logger from '@lcs/logger'
import { expect } from 'chai'
import deleteController from './delete.controller.js'
import * as getOrCreateAgentService from '../../../../services/mssql/agent/get-or-create.service.js'
import * as deleteFileService from '../../../../services/amqp/file/delete.service.js'
import * as deleteAgentProfileService from '../../../../services/mssql/agent/profile/delete.service.js'
import Sinon from 'sinon'
import AgentModel from '../../../../models/agents.model.js'
import AgentProfileModel from '../../../../models/agent-profile.model.js'

import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
const { NO_CONTENT, INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus
import * as getAgentProfileService from '../../../../services/mssql/agent/profile/get.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'

xdescribe('HTTP: Delete Agent Profile Controller', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  beforeEach(() => {
    const agentStub = Sinon.stub(getOrCreateAgentService, 'default')
    agentStub.returns(Promise.resolve(new AgentModel(undefined, { ID: 'test-1' })))
  })

  it('should delete the agent profile', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: '1',
      }
    })

    const getAgentProfileStub = Sinon.stub(getAgentProfileService, 'getAgentProfileService')
    getAgentProfileStub.returns(Promise.resolve(new AgentProfileModel({
      Etag: 'test',
      Updated: new Date(),
      FileID: 'test-file'
    })))

    const deleteAgentProfileSub = Sinon.stub(deleteAgentProfileService, 'default')
    deleteAgentProfileSub.returns(Promise.resolve(1))

    const deleteFileStub = Sinon.stub(deleteFileService, 'default')
    deleteFileStub.returns(Promise.resolve(1))

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
    expect(deleteAgentProfileSub.called).to.be.true
    expect(deleteFileStub.called).to.be.true
    expect(mocks.res.getHeader('ETag')).to.equal('test')
  })

  it('should return Not Found when the agent profile does not exist', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: '1'
      }
    })

    const getAgentProfileStub = Sinon.stub(getAgentProfileService, 'getAgentProfileService')
    getAgentProfileStub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))

    const deleteAgentProfileSub = Sinon.stub(deleteAgentProfileService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
    expect(deleteAgentProfileSub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })

  it('should return Internal Server Error when the agent profile service encounters an error', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: '1'
      }
    })

    const getAgentProfileStub = Sinon.stub(getAgentProfileService, 'getAgentProfileService')
    getAgentProfileStub.returns(Promise.reject(new Error('Something Bad')))

    const deleteAgentProfileSub = Sinon.stub(deleteAgentProfileService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
    expect(deleteAgentProfileSub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })

  it('should delete the requested agent profile', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: '1'
      }
    })

    const getAgentProfileStub = Sinon.stub(getAgentProfileService, 'getAgentProfileService')
    getAgentProfileStub.returns(Promise.resolve(
      new AgentProfileModel({
        Etag: 'test',
        Updated: new Date(),
        FileID: 'test-file'
      })))

    const deleteAgentProfileSub = Sinon.stub(deleteAgentProfileService, 'default')
    deleteAgentProfileSub.returns(Promise.resolve(1))

    const deleteFileStub = Sinon.stub(deleteFileService, 'default')
    deleteFileStub.returns(Promise.resolve(1))

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
    expect(deleteAgentProfileSub.called).to.be.true
    expect(deleteFileStub.called).to.be.true
  })


  it('should return Not Found when no agent profile exist for the search', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: '1'
      }
    })

    const getAgentProfileStub = Sinon.stub(getAgentProfileService, 'getAgentProfileService')
    getAgentProfileStub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))

    const deleteAgentProfileSub = Sinon.stub(deleteAgentProfileService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
    expect(deleteAgentProfileSub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })

  it('should return Internal Server Error when the get agent profile service encounters an error', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: '1'
      }
    })

    const getAgentProfileStub = Sinon.stub(getAgentProfileService, 'getAgentProfileService')
    getAgentProfileStub.returns(Promise.reject(new Error('Something Bad')))

    const deleteAgentProfileSub = Sinon.stub(deleteAgentProfileService, 'default')
    const deleteFileStub = Sinon.stub(deleteFileService, 'default')

    await deleteController(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
    expect(deleteAgentProfileSub.called).to.be.false
    expect(deleteFileStub.called).to.be.false
  })
})