import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import AgentSession, { AgentSessionDatabaseRecord } from '../../../models/dashboard/agent-session.model.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { AgentTableName } from '@tess-f/sql-tables/dist/lrs/agent.js'

export default async function getPaginatedAgentsForActivity(activityID: string, offset = 0, limit = 100, search?: string): Promise<{ totalRecords: number, agents: AgentSession[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('offset', offset)
  request.input('limit', limit)
  request.input('activity', activityID)

  let query = `
    WITH SessionCounts AS (
      SELECT COUNT(DISTINCT [ContextRegistration]) AS [Sessions], [ActorID]
      FROM [${StatementTableName}]
      WHERE [ObjectActivityID] = @activity
      GROUP BY [ActorID]
    )
    SELECT [${AgentTableName}].*,
      TotalRecords = COUNT(*) OVER(),
      [SessionCounts].[Sessions] AS [SessionCount]
    FROM [${AgentTableName}]
      JOIN [SessionCounts] ON [SessionCounts].[ActorID] = [${AgentTableName}].[ID]
    WHERE [ID] IN (
      SELECT [ActorID]
      FROM [SessionCounts]
    )
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, ['Name', 'Mbox', 'MboxSHA1Sum', 'OpenID', 'OAuthIdentifier', 'AccountHomePage', 'AccountName'], 'all')}) `
  }

  query += `
    ORDER BY [Name], [Mbox], [MboxSHA1Sum], [OpenID], [OauthIdentifier], [AccountName], [AccountHomePage]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<AgentSessionDatabaseRecord>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    agents: results.recordset.map(record => new AgentSession(undefined, record))
  }
}
