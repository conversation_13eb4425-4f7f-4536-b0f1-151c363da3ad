import logger from '@lcs/logger'
import { DeleteFiles } from '@tess-f/fds/dist/amqp/delete.js'
import settings from '../../../config/settings.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('AMQP-File-Service.Delete-File')

/**
 * Deletes the given files in FDS
 * @param {string[]} fileIds The ids of the files to delete
 */
export default async function deleteFile(fileIds: string[]): Promise<number> {
  try {
    const response = await DeleteFiles(
      settings.amqp.serviceQueues.fds, {
      fileIds
    },
      settings.amqp.rpc_timeout
    )

    if (response.success && response.data) {
      return response.data.numDeleted
    } else if (response.success && !response.data) {
      log('warn', 'RPC returned success but no delete count', { success: false })
      throw new Error('RPC returned success but no delete count')
    } else {
      log('warn', 'RPC failed to delete file(s)', { success: false, fileIds: fileIds.join(', '), rpcMessage: response.message })
      throw new Error(response.message || 'R<PERSON> failed to delete file(s)')
    }
  } catch (error) {
    log('error', 'Failed to delete file(s)', { success: false, errorMessage: getErrorMessage(error) })
    throw error
  }
}
