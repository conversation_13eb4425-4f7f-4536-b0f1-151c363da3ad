import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivityModel from '../../../../models/activity.model.js'
import { Activity, ActivityTableName } from '@tess-f/sql-tables/dist/lrs/activity.js'
import getActivityDefinitionDescription from '../description/get.service.js'
import getActivityDefinitionName from '../name/get.service.js'

export default async function getContextActivities(statementID: string, joinTableName: string): Promise<ActivityModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('statementID', statementID)

  const records = await request.query<Activity>(`
    SELECT *
    FROM ${ActivityTableName}
    WHERE [ID] IN (
      SELECT [ActivityID]
      FROM [${joinTableName}]
      WHERE [StatementID] = @statementID
    )
  `)

  if (records.recordset.length <= 0) {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }

  const activities = records.recordset.map(record => new ActivityModel(undefined, record))

  // map on the definition name an description if it has one
  await Promise.all(activities.map(async activity => {
    try {
      activity.attachDefinitionName(await getActivityDefinitionName(activity.fields.id))
    } catch (error) {
      if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
        throw error
      } // if we don't find the name this activity doesn't have one set
    }

    try {
      activity.attachDefinitionDescription(await getActivityDefinitionDescription(activity.fields.id))
    } catch (error) {
      if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
        throw error
      } // if we don't find the description this activity doesn't have one set
    }
  }))

  return activities
}
