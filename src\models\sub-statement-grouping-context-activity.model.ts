import { Table } from '@lcs/mssql-utility'
import {
  SubStatementGroupingContextActivity,
  SubStatementGroupingContextActivityFields,
  SubStatementGroupingContextActivityTableName
} from '@tess-f/sql-tables/dist/lrs/sub-statement-grouping-context-activity.js'

export default class SubStatementGroupingContextActivityModel extends Table<SubStatementGroupingContextActivity, SubStatementGroupingContextActivity> {
  public fields: SubStatementGroupingContextActivity

  constructor(fields: SubStatementGroupingContextActivity) {
    super(SubStatementGroupingContextActivityTableName, [
      SubStatementGroupingContextActivityFields.ActivityID,
      SubStatementGroupingContextActivityFields.StatementID
    ])
    this.fields = fields
  }

  public importFromDatabase(record: SubStatementGroupingContextActivity): void {
    this.fields = record
  }

  public exportJsonToDatabase(): SubStatementGroupingContextActivity {
    return this.fields
  }
}
