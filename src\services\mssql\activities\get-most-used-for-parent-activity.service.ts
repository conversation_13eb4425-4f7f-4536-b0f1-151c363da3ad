import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivityModel from '../../../models/activity.model.js'
import getActivityDefinitionDescription from './description/get.service.js'
import getActivityDefinitionName from './name/get.service.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { Activity, ActivityTableName } from '@tess-f/sql-tables/dist/lrs/activity.js'
import { ParentContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/parent-context-activity.js'

export default async function getMostUsedActivitiesForParent(parentID: string, limit = 3): Promise<ActivityModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('activity', parentID)

  const query = `
    WITH TopObjects AS (
      SELECT TOP ${limit} COUNT([ID]) AS [UseCount], [ObjectActivityID]
      FROM [${StatementTableName}]
      WHERE [ID] IN (
        SELECT [StatementID]
        FROM [${ParentContextActivityTableName}]
        WHERE [ActivityID] = @activity
      )
      GROUP BY [ObjectActivityID]
      ORDER BY [UseCount] DESC
    )
    SELECT *
    FROM [${ActivityTableName}]
    WHERE [ID] IN (
      SELECT [ObjectActivityID]
      FROM [TopObjects]
    )
  `

  const results = await request.query<Activity>(query)

  if (results.recordset.length > 0) {
    return Promise.all(results.recordset.map(async record => {
      const activity = new ActivityModel(undefined, record)

      try {
        activity.attachDefinitionDescription(await getActivityDefinitionDescription(activity.fields.id))
      } catch (error) {
        if ((error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) || !(error instanceof Error)) {
          throw error
        }
      }

      try {
        activity.attachDefinitionName(await getActivityDefinitionName(activity.fields.id))
      } catch (error) {
        if ((error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) || !(error instanceof Error)) {
          throw error
        }
      }

      return activity
    }))
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
