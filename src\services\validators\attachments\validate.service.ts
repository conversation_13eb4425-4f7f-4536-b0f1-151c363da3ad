import { checkAllowed<PERSON>ields, checkIfDict, checkIfList, checkRequiredFields, validateIri } from '../../../utils/validate-data-type.js'
import { validateLanguageMap } from '../../../utils/validate-language.js'

const ATTACHMENT_ALLOWED_FIELDS = ['usageType', 'display', 'description', 'contentType', 'length', 'sha2', 'fileUrl']
const ATTACHMENT_REQUIRED_FIELDS = ['usageType', 'display', 'contentType', 'length']

export default function validateAttachments(attachments: any): void {
  // validate attachments
  // ensure that attachments is a list
  checkIfList(attachments, 'Attachments')

  // validate each attachment
  attachments.forEach((attachment: any) => {
    // check allowed fields
    checkAllowedFields(ATTACHMENT_ALLOWED_FIELDS, attachment, 'Attachments')
    // check required fields
    checkRequiredFields(ATTACHMENT_REQUIRED_FIELDS, attachment, 'Attachments')

    // validate usage type
    validateIri(attachment.usageType, 'Attachments usageType')

    // if fileUrl included, validate it
    if (attachment.fileUrl) {
      validateIri(attachment.fileUrl, 'Attachments fileUrl')
    }

    if (!attachment.sha2) {
      throw new Error('Attachment sha2 is required')
    } else {
      // ensure sha2 is submitted as string
      if (typeof attachment.sha2 !== 'string') {
        throw new Error('Attachment sha2 must be a string')
      }
      if (!/^[a-f0-9]{64}$/.test(attachment.sha2)) {
        throw new Error('Not a valid sha2 inside the statement')
      }
    }

    // ensure the length is an int
    if (Number.isNaN(attachment.length) || !Number.isInteger(attachment.length)) {
      throw new Error('Attachment length must be an integer')
    }

    // ensure content type
    if (typeof attachment.contentType !== 'string') {
      throw new Error('Attachment contentType must be a string')
    }

    // ensure display is a dictionary (language map)
    checkIfDict(attachment.display, 'Attachment display')
    validateLanguageMap(attachment.display, 'attachment display')

    // if description included, ensure it is a dictionary (language map)
    if (attachment.description) {
      checkIfDict(attachment.description, 'Attachment description')
      validateLanguageMap(attachment.description, 'attachment description')
    }

    // Content type must be set to octet/stream for signatures
    if (attachment.contentType !== 'application/octet-stream' && attachment.usageType?.includes('://adlnet.gov/expapi/attachments/signature')) {
      throw new Error('Signature attachment must have Content-Type of \'application/octet-stream\'')
    }
  })
}
