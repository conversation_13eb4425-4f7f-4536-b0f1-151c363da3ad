import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getActivityService from '../../../services/mssql/activities/get.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('HTTP-Controller:get-activities', httpLogTransformer)

export default async function (req: Request, res: Response) {
  const activity_id = req.query.activityId!.toString()

  try {
    const activityRecord = await getActivityService(activity_id)
    const activity = activityRecord.toJson(["all"])
    res.setHeader('Content-Length', JSON.stringify(activity).length)
    res.json(activity)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get activities: no activity found', { success: false, req })
      const activity = {
        id: activity_id,
        objectType: "Activity"
      }
      res.setHeader('Content-Length', JSON.stringify(activity).length)
      res.json(activity)
    } else {
      log('error', 'Failed to get activities', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}