import mssql from '@lcs/mssql-utility'
import { ContextGroupRelevantTypesFields, ContextGroupRelevantTypesTableName } from '@tess-f/sql-tables/dist/lrs/context-group-relevant-types.js'
import { RelevantTypes, RelevantTypesFields, RelevantTypesTableName } from '@tess-f/sql-tables/dist/lrs/relevant-types.js'

export default async function getContextGroupRelevantTypes(contextGroupId: string): Promise<string[]> {
  const request = mssql.getPool().request()
  request.input('contextGroupId', contextGroupId)

  const results = await request.query<Required<Pick<RelevantTypes, 'IRI'>>>(`
    SELECT [${RelevantTypesFields.IRI}]
    FROM [${RelevantTypesTableName}]
    WHERE [${RelevantTypesFields.ID}] IN (
      SELECT [${ContextGroupRelevantTypesFields.TypeID}]
      FROM [${ContextGroupRelevantTypesTableName}]
      WHERE [${ContextGroupRelevantTypesFields.ContextGroupID}] = @contextGroupId
    )
  `)

  return results.recordset.map(record => record.IRI)
}