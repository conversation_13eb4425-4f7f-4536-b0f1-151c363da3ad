import { expect } from 'chai'
import logger from '@lcs/logger'
import controller from './get.controller.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, OK } = httpStatus
import httpMocks from 'node-mocks-http'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import Sinon from 'sinon'
import * as getActivityStateService from '../../../../services/mssql/activity-state/get.service.js'
import * as getOrCreateAgent from '../../../../services/mssql/agent/get-or-create.service.js'
import AgentModel from '../../../../models/agents.model.js'
import ActivityStateModel from '../../../../models/activity-state.model.js'

xdescribe('HTTP Controller: get activity state', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  beforeEach(() => {
    const stub = Sinon.stub(getOrCreateAgent, "default")
    stub.returns(Promise.resolve(new AgentModel(undefined, { ID: 'test' })))
  })

  it('should return not found when the requested stateId does not exist', async () => {
    const mocks = httpMocks.createMocks({ query: { stateId: '-1', activityId: 'xxx', agent: '{}' } })
    const stub = Sinon.stub(getActivityStateService, "getActivityStateService")
    stub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
    expect(mocks.res._getData()).to.contain('Activity state not found')
  })

  it('should return not found when stateId is not provided', async () => {
    const mocks = httpMocks.createMocks({ query: { activityId: 'xxx', registration: '06e75030-4e66-11ee-be56-0242ac120002', agent: '{}', since: '2032-08-07T03' } })
    const stub = Sinon.stub(getActivityStateService, "getActivitiesStateService")
    stub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
    expect(mocks.res._getData()).to.contain('Activities state not found')
  })

  it('should return Internal Server when service fails and state id is provided', async () => {
    const mocks = httpMocks.createMocks({ query: { activityId: 'xxx', registration: '06e75030-4e66-11ee-be56-0242ac120002', agent: '{}', since: '2032-08-07T03', stateId: '1' } })
    const stub = Sinon.stub(getActivityStateService, "getActivitiesStateService")
    stub.returns(Promise.reject(new Error('Internal Error')))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })

  it('should return Internal Server when service fails and state id is not provided', async () => {
    const mocks = httpMocks.createMocks({ query: { activityId: 'xxx', registration: '06e75030-4e66-11ee-be56-0242ac120002', agent: '{}', since: '2032-08-07T03' } })
    const stub = Sinon.stub(getActivityStateService, "getActivitiesStateService")
    stub.returns(Promise.reject(new Error('Internal Error')))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })

  it('Returns a json activity state when stateId is provided', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        stateId: '1',
        activityId: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
        agent: '{}',
        registration: 'registrations'
      }
    })
    const stub = Sinon.stub(getActivityStateService, 'getActivityStateService')
    stub.returns(Promise.resolve(new ActivityStateModel({
      ID: '2',
      ActivityID: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
      AgentID: '{"mbox":"mailto:<EMAIL>"}',
      State: '{"test": 1234}',
      ModifiedOn: new Date(),
      Etag: 'test'
    })))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const data = JSON.parse(mocks.res._getData())
    expect(data).to.exist
  })


  it('returns a list of activity state ids for the given request', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        activityId: 'https://tess-dev.lcs.ds.northgrum.com/activity/1',
        agent: '{}',
        registration: 'registrations'
      }
    })
    const stub = Sinon.stub(getActivityStateService, 'getActivitiesStateService')
    stub.returns(Promise.resolve([
      new ActivityStateModel({
        ID: '1'
      }),
      new ActivityStateModel({
        ID: '2'
      })
    ]))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const ids: string[] = JSON.parse(mocks.res._getData())
    expect(ids).to.exist
    expect(ids.length).to.equal(2)
    expect(ids).to.contain('1')
    expect(ids).to.contain('2')
  })
})
