import mssql, { getRows } from '@lcs/mssql-utility'
import VerbDisplayModel from '../../../../models/verb-display.model.js'
import { VerbDisplay, VerbDisplayTableName } from '@tess-f/sql-tables/dist/lrs/verb-display.js'
import logger from '@lcs/logger'
import { RedisClient as VerbDisplayClient } from '../../../redis/verb-display-client.service.js'

const log = logger.create('Service-MSSQL.get-verb-display')

export default async function getVerbDisplay(verbID: string): Promise<VerbDisplayModel[]> {
  const cachedDisplay: VerbDisplayModel[] = await VerbDisplayClient.getVerbDisplay(verbID) as VerbDisplayModel[]
  if (cachedDisplay) {
    log('info', 'Successfully retrieved verb display from cache', { verbID, success: true })
    // renew the cache
    await VerbDisplayClient.setVerbDisplay(verbID, cachedDisplay)
    log('verbose', 'Successfully renewed verb display time to live in the cache', { success: true })
    return cachedDisplay
  }

  // display not found in the cache lets get it from the database
  const pool = mssql.getPool()
  const records = await getRows<VerbDisplay>(VerbDisplayTableName, pool.request(), { VerbID: verbID })
  const display = records.map(record => new VerbDisplayModel(record))

  // set the display in the cache
  await VerbDisplayClient.setVerbDisplay(verbID, display)
  return display
}
