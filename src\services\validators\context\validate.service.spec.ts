import { expect } from 'chai'
import validateContext from './validate.service.js'
import { v4 as uuid } from 'uuid'

describe('Validates A Statements Context', () => {
  it('throws an error when the context is not a valid dictionary', () => {
    expect(validateContext.bind(validateContext, 42, {}))
      .to.throw('Context is not a properly formatted dictionary')
    expect(validateContext.bind(validateContext, [42], {}))
      .to.throw('Context is not a properly formatted dictionary')
    expect(validateContext.bind(validateContext, true, {}))
      .to.throw('Context is not a properly formatted dictionary')
    expect(validateContext.bind(validateContext, false, {}))
      .to.throw('Context is not a properly formatted dictionary')
    expect(validateContext.bind(validateContext, 'context object', {}))
      .to.throw('Context is not a properly formatted dictionary')
  })

  it('throws an error when the context has an unallowed field', () => {
    expect(validateContext.bind(validateContext, {
      registration: 'test',
      instructor: '<PERSON>',
      team: 'Team PB',
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ],
      name: 'George',
      test: 'test prop',
      foo: 'bar'
    }, {}))
      .to.throw('Invalid field(s) found in Context - name, test, foo')
  })

  it('throws an error if the registration is not a valid UUID', () => {
    expect(validateContext.bind(validateContext, {
      registration: 'test',
      instructor: 'Bob Smith',
      team: 'Team PB',
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Context registration is not a valid uuid')
  })

  it('throws an error if the instructor is not a valid Agent', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      instructor: {
        mbox: '<EMAIL>'
      },
      team: 'Team PB',
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('<NAME_EMAIL> did not start with mailto:')
  })

  it('throws an error when the team is not a valid Agent', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      instructor: {
        mbox: 'mailto:<EMAIL>'
      },
      team: {
        mbox: '<EMAIL>'
      },
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('<NAME_EMAIL> did not start with mailto:')
  })

  it('throws an error when the team is an Agent', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      instructor: {
        mbox: 'mailto:<EMAIL>'
      },
      team: {
        mbox: 'mailto:<EMAIL>'
      },
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Team in context must be a group')

    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      instructor: {
        mbox: 'mailto:<EMAIL>'
      },
      team: {
        objectType: 'Agent',
        mbox: 'mailto:<EMAIL>'
      },
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Team in context must be a group')
  })

  it('throws an error when the context revision is not a string', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      instructor: {
        mbox: 'mailto:<EMAIL>'
      },
      team: {
        objectType: 'Group',
        mbox: 'mailto:<EMAIL>'
      },
      contextActivities: ['one', 'two', 'three'],
      revision: 4,
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Context revision must be a string')

    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      instructor: {
        mbox: 'mailto:<EMAIL>'
      },
      team: {
        objectType: 'Group',
        mbox: 'mailto:<EMAIL>'
      },
      contextActivities: ['one', 'two', 'three'],
      revision: true,
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Context revision must be a string')

    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      instructor: {
        mbox: 'mailto:<EMAIL>'
      },
      team: {
        objectType: 'Group',
        mbox: 'mailto:<EMAIL>'
      },
      contextActivities: ['one', 'two', 'three'],
      revision: { rev: 4 },
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Context revision must be a string')

    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      instructor: {
        mbox: 'mailto:<EMAIL>'
      },
      team: {
        objectType: 'Group',
        mbox: 'mailto:<EMAIL>'
      },
      contextActivities: ['one', 'two', 'three'],
      revision: [4, 0, 1],
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Context revision must be a string')
  })

  it('throws an error when the context has a revision and the statement object type is not Activity', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'StatementRef' }))
      .to.throw('Revision is not allowed in context if statement object is not an Activity')
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'SubStatement' }))
      .to.throw('Revision is not allowed in context if statement object is not an Activity')
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Group' }))
      .to.throw('Revision is not allowed in context if statement object is not an Activity')
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Agent' }))
      .to.throw('Revision is not allowed in context if statement object is not an Activity')
  })

  it('throws an error when the context platform is not a string', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      platform: 4,
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Context platform must be a string')

    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      platform: true,
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Context platform must be a string')

    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      platform: { arch: 'linux' },
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Context platform must be a string')

    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      platform: ['linux', 'x86'],
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, {}))
      .to.throw('Context platform must be a string')
  })

  it('throws an error when the context has a platform and the statement object type is not Activity', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'StatementRef' }))
      .to.throw('Platform is not allowed in context if statement object is not an Activity')
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'SubStatement' }))
      .to.throw('Platform is not allowed in context if statement object is not an Activity')
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Group' }))
      .to.throw('Platform is not allowed in context if statement object is not an Activity')
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      platform: 'linux',
      language: 'en-US',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Agent' }))
      .to.throw('Platform is not allowed in context if statement object is not an Activity')
  })

  it('throws an error if the language property is not a string', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 1,
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context language must be a string')
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: [1],
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context language must be a string')
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: { format: 'en-US' },
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context language must be a string')
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: true,
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context language must be a string')
  })

  it('throws an error if the language is not RFC3987 compliant', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      language: 'myFancyNewLang',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('language myFancyNewLang is not valid in context language')
  })

  it('throws an error when the context statement is not a dictionary', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      statement: '1',
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('StatementRef is not a properly formatted dictionary')
  })

  it('throws an error when the context statement objectType is not StatementRef', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'Activity'
      },
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('StatementRef objectType must be set to \'StatementRef\'')
  })

  it('throws an error when the context statement contains unallowed fields', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: 'test',
        name: 'statement ref'
      },
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Invalid field(s) found in StatementRef - name')
  })

  it('throws an error when the context statement is missing required fields', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef'
      },
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('id is missing in StatementRef')
  })

  it('throws an error when the context statement id is not a valid uuid', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: ['one', 'two', 'three'],
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: 'test'
      },
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('StatementRef id is not a valid uuid')
  })

  it('throws an error when the context activities is not a dictionary', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: 'one',
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context activity is not a properly formatted dictionary')
  })

  it('throws an error when the context activities contains an unallowed field', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: {
        parent: [{ id: 'http://example.com/parent' }],
        grouping: [{ id: 'http://example.com/grouping' }],
        category: [{ id: 'http://example.com/category' }],
        other: [{ id: 'http://example.com' }],
        test: [{ id: 'http://example.org/test' }]
      },
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context activity type is not valid - test - must be parent, grouping, category, other')
  })

  it('throws an error if a context activity is malformed', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: {
        parent: [{ id: 'http://example.com/parent' }, { foo: 'bar' }],
        grouping: [{ id: 'http://example.com/grouping' }],
        category: [{ id: 'http://example.com/category' }],
        other: [{ id: 'http://example.com' }]
      },
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Invalid field(s) found in Activity - foo')

    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: {
        parent: { foo: 'bar' },
        grouping: [{ id: 'http://example.com/grouping' }],
        category: [{ id: 'http://example.com/category' }],
        other: [{ id: 'http://example.com' }]
      },
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Invalid field(s) found in Activity - foo')

    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: {
        parent: 'test-parent-id'
      },
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [
        {
          mbox: 'mailto:<EMAIL>'
        }
      ],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('contextActivities is not formatted correctly')
  })

  it('throws an error when the context agents is not a list', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: {
        mbox: 'mailto:<EMAIL>'
      },
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context Agents is not a properly formatted array')
  })

  it('throws an error when a context agent does not have an objectType', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        mbox: 'mailto:<EMAIL>'
      }],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Invalid field(s) found in context.contextAgents[0] - mbox')
  })

  it('throws an error when a context agent object type is not contextAgent', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        agent: {
          mbox: 'mailto:<EMAIL>'
        },
        objectType: 'Agent'
      }],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context Agent entries must be \'contextAgent\', got Agent')
  })

  it('throws an error when a context agent relevantTypes is not a list', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        agent: {
          mbox: 'mailto:<EMAIL>'
        },
        objectType: 'contextAgent',
        relevantTypes: { foo: 'bar' }
      }],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('context.contextAgents[0].relevantTypes is not a properly formatted array')
  })

  it('throws an error when a context agent relevantTypes is an empty list', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        agent: {
          mbox: 'mailto:<EMAIL>'
        },
        objectType: 'contextAgent',
        relevantTypes: []
      }],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context Agent relevant types entries must be a non-empty list')
  })

  it('throws an error when a context agent relevantType is not an IRI', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        agent: {
          mbox: 'mailto:<EMAIL>'
        },
        objectType: 'contextAgent',
        relevantTypes: ['test-value']
      }],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('context.contextAgents[0].relevantTypes[0] with value test-value was not a valid IRI')
  })

  it('throws an error when a context agent does not contain an agent', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com']
      }],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context Agent entries must have an agent')
  })

  it('throws an error when a context agent is not a valid agent', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: '<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'Group',
          name: 'Team PB',
          mobx: 'mailto:<EMAIL>'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('<NAME_EMAIL> did not start with mailto:')
  })

  it('throws an error when the context groups is not a list', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: {
        objectType: 'Group',
        name: 'Team PB',
        mobx: 'mailto:<EMAIL>'
      }
    }, { objectType: 'Activity' }))
      .to.throw('Context Groups is not a properly formatted array')
  })

  it('throws an error when a context group does not have an object type', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [{
        relevantTypes: ['http://example.com'],
        group: {
          name: 'Team PB',
          mbox: 'mailto:<EMAIL>'
        }
      }]
    }, { objectType: 'Activity' }))
      .to.throw('Context Group entries must have an objectType')
  })

  it('throws an error when a context groups object type is not contextGroup', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'Group',
          group: {
            name: 'Team PB',
            mbox: 'mailto:<EMAIL>'
          }
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context Group entries must be \'contextGroup\', got Group')
  })

  it('throws an error when a context group does not have a relevant types property', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'contextGroup'
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context Group entries must have a group')
  })

  it('throws an error when a context group relevantTypes is not a list', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'contextGroup',
          relevantTypes: { foo: 'bar' },
          group: {
            mbox: 'mailto:<EMAIL>'
          }
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('context.contextGroups[0].relevantTypes is not a properly formatted array')
  })

  it('throws an error when a context group relevantList is empty', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'contextGroup',
          relevantTypes: []
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context Group relevant types entries must be a non-empty list')
  })

  it('throws an error when a context group relevantList contains a non IRI value', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'contextGroup',
          relevantTypes: [{ foo: 'bar' }]
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('context.contextGroups[0].relevantTypes[0] must be a string type')
  })

  it('throws an error when a context group does not contain a group', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'contextGroup',
          relevantTypes: ['http://example.com']
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('Context Group entries must have a group')
  })

  it('throws an error when a context group\'s group property is not a valid agent', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: {
        parent: [{
          id: 'http://example.com/activity/1'
        }]
      },
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'contextGroup',
          relevantTypes: ['http://example.com/'],
          group: {
            objectType: 'Group',
            mbox: '<EMAIL>'
          }
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('<NAME_EMAIL> did not start with mailto:')
  })

  it('throws an error when extensions is not a dictionary', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: {
        parent: [{
          id: 'http://example.com/activity/1'
        }]
      },
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: 'asdf123',
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'contextGroup',
          relevantTypes: ['http://example.com/'],
          group: {
            objectType: 'Group',
            mbox: 'mailto:<EMAIL>'
          }
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('context extensions extensions is not a properly formatted dictionary')
  })

  it('throws an error when the extension key is not a valid IRI', () => {
    expect(validateContext.bind(validateContext, {
      registration: uuid(),
      contextActivities: {
        parent: [{
          id: 'http://example.com/activity/1'
        }]
      },
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: {
        'foo': 'bar'
      },
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'contextGroup',
          relevantTypes: ['http://example.com/'],
          group: {
            objectType: 'Group',
            mbox: 'mailto:<EMAIL>'
          }
        }
      ]
    }, { objectType: 'Activity' }))
      .to.throw('context extensions with value foo was not a valid IRI')
  })

  it('returns void when valid', () => {
    expect(validateContext({
      registration: uuid(),
      instructor: {
        mbox: 'mailto:<EMAIL>'
      },
      team: {
        mbox: 'mailto:<EMAIL>',
        objectType: 'Group'
      },
      contextActivities: {
        parent: [{
          id: 'http://example.com/xapi/example/test1'
        }]
      },
      revision: '4.0.2',
      platform: 'linux',
      language: 'en-US',
      statement: {
        objectType: 'StatementRef',
        id: uuid()
      },
      extensions: {
        'http://example.com/my/extesion': 'bar'
      },
      contextAgents: [{
        objectType: 'contextAgent',
        relevantTypes: ['http://example.com'],
        agent: {
          mbox: 'mailto:<EMAIL>'
        }
      }],
      contextGroups: [
        {
          objectType: 'contextGroup',
          relevantTypes: ['http://example.com/'],
          group: {
            objectType: 'Group',
            mbox: 'mailto:<EMAIL>'
          }
        }
      ]
    }, { objectType: 'Activity' })).to.equal(void 0)
  })
})
