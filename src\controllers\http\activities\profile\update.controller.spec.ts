import logger from '@lcs/logger'
import { expect } from 'chai'
import update from './update.controller.js'
import * as getOrCreateActivityProfileService from '../../../../services/mssql/activity-profile/get-or-create.service.js'
import * as deleteFileService from '../../../../services/amqp/file/delete.service.js'
import * as updateActivityProfileService from '../../../../services/mssql/activity-profile/update.service.js'
import * as deleteActivityProfileService from '../../../../services/mssql/activity-profile/delete.service.js'
import Sinon from 'sinon'
import ActivityProfileModel from '../../../../models/activity-profile.model.js'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
const { NO_CONTENT, PRECONDITION_FAILED, INTERNAL_SERVER_ERROR } = httpStatus
import * as eTagUtils from '../../../../utils/etag.utils.js'

xdescribe('HTTP: Update activity profile controller', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  beforeEach(() => {
    const updateStub = Sinon.stub(updateActivityProfileService, 'default')
    updateStub.returns(Promise.resolve(new ActivityProfileModel({})))
  })

  it('should return no content on successful update of json activity profile', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        profileId: '1',
        activityId: '1'
      }
    })

    const createProfileStub = Sinon.stub(getOrCreateActivityProfileService, 'default')
    createProfileStub.returns(Promise.resolve({
      activityProfile: new ActivityProfileModel({
        ID: '1',
        ActivityID: '1'
      }),
      created: false
    }))

    const checkEtagStub = Sinon.stub(eTagUtils, 'checkModificationConditions')
    checkEtagStub.returns(void 0)
    await update(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
  })

  it('should return PRECONDITION_FAILED when etag profile does not match', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        profileId: '1',
        activityId: '1'
      },
      body: {
        test: '1',
        foo: 'bar'
      },
      headers: {
        'updated': (new Date()).toISOString(),
        'if-match': '123456'
      }
    })

    const createProfileStub = Sinon.stub(getOrCreateActivityProfileService, 'default')
    createProfileStub.returns(Promise.resolve({
      created: false,
      activityProfile: new ActivityProfileModel({
        ID: '1',
        ActivityID: '1',
        Profile: '{"bar":"baz", "test":"2"}',
        ModifiedOn: new Date()
      })
    }))

    await update(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(PRECONDITION_FAILED)
  })

  it('should return INTERNAL_SERVER_ERROR when internal service fail', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        profileId: '1',
        activityId: '1',
      },
      body: {
        test: '1',
        foo: 'bar'
      },
      headers: {
        'updated': (new Date()).toISOString(),
        'if-match': '123456'
      }
    })

    const createProfileStub = Sinon.stub(getOrCreateActivityProfileService, 'default')
    createProfileStub.returns(Promise.reject(new Error('server error')))

    await update(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })

  it('should delete the created resources when etag if match header is present and creating profile', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        profileId: '1',
        activityId: '1',
      },
      body: {
        test: '1',
        foo: 'bar'
      },
      headers: {
        'updated': (new Date()).toISOString(),
        'if-match': '123456'
      }
    })

    const createProfileStub = Sinon.stub(getOrCreateActivityProfileService, 'default')
    createProfileStub.returns(Promise.resolve({
      created: true,
      activityProfile: new ActivityProfileModel({
        ID: '1',
        ActivityID: '1',
        FileID: '123',
        ModifiedOn: new Date()
      })
    }))

    const deleteActivitySub = Sinon.stub(deleteActivityProfileService, 'default')
    deleteActivitySub.returns(Promise.resolve(1))

    const deleteFileStub = Sinon.stub(deleteFileService, 'default')
    deleteFileStub.returns(Promise.resolve(1))

    await update(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(PRECONDITION_FAILED)
    expect(deleteActivitySub.called).to.be.true
    expect(deleteFileStub.called).to.be.true
  })
})
