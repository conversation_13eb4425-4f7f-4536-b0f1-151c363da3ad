import mssql, { getRows } from '@lcs/mssql-utility'
import ActivityDescriptionModel from '../../../../models/activity-descriptions.model.js'
import { ActivityDescription, ActivityDescriptionTableName } from '@tess-f/sql-tables/dist/lrs/activity-description.js'

export default async function getActivityDefinitionDescription(activityID: string): Promise<ActivityDescriptionModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<ActivityDescription>(ActivityDescriptionTableName, pool.request(), { ActivityID: activityID })
  return records.map(record => new ActivityDescriptionModel(record))
}
