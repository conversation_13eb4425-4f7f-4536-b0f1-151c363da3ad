import { Table } from '@lcs/mssql-utility'
import { OtherContextActivity, OtherContextActivityFields, OtherContextActivityTableName } from '@tess-f/sql-tables/dist/lrs/other-context-activity.js'

export default class OtherContextActivityModel extends Table<OtherContextActivity, OtherContextActivity> {
  public fields: OtherContextActivity

  constructor(fields: OtherContextActivity) {
    super(OtherContextActivityTableName, [
      OtherContextActivityFields.ActivityID,
      OtherContextActivityFields.StatementID
    ])
    this.fields = fields
  }

  public importFromDatabase(record: OtherContextActivity): void {
    this.fields = record
  }

  public exportJsonToDatabase(): OtherContextActivity {
    return this.fields
  }
}
