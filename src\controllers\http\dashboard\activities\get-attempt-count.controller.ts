import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getSessionCountForActivity from '../../../../services/mssql/activities/get-session-count.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Activity-Attempt-Count', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    if (!req.query || !req.query.activity) {
      log('warn', 'Failed to get attempt count for activity, id missing in the query params', { req, success: false })
      res.status(httpStatus.BAD_REQUEST).send('Activity ID not provided')
      return
    }
    const attempts = await getSessionCountForActivity(req.query.activity.toString())
    log('info', 'Successfully retrieved attempt count for activity', { attempts, req, activity: req.query.activity.toString(), success: true })
    res.json(attempts)
  } catch (error) {
    log('error', 'Failed to get attempt count for activity', { errorMessage: getErrorMessage(error), req, success: false })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
