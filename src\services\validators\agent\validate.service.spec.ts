import { expect } from 'chai'
import validateAgent from './validate.service.js'

describe('Validates an Agent', () => {
  it('throws an error when the agent is a number', () => {
    expect(validateAgent.bind(validateAgent, 42, 'actor'))
      .to.throw('Agent in actor is not a properly formatted dictionary')
  })

  it('throws an error when the agent is a string', () => {
    expect(validateAgent.bind(validateAgent, 'bob', 'actor'))
      .to.throw('Agent in actor is not a properly formatted dictionary')
  })

  it('throws an error when the agent is an array', () => {
    expect(validateAgent.bind(validateAgent, ['bob'], 'actor'))
      .to.throw('Agent in actor is not a properly formatted dictionary')
  })

  it('throws an error when the agent is a boolean', () => {
    expect(validateAgent.bind(validateAgent, true, 'actor'))
      .to.throw('Agent in actor is not a properly formatted dictionary')
  })

  it('throws an error when the agent has unallowed fields', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Agent',
      name: '<PERSON>',
      age: 37
    }, 'actor'))
      .to.throw('Invalid field(s) found in Agent/Group - age')
  })

  it('throws an error when the agent is an object and the objectType is not set', () => {
    expect(validateAgent.bind(validateAgent, {
      account: {
        name: 'Bob',
        homepage: 'http://example.com'
      },
    }, 'object'))
      .to.throw('objectType must be set when using an Agent as the object of a statement')
  })

  it('throws an error when the objectType is not Agent or Group and the placement is not object', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Actor',
      name: 'admin'
    }, 'actor'))
      .to.throw('An agent\'s objectType must be either Agent or Group if given')
  })

  it('throws an error when more than one IFI is given for an Agent', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Agent',
      mbox: 'mailto:<EMAIL>',
      account: {
        name: 'admin',
        homepage: 'http://example.com'
      }
    }, 'actor'))
      .to.throw('One and only one of mbox, mbox_sha1sum, openid, account may be supplied with an Agent')
  })

  it('throws an error when no IFI is given for an Agent', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Agent'
    }, 'actor'))
      .to.throw('One and only one of mbox, mbox_sha1sum, openid, account may be supplied with an Agent')
  })

  it('throws an error when a group has more than 1 IFI', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      mbox: 'mailto:<EMAIL>',
      account: {
        name: 'admin',
        homepage: 'http://example.com'
      }
    }, 'actor'))
      .to.throw('None or one and only one of mbox, mbox_sha1sum, openid, account may be supplied with a Group')
  })

  it('throws an error when the Agent name is not a string', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Agent',
      name: 42,
      mbox: 'mailto:<EMAIL>'
    }, 'actor'))
      .to.throw('If name is given in Agent, it must be a string -- got number')
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Agent',
      name: true,
      mbox: 'mailto:<EMAIL>'
    }, 'actor'))
      .to.throw('If name is given in Agent, it must be a string -- got boolean')
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Agent',
      name: {
        first: 'Bob',
        last: 'Smith'
      },
      mbox: 'mailto:<EMAIL>'
    }, 'actor'))
      .to.throw('If name is given in Agent, it must be a string -- got object')
  })

  it('throws an error when the Agent mbox is not valid', () => {
    expect(validateAgent.bind(validateAgent, {
      mbox: '<EMAIL>'
    }, 'actor'))
      .to.throw('<NAME_EMAIL> did not start with mailto:')
  })

  it('throws an error when the Agent sha1sum is not valid', () => {
    expect(validateAgent.bind(validateAgent, {
      mbox_sha1sum: 'abcdefABCDEF0123456789abcdefABCDEF0123'
    }, 'actor'))
      .to.throw('mbox_sha1sum value [abcdefABCDEF0123456789abcdefABCDEF0123] is not a valid sha1sum')
  })

  it('throws an error when the Agent openid is not valid', () => {
    expect(validateAgent.bind(validateAgent, {
      openid: '(@website.com)'
    }, 'actor'))
      .to.throw('openid with value (@website.com) was not a valid IRI')
  })

  it('throws an error when the Agent account is an array', () => {
    expect(validateAgent.bind(validateAgent, {
      account: [1, 2, 3]
    }, 'actor'))
      .to.throw('Account is not a properly formatted dictionary')
  })

  it('throws an error when the account contains unallowed fields', () => {
    expect(validateAgent.bind(validateAgent, {
      account: {
        name: 'admin',
        homePage: 'http://example.com',
        alt: 'extra prop'
      }
    }, 'actor'))
      .to.throw('Invalid field(s) found in Account - alt')
  })

  it('throws an error when the account is missing required fields', () => {
    expect(validateAgent.bind(validateAgent, {
      account: {
        name: 'admin'
      }
    }, 'actor'))
      .to.throw('homePage is missing in Account')
  })

  it('throws an error when the homepage is not a valid IRI', () => {
    expect(validateAgent.bind(validateAgent, {
      account: {
        name: 'admin',
        homePage: 'web site that I use'
      }
    }, 'actor'))
      .to.throw('homePage with value web site that I use was not a valid IRI')
  })

  it('throws an error when the account name is not a string', () => {
    expect(validateAgent.bind(validateAgent, {
      account: {
        name: 42,
        homePage: 'http://example.com'
      }
    }, 'actor'))
      .to.throw('account name must be a string')
    expect(validateAgent.bind(validateAgent, {
      account: {
        name: true,
        homePage: 'http://example.com'
      }
    }, 'actor'))
      .to.throw('account name must be a string')
    expect(validateAgent.bind(validateAgent, {
      account: {
        name: [42],
        homePage: 'http://example.com'
      }
    }, 'actor'))
      .to.throw('account name must be a string')
    expect(validateAgent.bind(validateAgent, {
      account: {
        name: { first: 'Bob', last: 'Smith' },
        homePage: 'http://example.com'
      }
    }, 'actor'))
      .to.throw('account name must be a string')
  })

  it('throws an error when the group name is not a string', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 42
    }, 'actor'))
      .to.throw('If name is given in Group, it must be a string')
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: { first: 'Bob', last: 'Smith' }
    }, 'actor'))
      .to.throw('If name is given in Group, it must be a string')
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: [42]
    }, 'actor'))
      .to.throw('If name is given in Group, it must be a string')
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: true
    }, 'actor'))
      .to.throw('If name is given in Group, it must be a string')
  })

  it('throws an error when group has no IFI and no members', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 'test group'
    }, 'actor'))
      .to.throw('Anonymous groups must contain member')
  })

  it('throws an error when the group member property is not an array', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 'test group',
      member: {
        1: 'Bob',
        'test': 'Mr. Smith'
      }
    }, 'actor'))
      .to.throw('Members is not a properly formatted array')

    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 'test group',
      member: true
    }, 'actor'))
      .to.throw('Members is not a properly formatted array')

    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 'test group',
      member: 'Bob Smith'
    }, 'actor'))
      .to.throw('Members is not a properly formatted array')

    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 'test group',
      member: 42
    }, 'actor'))
      .to.throw('Members is not a properly formatted array')
  })

  it('throws an error if the Group member list is empty', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 'test group',
      member: []
    }, 'actor'))
      .to.throw('Member property must contain agents')
  })

  it('throws an error if a Group contains another Group', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 'test group',
      member: [
        {
          account: {
            name: 'Bob',
            homePage: 'http://example.com'
          }
        },
        {
          objectType: 'Group',
          name: 'sub group'
        }
      ]
    }, 'actor'))
      .to.throw('Group member value cannot be other groups')
  })

  it('throws an error when the Group contains a malformed Agent member', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 'test group',
      member: [
        {
          account: {
            name: 'Bob',
            homePage: 'http://example.com'
          }
        },
        {
          mbox: '<EMAIL>'
        }
      ]
    }, 'actor'))
      .to.throw('<NAME_EMAIL> did not start with mailto:')
  })

  it('throws an error when the group IFI is invalid', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 'test group',
      mbox: '<EMAIL>'
    }, 'actor'))
      .to.throw('<NAME_EMAIL> did not start with mailto:')
  })

  it('throws an error when an identified groups member is invalid', () => {
    expect(validateAgent.bind(validateAgent, {
      objectType: 'Group',
      name: 'test group',
      mbox: 'mailto:<EMAIL>',
      member: [
        {
          account: {
            name: 'admin',
            homePage: 'http://example.com'
          }
        },
        {
          mbox: '<EMAIL>'
        }
      ]
    }, 'actor'))
      .to.throw('<NAME_EMAIL> did not start with mailto:')
  })

  it('returns void for valid Agent', () => {
    expect(
      validateAgent({
        account: {
          name: 'admin',
          homePage: 'http://example.com'
        }
      }, 'actor')
    ).to.equal(void 0)

    expect(
      validateAgent({
        mbox: 'mailto:<EMAIL>'
      }, 'actor')
    ).to.equal(void 0)

    expect(
      validateAgent({
        mbox_sha1sum: 'abcdefABCDEF0123456789abcdefABCDEF012345'
      }, 'actor')
    ).to.equal(void 0)

    expect(
      validateAgent({
        objectType: 'Agent',
        openid: 'http://example.com'
      }, 'object')
    ).to.equal(void 0)
  })

  it('returns void for valid Group', () => {
    expect(validateAgent({
      objectType: 'Group',
      member: [
        {
          account: {
            name: 'admin',
            homePage: 'http://example.com'
          }
        }
      ]
    }, 'actor')).to.equal(void 0)

    expect(validateAgent({
      objectType: 'Group',
      name: 'test group',
      mbox: 'mailto:<EMAIL>'
    }, 'actor')).to.equal(void 0)

    expect(validateAgent({
      objectType: 'Group',
      name: 'test group',
      mbox: 'mailto:<EMAIL>',
      member: [
        {
          objectType: 'Agent',
          account: {
            name: 'admin',
            homePage: 'http://example.com'
          }
        }
      ]
    }, 'actor')).to.equal(void 0)
  })
})
