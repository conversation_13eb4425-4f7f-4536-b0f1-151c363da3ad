import logger from '@lcs/logger'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import moment from 'moment'
import { AgentSessionJson } from '../../../../models/dashboard/agent-session.model.js'
import getPaginatedAgentsForActivity from '../../../../services/mssql/agent/get-multiple-for-activity.service.js'
import getCompletedStatementsForAgentAndActivity from '../../../../services/mssql/statements/get-completed-for-agent-and-activity.service.js'
import formatDuration from '../../../../utils/format-duration.js'
import { clamp } from '../../../../utils/number.utils.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Paginated-Agents-For-Activity', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    if (!req.query || !req.query.activity) {
      log('warn', 'Failed to get agents for activity, no activity ID provided', { req })
      res.status(httpStatus.BAD_REQUEST).send('No activity ID provided')
      return
    }
    let offset: number, limit: number, search: string | undefined
    if (req.query) {
      limit = req.query.limit && !isNaN(Number(req.query.limit)) ? Number(req.query.limit) : 0
      offset = req.query.offset && !isNaN(Number(req.query.offset)) ? Number(req.query.offset) : 0
      search = req.query.search ? req.query.search.toString() : undefined
    } else {
      offset = limit = 0
    }

    limit = clamp(limit, 10, 100)

    const data = await getPaginatedAgentsForActivity(req.query.activity.toString(), offset, limit, search)

    log('info', 'Successfully retrieved agents with sessions for activity', { req, count: data.agents.length, total: data.totalRecords, activity: req.query.activity.toString(), success: true })

    const agents = await Promise.all(data.agents.map(async agent => {
      try {
        const statements = await getCompletedStatementsForAgentAndActivity(agent.ID, req.query.activity!.toString())
        const sessionAgent: {
          agent: AgentSessionJson,
          avgDuration: string,
          sessions: { duration: string, id?: string, timestamp?: string }[]
        } = {
          agent: agent.fields,
          avgDuration: 'Unknown',
          sessions: []
        }
        let runningDuration = 0
        let sessions = 0
        statements.forEach(statement => {
          const session = {
            duration: 'Unknown',
            timestamp: statement.fields.timestamp,
            id: statement.fields.context?.registration
          }
          if (statement.fields.result && statement.fields.result.duration && moment.duration(statement.fields.result.duration).isValid()) {
            const duration = moment.duration(statement.fields.result.duration)
            runningDuration += duration.asMilliseconds()
            session.duration = formatDuration(duration)
            sessions++
          }
          sessionAgent.sessions.push(session)
        })
        if (runningDuration > 0 && sessions > 0) {
          const averageDuration = moment.duration(runningDuration / sessions)
          sessionAgent.avgDuration = formatDuration(averageDuration)
        }
        return sessionAgent
      } catch (error) {
        if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
          return {
            agent: agent.fields,
            avgDuration: '00:00',
            sessions: []
          }
        } else {
          throw error
        }
      }
    }))
    res.json({
      totalRecords: data.totalRecords,
      agents
    })
  } catch (error) {
    log('error', 'Failed to get agents with sessions for activity', { errorMessage: getErrorMessage(error), req, success: false })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
