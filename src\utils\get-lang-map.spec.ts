import { expect } from 'chai'
import getLangMap from './get-lang-map.js'

describe('Get Language Map', () => {
  it('should return an empty object when the given language array is empty', () => {
    const langMap1 = getLangMap([])
    expect(langMap1).to.exist
    expect(Object.keys(langMap1).length).to.equal(0)
    const langMap2 = getLangMap([], ['en'])
    expect(langMap2).to.exist
    expect(Object.keys(langMap2).length).to.equal(0)
  })

  it('should return all the given languages', () => {
    const langMap1 = getLangMap(
      [{ Lang: 'en', Display: 'english' }, { Lang: 'es', Display: 'spanish' }, { Lang: 'fr', Display: 'french' }],
      ['es', 'en', 'fr', 'all']
    )
    const langMap2 = getLangMap(
      [{ Lang: 'en', Display: 'english' }, { Lang: 'es', Display: 'spanish' }, { Lang: 'fr', Display: 'french' }],
      ['es', 'all', 'fr', 'en']
    )

    expect(langMap1).to.exist
    const langMap1Keys = Object.keys(langMap1)
    expect(langMap1Keys.length).to.equal(3)
    expect(langMap1Keys).to.contain('en')
    expect(langMap1Keys).to.contain('es')
    expect(langMap1Keys).to.contain('fr')
    expect(langMap1.en).to.equal('english')
    expect(langMap1.es).to.equal('spanish')
    expect(langMap1.fr).to.equal('french')

    expect(langMap2).to.exist
    const langMap2Keys = Object.keys(langMap2)
    expect(langMap2Keys.length).to.equal(3)
    expect(langMap2Keys).to.contain('en')
    expect(langMap2Keys).to.contain('es')
    expect(langMap2Keys).to.contain('fr')
    expect(langMap2.en).to.equal('english')
    expect(langMap2.es).to.equal('spanish')
    expect(langMap2.fr).to.equal('french')
  })

  it('returns the system default language when looking for any language and system default lang is available', () => {
    // system language is en-US
    const langMap = getLangMap(
      [{ Lang: 'es', Display: 'spanish' }, { Lang: 'en-US', Display: 'english' }],
      ['fr', 'gm', 'anylanguage']
    )
    expect(langMap).to.exist
    expect(Object.keys(langMap)).to.contain('en-US')
    expect(langMap['en-US']).to.equal('english')
  })

  it('returns a random language when the system default lang is not found in the lang map array', () => {
    const langMap = getLangMap(
      [{ Lang: 'es', Display: 'spanish' }, { Lang: 'fr', Display: 'french' }],
      ['gm', 'anylanguage']
    )
    expect(langMap).to.exist
    const keys = Object.keys(langMap)
    expect(keys.length).to.equal(1)
    expect(keys).not.to.contain('en')
    expect(keys).not.to.contain('en-US')
    expect(['spanish', 'french']).to.contain(langMap[keys[0]])
  })

  it('returns the requested language', () => {
    const langMap = getLangMap(
      [{ Lang: 'es', Display: 'spanish' }, { Lang: 'fr', Display: 'french' }, { Lang: 'en', Display: 'english short' }, { Lang: 'en-US', Display: 'english long' }],
      ['gm', 'xz', 'en', 'en-US', 'anylanguage']
    )

    expect(langMap).to.exist
    const keys = Object.keys(langMap)
    expect(keys.length).to.equal(1)
    expect(keys).to.contain('en')
    expect(langMap.en).to.equal('english short')
  })

  it('returns a dialect of the given generic language', () => {
    const langMap2 = getLangMap(
      [{ Lang: 'es', Display: 'spanish' }, { Lang: 'fr', Display: 'french' }, { Lang: 'en-US', Display: 'english long' }],
      ['gm', 'xz', 'en']
    )

    expect(langMap2).to.exist
    const keys2 = Object.keys(langMap2)
    expect(keys2.length).to.equal(1)
    expect(keys2).to.contain('en-US')
    expect(langMap2['en-US']).to.equal('english long')
  })

  it('returns the system language when there is no language match and the system language is available in the map', () => {
    const langMap = getLangMap(
      [{ Lang: 'en-US', Display: 'english' }, { Lang: 'fr', Display: 'french' }, { Lang: 'es', Display: 'spanish' }],
      ['gm']
    )

    expect(langMap).to.exist
    expect(Object.keys(langMap)).to.contain('en-US')
    expect(langMap['en-US']).to.equal('english')
  })

  it('returns the first available language when the system language is not available and there is no language match', () => {
    const langMap = getLangMap(
      [{ Lang: 'gm', Display: 'german' }, { Lang: 'fr', Display: 'french' }, { Lang: 'es', Display: 'spanish' }],
      ['en-CA']
    )

    expect(langMap).to.exist
    expect(Object.keys(langMap)).to.contain('gm')
    expect(langMap.gm).to.equal('german')
  })
})
