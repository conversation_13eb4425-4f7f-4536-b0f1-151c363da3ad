import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import moment from 'moment'
import getTerminatedStatementsForActivity from '../../../../services/mssql/statements/get-terminated-for-activity.service.js'
import formatDuration from '../../../../utils/format-duration.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Activity-Average-Duration', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    if (!req.query || !req.query.activity) {
      log('warn', 'Failed to get average duration for activity, no activity id provided', { req })
      res.status(httpStatus.BAD_REQUEST).send('No id provided')
      return
    }
    const statements = await getTerminatedStatementsForActivity(req.query.activity.toString())
    let runningDuration = 0
    let activities = 0
    statements.forEach(statement => {
      if (statement.fields.result && statement.fields.result.duration && moment.duration(statement.fields.result.duration).isValid()) {
        runningDuration += moment.duration(statement.fields.result.duration).asMilliseconds()
        activities++
      }
    })
    let duration = 'Unknown'
    if (runningDuration > 0 && activities > 0) {
      const averageDuration = moment.duration(runningDuration / activities)
      duration = formatDuration(averageDuration)
    }

    log('info', 'Calculated average duration for activity', { duration, req, activity: req.query.activity.toString(), success: true })
    res.json(duration)
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      // this activity has no termination statements
      log('warn', 'Failed to get termination statements for the activity', { req, activity: req.query.activity?.toString(), success: false })
      res.json('Unknown')
    } else {
      log('error', 'Failed to calculate average duration for activity', { errorMessage: getErrorMessage(error), req, activity: req.query.activity?.toString(), success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
