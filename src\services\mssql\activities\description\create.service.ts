import mssql, { addRow } from '@lcs/mssql-utility'
import ActivityDescriptionModel from '../../../../models/activity-descriptions.model.js'
import { ActivityDescription } from '@tess-f/sql-tables/dist/lrs/activity-description.js'

export async function createActivityDefinitionDescription(description: ActivityDescriptionModel): Promise<ActivityDescriptionModel> {
  const pool = mssql.getPool()
  const record = await addRow<ActivityDescription>(pool.request(), description)
  return new ActivityDescriptionModel(record)
}
