import logger from '@lcs/logger'
import { Agent } from '@tess-f/sql-tables/dist/lrs/agent.js'
import { Request, Response } from 'express'
import getAgentService from '../../../services/mssql/agent/get.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus
import AgentModel from '../../../models/agents.model.js'

const log = logger.create('HTTP-Controller:get-agents', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // if we got this far we know there is an agent in the query
  const agent = JSON.parse(req.query.agent!.toString())
  const search: Partial<Agent> = {}
  if (agent.mbox) {
    search.Mbox = agent.mbox
  }
  if (agent.mbox_sha1sum) {
    search.MboxSHA1Sum = agent.mbox_sha1sum
  }
  if (agent.openid) {
    search.OpenID = agent.openid
  }
  if (agent.account?.homePage && agent.account?.name) {
    search.AccountHomePage = agent.homePage
    search.AccountName = agent.name
  }

  try {
    const dbAgent = await getAgentService(search)
    const person = dbAgent.toPerson()
    res.setHeader('Content-Length', JSON.stringify(person).length)
    res.json(person)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      // Return a Person object containing the information about the Agent it received in the request.
      const returnAgent = new AgentModel(agent).toPerson()
      res.json(returnAgent)
    } else {
      log('error', 'Failed to get agents', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}