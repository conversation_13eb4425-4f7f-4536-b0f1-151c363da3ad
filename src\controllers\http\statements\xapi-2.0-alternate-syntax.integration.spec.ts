import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import xApiVersionHeaderMiddleware from '../middleware/xapi-version-header.middleware.js'

describe('xAPI 2.0 Alternate Request Syntax Integration Test', () => {
  before(() => logger.init({ level: 'silly' }))

  it('should reject alternate request syntax for xAPI 2.0 at the middleware level', () => {
    // This test simulates the exact scenario from the original failing test:
    // "An LRS will reject an alternate request syntax sending content which does not have a form parameter with the name of 'content'"
    // The test expects a 4xx error when using alternate request syntax with xAPI 2.0
    
    const mocks = httpMocks.createMocks({
      method: 'PUT',
      headers: {
        'content-type': 'application/x-www-form-urlencoded',
        'X-Experience-API-Version': '2.0.0'
      },
      body: {
        statementId: 'a06a79fb-2521-4087-a561-df48c697c7e4',
        content: JSON.stringify({
          actor: {
            objectType: 'Agent',
            name: 'xAPI mbox',
            mbox: 'mailto:<EMAIL>'
          },
          verb: {
            id: 'http://adlnet.gov/expapi/verbs/attended',
            display: {
              'en-GB': 'attended',
              'en-US': 'attended'
            }
          },
          object: {
            objectType: 'Activity',
            id: 'http://www.example.com/meetings/occurances/34534'
          },
          id: 'a06a79fb-2521-4087-a561-df48c697c7e4'
        })
      }
    })

    let nextCalled = false
    const next = () => { nextCalled = true }

    xApiVersionHeaderMiddleware(mocks.req, mocks.res, next)

    // The middleware should reject the request and not call next()
    expect(nextCalled).to.be.false
    expect(mocks.res.statusCode).to.equal(BAD_REQUEST)
    expect(mocks.res._getData()).to.equal('Alternate request syntax is not supported in xAPI 2.0')
  })

  it('should allow alternate request syntax for xAPI 1.0.3', () => {
    const mocks = httpMocks.createMocks({
      method: 'PUT',
      headers: {
        'content-type': 'application/x-www-form-urlencoded',
        'X-Experience-API-Version': '1.0.3'
      },
      body: {
        'X-Experience-API-Version': '1.0.3',
        statementId: 'a06a79fb-2521-4087-a561-df48c697c7e4',
        content: JSON.stringify({
          actor: {
            objectType: 'Agent',
            name: 'xAPI mbox',
            mbox: 'mailto:<EMAIL>'
          },
          verb: {
            id: 'http://adlnet.gov/expapi/verbs/attended',
            display: {
              'en-GB': 'attended',
              'en-US': 'attended'
            }
          },
          object: {
            objectType: 'Activity',
            id: 'http://www.example.com/meetings/occurances/34534'
          },
          id: 'a06a79fb-2521-4087-a561-df48c697c7e4'
        })
      }
    })

    let nextCalled = false
    const next = () => { nextCalled = true }

    xApiVersionHeaderMiddleware(mocks.req, mocks.res, next)

    // The middleware should allow the request and call next()
    expect(nextCalled).to.be.true
    expect(mocks.res.statusCode).to.equal(200) // Default OK status
  })

  it('should allow regular JSON requests for xAPI 2.0', () => {
    const mocks = httpMocks.createMocks({
      method: 'PUT',
      headers: {
        'content-type': 'application/json',
        'X-Experience-API-Version': '2.0.0'
      },
      body: {
        actor: {
          objectType: 'Agent',
          name: 'xAPI mbox',
          mbox: 'mailto:<EMAIL>'
        },
        verb: {
          id: 'http://adlnet.gov/expapi/verbs/attended',
          display: {
            'en-GB': 'attended',
            'en-US': 'attended'
          }
        },
        object: {
          objectType: 'Activity',
          id: 'http://www.example.com/meetings/occurances/34534'
        },
        id: 'a06a79fb-2521-4087-a561-df48c697c7e4'
      }
    })

    let nextCalled = false
    const next = () => { nextCalled = true }

    xApiVersionHeaderMiddleware(mocks.req, mocks.res, next)

    // The middleware should allow the request and call next()
    expect(nextCalled).to.be.true
    expect(mocks.res.statusCode).to.equal(200) // Default OK status
  })
})
