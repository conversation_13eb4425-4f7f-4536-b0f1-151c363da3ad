import mssql, { getRows, DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivityProfileModel from '../../../models/activity-profile.model.js'
import { ActivityProfile, ActivityProfileTableName, ActivityProfileFields } from '@tess-f/sql-tables/dist/lrs/activity-profile.js'
import { map } from 'mssql'

export async function getActivityProfileService(id: string, activityId: string): Promise<ActivityProfileModel> {
  return new ActivityProfileModel((await getRows<ActivityProfile>(ActivityProfileTableName, mssql.getPool().request(), {
    ID: id,
    ActivityID: activityId
  }))[0])
}

export async function getActivitiesProfileService(activityId: string, since?: string): Promise<ActivityProfileModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('activityId', activityId)
  let query = `
    SELECT *
    FROM [${ActivityProfileTableName}]
    WHERE [${ActivityProfileFields.ActivityID}] = @activityId 
  `

  // Since
  if (since) {
    request.input('since', since)
    query += `AND [${ActivityProfileFields.ModifiedOn}] >= @since `
  }

  const results = await request.query<ActivityProfile>(query)

  const activitiesProfile = await Promise.all(results.recordset.map(async record => {
    const activityProfile = new ActivityProfileModel(record)
    return activityProfile
  }))

  return activitiesProfile

}