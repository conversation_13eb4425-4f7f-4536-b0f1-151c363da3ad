import logger from '@lcs/logger'
import { Request, Response, NextFunction } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import { getErrorMessage } from '@tess-f/backend-utils'
import { validateIri } from '../../../../utils/validate-data-type.js'

const log = logger.create('HTTP-Middleware:get-activities-validator')

export default function (req: Request, res: Response, next: NextFunction) {
  const unallowedParams = Object.keys(req.query).filter(key => key !== 'activityId')
  if (unallowedParams.length > 0) {
    log('warn', 'Failed to get activities: unallowed query params', { unallowedParams, success: false })
    res.status(BAD_REQUEST).send(`The get activity request contained unexpected parameters: ${unallowedParams.join(', ')}`)
    return
  }

  if (!req.query.activityId) {
    log('warn', 'Failed to get activities: no activity in query params', { success: false })
    res.status(BAD_REQUEST).send(`Error -- activities - method ${req.method}, but activityId parameter is missing`)
    return
  }

  try {
    validateIri(req.query.activityId.toString(), 'Activity Id param')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'Failed to get activities: malformed activity', { success: false, errorMessage })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  next()
}
