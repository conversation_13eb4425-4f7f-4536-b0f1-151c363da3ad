import { expect } from 'chai'
import Sinon from 'sinon'
import logger from '@lcs/logger'
import save from './save.service.js'
import * as fdsSaveFile from '@tess-f/fds/dist/amqp/create.js'

describe('AMQP Save File Service', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  xit('should return the id of the saved file', async () => {
    const stub = Sinon.stub(fdsSaveFile, 'CreateFile')
    stub.returns(Promise.resolve({ success: true, data: { id: 'test-id' } }))
    const id = await save('dGVzdA==', 'test.txt', 'text/plain')
    expect(id).to.eq('test-id')
  })

  xit('should throw an error when the rpc calls is successful but no id is returned', async () => {
    const stub = Sinon.stub(fdsSaveFile, 'CreateFile')
    stub.returns(Promise.resolve({ success: true }))
    try {
      const id = await save('dGVzdA==', 'test.txt', 'text/plain')
      expect(id).to.not.exist
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('RPC returned success status but no data')
    }
  })

  xit('should throw an error when the rpc calls is unsuccessful', async () => {
    const stub = Sinon.stub(fdsSaveFile, 'CreateFile')
    stub.returns(Promise.resolve({ success: false }))
    try {
      const id = await save('dGVzdA==', 'test.txt', 'text/plain')
      expect(id).to.not.exist
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('Unknown RPC error')
    }
  })

  xit('should throw an error when the rpc calls times out', async () => {
    const stub = Sinon.stub(fdsSaveFile, 'CreateFile')
    stub.returns(Promise.reject(new Error('RPC Timeout')))
    try {
      const id = await save('dGVzdA==', 'test.txt', 'text/plain')
      expect(id).to.not.exist
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('RPC Timeout')
    }
  })
})
