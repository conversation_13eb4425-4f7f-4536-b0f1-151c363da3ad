import { Table } from '@lcs/mssql-utility'
import { Agent, AgentTableName } from '@tess-f/sql-tables/dist/lrs/agent.js'

export interface AgentJson {
  objectType?: string
  name?: string
  member?: AgentJson[]
  mbox?: string
  mbox_sha1sum?: string
  openid?: string
  account?: {
    homePage: string
    name: string
  }
}

export default class AgentModel extends Table<AgentJson, Agent> {
  fields: AgentJson
  ID!: string
  private _members?: AgentModel[]

  constructor(fields?: Agent<PERSON><PERSON>, record?: Agent) {
    super(AgentTableName)
    if (fields) {
      this.fields = fields
    } else {
      this.fields = {}
    }

    if (record) {
      this.importFromDatabase(record)
    }
  }

  importFromDatabase(record: Agent): void {
    this.ID = record.ID!
    this.fields.objectType = record.ObjectType
    if (record.Name) {
      this.fields.name = record.Name
    }
    if (record.Mbox) {
      this.fields.mbox = record.Mbox
    }
    if (record.MboxSHA1Sum) {
      this.fields.mbox_sha1sum = record.MboxSHA1Sum
    }
    if (record.OpenID) {
      this.fields.openid = record.OpenID
    }
    if (record.AccountHomePage && record.AccountName) {
      this.fields.account = {
        homePage: record.AccountHomePage,
        name: record.AccountName
      }
    }
  }

  exportJsonToDatabase(): Agent {
    // ID will be created by DB
    return {
      ObjectType: this.fields.objectType ? this.fields.objectType : 'Agent', // if the object type is not present it is an agent
      Name: this.fields.name,
      Mbox: this.fields.mbox,
      MboxSHA1Sum: this.fields.mbox_sha1sum,
      OpenID: this.fields.openid,
      AccountHomePage: this.fields.account?.homePage,
      AccountName: this.fields.account?.name
    }
  }

  attachMembers(members: AgentModel[]): void {
    this.fields.member = members.map(member => member.fields)
    this._members = members
  }

  public toJson(idsOnly = false): { [key: string]: any } {
    const ret: { [key: string]: any } = {}
    if (this.fields.mbox) {
      ret.mbox = this.fields.mbox
    }
    if (this.fields.mbox_sha1sum) {
      ret.mbox_sha1sum = this.fields.mbox_sha1sum
    }
    if (this.fields.openid) {
      ret.openid = this.fields.openid
    }
    if (this.fields.account && this.fields.account.name && this.fields.account.homePage) {
      ret.account = {
        name: this.fields.account.name,
        homePage: this.fields.account.homePage
      }
    }
    if (this.fields.objectType === 'Group') {
      // show members for groups is idsOnly is false
      // show members' ids for anonymous groups if idsOnly is true
      const keys = Object.keys(ret)
      const intersection = ['mbox', 'mbox_sha1sum', 'openid', 'account'].filter(x => keys.includes(x))
      if (!idsOnly || (intersection.length <= 0)) {
        if (this._members && this._members.length > 0) {
          ret.member = this._members.map(member => member.toJson(idsOnly))
        }
      }
    }

    ret.objectType = this.fields.objectType
    if (this.fields.name && !idsOnly) {
      ret.name = this.fields.name
    }

    return ret
  }

  public toPerson(): { [key: string]: any } {
    // used only for /agent GET endpoint
    const ret: { [key: string]: any } = {}

    ret.objectType = 'Person'
    if (this.fields.name) {
      ret.name = [this.fields.name]
    }
    if (this.fields.mbox) {
      ret.mbox = [this.fields.mbox]
    }
    if (this.fields.mbox_sha1sum) {
      ret.mbox_sha1sum = [this.fields.mbox_sha1sum]
    }
    if (this.fields.openid) {
      ret.openid = [this.fields.openid]
    }
    if (this.fields.account?.name && this.fields.account?.homePage) {
      ret.account = [{
        name: this.fields.account.name,
        homePage: this.fields.account.homePage
      }]
    }

    return ret
  }
}
