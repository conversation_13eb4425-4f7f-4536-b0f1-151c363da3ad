import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import ActivityModel from '../../../models/activity.model.js'
import getActivityDefinitionDescription from './description/get.service.js'
import getActivityDefinitionName from './name/get.service.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { Activity, ActivityTableName } from '@tess-f/sql-tables/dist/lrs/activity.js'

export default async function getCourseActivitiesForAgent(agentID: string, offset = 0, limit = 10): Promise<{ totalRecords: number, activities: ActivityModel[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('agentID', agentID)
  request.input('offset', offset)
  request.input('limit', limit)

  const query = `
    SELECT [${ActivityTableName}].*, TotalRecords = COUNT(*) OVER()
    FROM [${ActivityTableName}]
    WHERE [ID] IN (
      SELECT DISTINCT [ObjectActivityID]
      FROM [${StatementTableName}]
      WHERE [VerbID] = 'https://adlnet.gov/expapi/verbs/initialized'
      AND [ContextRegistration] IS NOT NULL
      AND [ActorID] = @agentID
    )
    ORDER BY [ID]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<Activity & { TotalRecords: number }>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    activities: await Promise.all(results.recordset.map(async record => {
      const activity = new ActivityModel(undefined, record)
      try {
        activity.attachDefinitionDescription(await getActivityDefinitionDescription(activity.fields.id))
      } catch (error) {
        if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
          throw error
        }
      }

      try {
        activity.attachDefinitionName(await getActivityDefinitionName(activity.fields.id))
      } catch (error) {
        if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
          throw error
        }
      }

      return activity
    }))
  }
}
