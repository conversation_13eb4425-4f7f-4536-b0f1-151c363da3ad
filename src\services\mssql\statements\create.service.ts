import mssql, { addRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import Activity, { ActivityJSON } from '../../../models/activity.model.js'
import Agent from '../../../models/agents.model.js'
import StatementModel from '../../../models/statement.model.js'
import { Statement } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import SubStatement, { SubStatementJson } from '../../../models/sub-statement.model.js'
import Verb from '../../../models/verb.model.js'
import getOrCreateActivityService from '../activities/get-or-create.service.js'
import getOrCreateAgent from '../agent/get-or-create.service.js'
import broadcastStatementEventService from '../../amqp/broadcast-statement-event.service.js'
import createRecord from '../generic/create-record.service.js'
import createSubStatement from '../sub-statements/create.service.js'
import getOrCreateVerbService from '../verb/get-or-create.service.js'
import { CategoryContextActivity } from '@tess-f/sql-tables/dist/lrs/category-context-activity.js'
import { GroupingContextActivity } from '@tess-f/sql-tables/dist/lrs/grouping-context-activity.js'
import { OtherContextActivity } from '@tess-f/sql-tables/dist/lrs/other-context-activity.js'
import { ParentContextActivity } from '@tess-f/sql-tables/dist/lrs/parent-context-activity.js'
import { Attachment } from '@tess-f/sql-tables/dist/lrs/attachments.js'
import ContextAgentModel from '../../../models/context-agent.model.js'
import createContextAgent from '../context-agents/create.service.js'
import ContextGroupModel from '../../../models/context-group.model.js'
import createContextGroup from '../context-groups/create.service.js'

export default async function (statement: StatementModel): Promise<StatementModel> {
  const pool = mssql.getPool()

  // 1. save the verb
  statement.fields.verb = (await getOrCreateVerbService(new Verb(statement.fields.verb))).fields

  // 2. save the actor
  statement.attachActor(await getOrCreateAgent(new Agent(statement.fields.actor)))

  // 3. save the object
  if (statement.fields.object!.objectType === 'Agent' || statement.fields.object!.objectType === 'Group') {
    // Object is an Agent
    statement.attachObjectAgent(await getOrCreateAgent(new Agent(statement.fields.object)))
  } else if (statement.fields.object!.objectType === 'SubStatement') {
    // Object is a SubStatement
    statement.attachObjectSubStatement(await createSubStatement(new SubStatement(statement.fields.object as SubStatementJson)))
  } else if (statement.fields.object!.objectType === 'StatementRef') {
    // Object is a StatementRef (we don't need to do anything the statement class will handle it)
  } else {
    // Object is an Activity
    statement.fields.object!.objectType = 'Activity' // set to activity just in case it came without an object type
    statement.attachObjectActivity(await getOrCreateActivityService(new Activity(statement.fields.object as ActivityJSON)))
  }

  // 4. save context instructor / team
  if (statement.fields.context) {
    // instructor
    if (statement.fields.context.instructor) {
      statement.attachContextInstructor(await getOrCreateAgent(new Agent(statement.fields.context.instructor)))
    }
    // team
    if (statement.fields.context.team) {
      statement.attachContextTeam(await getOrCreateAgent(new Agent(statement.fields.context.team)))
    }
  }

  // add authority
  statement.attachAuthority(await getOrCreateAgent(new Agent(statement.fields.authority)))

  // 5. save the statement
  const created = await addRow<Statement>(pool.request(), statement)
  statement.fields.id = created.ID

  // 6. if context has activities save those now
  //   a. save the activities first then save the statement attachments
  if (statement.getContextParentJoins().length > 0) {
    statement.attachContextParentActivities(await Promise.all(statement.fields.context!.contextActivities!.parent!.map(async activity => {
      return getOrCreateActivityService(new Activity(activity))
    })))
    await Promise.all(statement.getContextParentJoins().map(async join => createRecord<ParentContextActivity>(join)))
  }

  if (statement.getContextCategoryJoins().length > 0) {
    statement.attachContextCategoryActivities(await Promise.all(statement.fields.context!.contextActivities!.category!.map(async activity => {
      return getOrCreateActivityService(new Activity(activity))
    })))
    await Promise.all(statement.getContextCategoryJoins().map(async join => createRecord<CategoryContextActivity>(join)))
  }

  if (statement.getContextGroupingJoins().length > 0) {
    statement.attachContextGroupingActivities(await Promise.all(statement.fields.context!.contextActivities!.grouping!.map(async activity => {
      return getOrCreateActivityService(new Activity(activity))
    })))
    await Promise.all(statement.getContextGroupingJoins().map(async join => createRecord<GroupingContextActivity>(join)))
  }

  if (statement.getContextOtherJoins().length > 0) {
    statement.attachContextOtherActivities(await Promise.all(statement.fields.context!.contextActivities!.other!.map(async activity => {
      return getOrCreateActivityService(new Activity(activity))
    })))
    await Promise.all(statement.getContextOtherJoins().map(async join => createRecord<OtherContextActivity>(join)))
  }

  if (statement.getStatementAttachmentMetadataJoins().length > 0) {
    statement.attachAttachmentMetadata(await Promise.all(statement.getStatementAttachmentMetadataJoins().map(async attachment =>
      await createRecord<Attachment>(attachment)
    )))
  }

  // save the context agents if we have them
  if (statement.fields.context?.contextAgents) {
    // then we can save the context agent
    // next we get or create each relevant type and attach it to the context agent
    const contextAgents: ContextAgentModel[] = []
    for (const contextAgent of statement.fields.context.contextAgents) {
      // create a model
      const contextAgentModel = new ContextAgentModel(contextAgent)
      // set the statement id
      contextAgentModel.statementId = statement.fields.id!
      // get or create the agent
      contextAgentModel.attachAgent(await getOrCreateAgent(new Agent(contextAgent.agent)))
      // create the context agent record
      const createdContextAgent = await createContextAgent(contextAgentModel)
      // add created context agent to array of contextAgents
      contextAgents.push(createdContextAgent)
    }
    statement.attachContextAgents(contextAgents)
  }

  if (statement.fields.context?.contextGroups) {
    const contextGroups: ContextGroupModel[] = []
    for (const contextGroup of statement.fields.context.contextGroups) {
      const contextGroupModel = new ContextGroupModel(contextGroup)
      contextGroupModel.statementId = statement.fields.id!
      contextGroupModel.attachGroup(await getOrCreateAgent(new Agent(contextGroup.group)))
      const createdContextGroup = await createContextGroup(contextGroupModel)
      contextGroups.push(createdContextGroup)
    }
    statement.attachContextGroups(contextGroups)
  }
  
  await broadcastStatementEventService(settings.amqp.statementExchange.routes.created, statement.fields)

  return statement
}
