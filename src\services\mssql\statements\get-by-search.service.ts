import mssql from '@lcs/mssql-utility'
import { AgentJson } from '../../../models/agents.model.js'
import StatementModel from '../../../models/statement.model.js'
import { Statement, StatementFields, StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { StatementTableName as SUB_STATEMENT_TABLE_NAME } from '@tess-f/sql-tables/dist/lrs/sub-statement.js'
import buildStatementService from './build.service.js'
import buildAgentStatementFilter from './query-utils/build-agent-statement-filter.js'
import buildActivityStatementFilter from './query-utils/build-activity-statement-filter.js'

export default async function (
  offset = 0,
  limit = 100,
  agent?: AgentJson,
  verb?: string,
  activity?: string,
  registration?: string,
  relatedActivities = false,
  relatedAgents = false,
  since?: Date,
  until?: Date,
  ascending = false,
  attachments = false
): Promise<{ statements: StatementModel[], totalRecords: number }> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('offset', offset)
  request.input('limit', limit)

  let query = `
    SELECT *, TotalRecords = COUNT(*) OVER()
    FROM [${StatementTableName}]
    WHERE [${StatementFields.Voided}] = 0 
  `

  // filter statements by agents
  if (agent) {
    query += `AND (${buildAgentStatementFilter(request, agent, relatedAgents)}) `
  }

  // filter statements by verb
  if (verb) {
    query += `
      AND (
        [VerbID] = @verbID
        OR [ObjectSubStatementID] IN (
          SELECT [ID]
          FROM [${SUB_STATEMENT_TABLE_NAME}]
          WHERE [VerbID] = @verbID
        )
        OR [ObjectStatementRef] IN (
          SELECT [ID]
          FROM [${StatementTableName}]
          WHERE [VerbID] = @verbID
        )
      ) 
    `
    request.input('verbID', verb)
  }

  // filter activity
  if (activity) {
    query += `AND (${buildActivityStatementFilter(request, activity, relatedActivities)}) `
  }

  if (registration) {
    query += 'AND [ContextRegistration] = @registration '
    request.input('registration', registration)
  }

  if (since && until) {
    request.input('since', since)
    request.input('until', until)
    query += 'AND [Stored] BETWEEN @since AND @until '
  } else if (since) {
    request.input('since', since)
    query += 'AND [Stored] >= @since '
  } else if (until) {
    request.input('until', until)
    query += 'AND [Stored] <= @until '
  }

  query += `
    ORDER BY [Stored] ${ascending ? 'ASC' : 'DESC'}
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<Statement & { TotalRecords: number }>(query)

  const statements = await Promise.all(results.recordset.map(async record => {
    const statement = new StatementModel(undefined, record)
    await buildStatementService(statement, attachments)
    return statement
  }))

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    statements
  }
}
