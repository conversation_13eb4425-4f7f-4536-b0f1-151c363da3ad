import { expect } from 'chai'
import Model from './verb.model.js'
import { fail } from 'assert'
import VerbDisplayModel from './verb-display.model.js'

describe('Verb Model', () => {
  it('imports fields', () => {
    const model = new Model({
      id: 'verb-id',
      display: {
        'en-US': 'verb'
      }
    })
    expect(model.fields).to.exist
    expect(model.fields.id).to.equal('verb-id')
    expect(model.fields.display).to.exist
    expect(model.fields.display!['en-US']).to.equal('verb')
  })

  it('imports a database record', () => {
    const model = new Model(undefined, {
      ID: 'verb-2'
    })
    expect(model.fields).to.exist
    expect(model.fields.id).to.equal('verb-2')
    expect(model.fields.display).not.to.exist
  })

  it('throws an error when no data is given', () => {
    try {
      new Model()
      fail('Model should not create')
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('Must provide fields or record to construct class')
    }
  })

  it('attaches a verbs display object', () => {
    const model = new Model({
      id: 'verb-id'
    })
    model.attachDisplay([
      new VerbDisplayModel({ VerbID: 'verb-id', Display: 'verb', Lang: 'en-US' }),
      new VerbDisplayModel({ VerbID: 'verb-id', Display: 'verb', Lang: 'es-MX' })
    ])
    expect(model.fields.display).to.exist
    expect(model.fields.display!['en-US']).to.equal('verb')
    expect(model.fields.display!['es-MX']).to.equal('verb')
  })

  it('get\'s a list verb display models', () => {
    const model = new Model({
      id: 'verb-id',
      display: {
        'en-US': 'verb',
        'es-MX': 'verb'
      }
    })

    const display = model.getDisplay()
    expect(display).to.exist
    expect(display.length).to.equal(2)
    expect(['en-US', 'es-MX']).to.contain(display[0].fields.Lang)
    expect(['en-US', 'es-MX']).to.contain(display[1].fields.Lang)
  })

  it('returns an empty list when the verb has no display', () => {
    const model = new Model({
      id: 'verb-id'
    })
    const display = model.getDisplay()
    expect(display).to.exist
    expect(display.length).to.equal(0)
  })
})
