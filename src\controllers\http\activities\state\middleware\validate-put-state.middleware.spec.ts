import { expect } from 'chai'
import Sinon from 'sinon'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import middleware from './validate-put-state.middleware.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, OK } = httpStatus

describe('Middleware: Validate PUT activity state request', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  it('should return bad request when a query param other than (activityId, agent, stateId, registration) is provided', () => {
    const mock = httpMocks.createMocks({ query: { foo: 'bar', baz: 'baz' } })
    const mock2 = httpMocks.createMocks({ query: { agent: 'test', activityId: 'test', stateId: 'test', registration: 'test', foo: 'bar' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('The put activity state request contained unexpected parameters: foo, baz')
    expect(next.called).to.be.false

    middleware(mock2.req, mock2.res, next)
    expect(mock2.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock2.res._getData()).to.contain('The put activity state request contained unexpected parameters: foo')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activity id is missing', () => {
    const mock = httpMocks.createMocks({ query: { agent: 'test' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Missing activityId parameter in the request')
    expect(next.called).to.be.false
  })

  it('should return bad request when the activity id is malformed', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'test' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should return bad request when the state id is missing', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Missing stateId parameter in the request')
    expect(next.called).to.be.false
  })

  it('should return bad request when the registration param is malformed', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', stateId: '1', registration: '123' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent param is missing', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', stateId: '1' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('Missing agent parameter in the request')
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent param is not a json object', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', stateId: '1', agent: 'test-1' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(mock.res._getData()).to.contain('agent query parameter is not valid')
    expect(next.called).to.be.false
  })

  it('should return bad request when the agent param is not a json object', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', stateId: '1', agent: '{"mbox": "<EMAIL>"}' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should return bad request when the content type is application/json and the body is empty', () => {
    const mock = httpMocks.createMocks({ query: { activityId: 'http:example.com/activities/1', stateId: '1', agent: '{"mbox": "mailto:<EMAIL>"}' }, body: {}, headers: { 'content-type': 'application/json' } })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(BAD_REQUEST)
    expect(next.called).to.be.false
  })

  it('should call next when the request is valid', () => {
    const mock = httpMocks.createMocks({
      query: {
        activityId: 'http:example.com/activities/1',
        stateId: '1',
        agent: '{"mbox": "mailto:<EMAIL>"}'
      },
      headers: { "content-type": 'application/json' },
      body: { foo: 'bar' }
    })
    const next = Sinon.spy()
    middleware(mock.req, mock.res, next)
    expect(mock.res.statusCode).to.equal(OK)
    expect(next.called).to.be.true
  })
})