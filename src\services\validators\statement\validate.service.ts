import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { checkAllowedFields, checkIfDict, checkRequiredFields, validateUUID } from '../../../utils/validate-data-type.js'
import { validateTimestamp } from '../../../utils/validate-time.js'
import getByIdService from '../../mssql/statements/get-by-id.service.js'
import validateAgent from '../agent/validate.service.js'
import validateAttachments from '../attachments/validate.service.js'
import validateAuthority from '../authority/validate.service.js'
import validateContext from '../context/validate.service.js'
import validateObject from '../object/validate.service.js'
import validateResult from '../result/validate.service.js'
import validateVerb from '../verb/validate.service.js'
import { VoidStatementError } from '../../../utils/error.utils.js'
import { error } from 'console'
const STATEMENT_ALLOWED_FIELDS = ['id', 'actor', 'verb', 'object', 'result', 'stored', 'context', 'timestamp', 'authority', 'version', 'attachments']
const STATEMENT_REQUIRED_FIELDS = ['actor', 'verb', 'object']

export default function validate(statement: any): void {
  // Validates a statement
  if (Array.isArray(statement)) {
    // check for duplicate statement ids
    const setIds: string[] = []
    statement.forEach(state => {
      if (state.id) {
        if (setIds.includes(state.id)) {
          throw new Error('Statement batch contains duplicate IDs')
        } else {
          setIds.push(state.id)
        }
      }
    })

    // check each statement is valid
    statement.forEach(state => validate(state))
  } else if (statement instanceof Object && Object.keys(statement).length > 0) {
    validateStatement(statement)
  } else {
    throw new Error(`There are no statements to validate, payload: ${statement?.toString()}`)
  }
}

function validateStatement(statement: any): void {
  // validates a statement
  // ensure statement is a dictionary
  checkIfDict(statement, 'Statement')

  // checks allowed fields
  checkAllowedFields(STATEMENT_ALLOWED_FIELDS, statement, 'Statement')

  // checks required fields
  checkRequiredFields(STATEMENT_REQUIRED_FIELDS, statement, 'Statement')

  // if version included in statement (usually in header instead) make suer is 1.0.0 +
  if (statement.version) {
    if (typeof statement.version !== 'string') {
      throw new Error('Version must be a string')
    } else if (!/^([12])\.0(\.\d+)?$/.test(statement.version)) {
      throw new Error(`${statement.version} is not a supported version`)
    }
  }

  // if id included, make sure it is a valid UUID
  if (statement.id) {
    validateUUID(statement.id, 'Statement id')
  }

  // if timestamp included, make sure a valid date can be parsed from it
  if (statement.timestamp) {
    validateStatementTimestamp(statement.timestamp, 'Timestamp')
  }

  if (statement.stored) {
    validateStatementTimestamp(statement.stored, 'Stored')
  }

  // validate the actor
  validateAgent(statement.actor, 'actor')

  // validate the verb
  validateVerb(statement.verb, statement.object)

  // validate the object
  validateObject(statement.object)

  // if the object is validated and has no objectType, set to Activity
  if (!statement.object.objectType) {
    statement.object.objectType = 'Activity'
  }

  // if result is included, validate it
  if (statement.result) {
    validateResult(statement.result)
  }

  // if context is included, validate it
  if (statement.context) {
    validateContext(statement.context, statement.object)
  }

  // if authority is included, validate it
  if (statement.authority) {
    validateAgent(statement.authority, 'authority')
    if (statement.authority.objectType === 'Group') {
      validateAuthority(statement.authority)
    }
  }

  // if attachments is included, validate it
  if (statement.attachments) {
    validateAttachments(statement.attachments)
  }

  // check for any null values in Statement except inside extensions (Data 2.2.s4.b1.b1, XAPI-00001)
  Object.keys(statement).forEach((key: any) => {
    Object.keys(statement[key]).forEach(subKey => {
      if (subKey !== 'extensions' && statement[key][subKey] === null) {
        throw new Error(`${subKey} property contains a null value.`)
      }
    })
  })
}

export async function validateVoidStatement(voidId: string) {
  // retrieve statement, check if the ver is 'voided' - if not then set the voided flag to true else return error
  // Since you cannot unvoid a statement and should just reissue the
  // statement with under a new ID
  try {
    const statement = await getByIdService(voidId)
    if (statement.voided) {
      throw new VoidStatementError(`Statement with ID: ${voidId} is already voided, cannot unvoid. Please re-issue the statement under a new ID.`)
    }
    if (statement.fields.verb?.id.includes('://adlnet.gov/expapi/verbs/voided')) {
      throw new VoidStatementError(`Statement with ID: ${voidId} is a voiding statement and cannot be voided`)
    }
  } catch (error) {
    // if we don't find the statement we are looking for that is acceptable
    if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }
}

function validateStatementTimestamp(timestamp: string, placement: string) {
  try {
    validateTimestamp(timestamp)
    // reject statements that don't comply with ISO 8601 offsets
    if (timestamp.endsWith('-00') || timestamp.endsWith('-0000') || timestamp.endsWith('-00:00')) {
      throw new Error(`${placement} error - Statement Timestamp Illegal offset (-00, -0000, -00:00) ${timestamp}`)
    }
  } catch (error) {
    throw new Error(`${placement} error - There was an error while parsing the date from ${timestamp} -- ${error}`)
  }
}
