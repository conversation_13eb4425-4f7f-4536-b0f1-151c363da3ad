import { Router } from 'express'
import getActivityController from './get.controller.js'
import validateGetActivityMiddleware from './middleware/get-activities.validator.js'
import activityStateRouter from './state/activity-state-router.js'
import activityProfileRouter from './profile/activity-profile-router.js'

const router = Router()

router.get('/', validateGetActivityMiddleware, getActivityController)

// activity state
router.use('/state', activityStateRouter)
// activity profile
router.use('/profile', activityProfileRouter)

export default router