import { Request, Response, NextFunction } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import { validateIri, validateUUID } from '../../../../../utils/validate-data-type.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import validateAgent from '../../../../../services/validators/agent/validate.service.js'

const log = logger.create('HTTP-Controller.Delete-Activity-State', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction) {
  const validParams = ['activityId', 'agent', 'stateId', 'registration']
  const badParams: Array<string> = []
  Object.keys(req.query).forEach(key => {
    if (!validParams.includes(key)) {
      badParams.push(key)
    }
  })

  if (badParams.length > 0) {
    log('warn', 'Failed to parse request: unexpected parameter', { success: false, badParams, req })
    res.status(BAD_REQUEST).send(`The get activity state request contained unexpected parameters: ${badParams.join(', ')}`)
    return
  }

  if (!req.query.activityId) {
    log('warn', 'Failed to parse request: activityId parameter is missing', { success: false, req })
    res.status(BAD_REQUEST).send(`Error -- activity_state - method = ${req.method}, but activityId parameter is missing.`)
    return
  }

  try {
    validateIri(req.query.activityId.toString(), 'activityId')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'Invalid activityId IRI: ', { success: false, errorMessage, req })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }


  if (req.query.registration) {
    try {
      validateUUID(req.query.registration.toString(), 'registration')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      log('warn', 'Invalid registration uuid', { registrationUuid: req.query.registration.toString(), success: false, errorMessage, req })
      res.status(BAD_REQUEST).send(errorMessage)
      return
    }
  }

  if (!req.query.agent) {
    log('warn', 'Failed to parse request: agent parameter is missing', { success: false, req })
    res.status(BAD_REQUEST).send(`Error -- activity_state - method = ${req.method}, but agent parameter is missing.`)
    return
  }

  let agent: any
  const agentParam = req.query.agent.toString()
  try {
    agent = JSON.parse(agentParam)
  } catch (error) {
    log('warn', 'Failed to get agents: malformed agent query param, failed to parse as JSON', { success: false, req })
    res.status(BAD_REQUEST).send('agent query param is not valid')
    return
  }

  try {
    validateAgent(agent, 'Agent param')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'Failed to get agent: malformed agent query param, failed to parse as JSON', { success: false, req })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  // Extra validation if oauth, req_validate.py line 522

  // return
  next()
}