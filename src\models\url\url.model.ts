import UrlParams from './url-params.model.js'

export class Url {
  private readonly _url: string

  constructor (path: string, queryParams?: UrlParams) {
    this._url = path

    if (queryParams && queryParams.length > 0) {
      const paramString = queryParams.toString()
      if (paramString.length > 0) {
        this._url += `?${paramString}`
      }
    }
  }

  public toString (): string {
    return this._url
  }
}
