import { expect } from 'chai'
import Model from './agents.model.js'

describe('Agent Model', () => {
  it('can import fields', () => {
    const model = new Model({
      mbox: '<EMAIL>'
    })
    expect(model.fields.mbox).to.equal('<EMAIL>')
  })

  it('can import database records', () => {
    const model = new Model(undefined, {
      ID: 'agent-id',
      Name: 'user',
      ObjectType: 'Agent',
      Mbox: 'mailto:<EMAIL>',
      MboxSHA1Sum: 'sha1sum',
      OpenID: 'id',
      AccountHomePage: 'google.com',
      AccountName: '<EMAIL>'
    })

    expect(model.fields).to.exist
    expect(model.ID).to.equal('agent-id')
    expect(model.fields.account).to.exist
    expect(model.fields.account?.homePage).to.equal('google.com')
    expect(model.fields.account?.name).to.equal('<EMAIL>')
    expect(model.fields.mbox).to.equal('mailto:<EMAIL>')
    expect(model.fields.mbox_sha1sum).to.equal('sha1sum')
    expect(model.fields.member).not.to.exist
    expect(model.fields.name).to.equal('user')
    expect(model.fields.objectType).to.equal('Agent')
    expect(model.fields.openid).to.equal('id')
  })

  it('can convert an agent to a person', () => {
    const model = new Model(undefined, {
      ID: 'agent-id',
      Name: 'user',
      ObjectType: 'Agent',
      Mbox: 'mailto:<EMAIL>',
      MboxSHA1Sum: 'sha1sum',
      OpenID: 'id',
      AccountHomePage: 'google.com',
      AccountName: '<EMAIL>'
    })

    const person = model.toPerson()
    expect(person).to.exist
    expect(person.objectType).to.equal('Person')
    expect(Array.isArray(person.name)).to.be.true
    expect(person.name).to.contain('user')
    expect(Array.isArray(person.mbox)).to.be.true
    expect(person.mbox).to.contain('mailto:<EMAIL>')
    expect(Array.isArray(person.mbox_sha1sum)).to.be.true
    expect(person.mbox_sha1sum).to.contain('sha1sum')
    expect(Array.isArray(person.openid)).to.be.true
    expect(person.openid).to.contain('id')
    expect(Array.isArray(person.account)).to.be.true
    expect(person.account[0].homePage).to.equal('google.com')
    expect(person.account[0].name).to.equal('<EMAIL>')
  })
})