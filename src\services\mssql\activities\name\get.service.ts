import mssql, { getRows } from '@lcs/mssql-utility'
import ActivityNameModel from '../../../../models/activity-names.model.js'
import { ActivityName, ActivityNameTableName } from '@tess-f/sql-tables/dist/lrs/activity-name.js'

export default async function getActivityDefinitionName(activityID: string): Promise<ActivityNameModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<ActivityName>(ActivityNameTableName, pool.request(), { ActivityID: activityID })
  return records.map(record => new ActivityNameModel(record))
}
