import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getStatsForAgent from '../../../../services/mssql/stats/get-for-agent.service.js'
import Verb from '../../../../models/verb.model.js'
import getMostUsedVerbsForAgent from '../../../../services/mssql/verb/get-most-used-for-agent.service.js'
import Activity from '../../../../models/activity.model.js'
import getMostUsedActivitiesForAgent from '../../../../services/mssql/activities/get-most-used-for-agent.service.js'
import getAvgCompletionForAgent from '../../../../services/mssql/stats/get-avg-completion-for-agent.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('HTTP-Controller.Get-Stats-For-Agent', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    // Get Stats (course count, session count)
    let stats: { CourseCount: number, SessionCount: number }
    try {
      stats = await getStatsForAgent(req.params.id)
    } catch (error) {
      if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
        stats = { CourseCount: 0, SessionCount: 0 }
      } else {
        throw error
      }
    }
    // Get most used verbs
    let mostUsedVerbs: Verb[] = []
    try {
      mostUsedVerbs = await getMostUsedVerbsForAgent(req.params.id)
    } catch (error) {
      if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
        mostUsedVerbs = []
      } else {
        throw error
      }
    }
    // Get most used activities
    let mostUsedActivities: Activity[] = []
    try {
      mostUsedActivities = await getMostUsedActivitiesForAgent(req.params.id)
    } catch (error) {
      if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
        mostUsedActivities = []
      } else {
        throw error
      }
    }
    // Calculate successful task completion
    let completion = 0
    try {
      completion = await getAvgCompletionForAgent(req.params.id)
    } catch (error) {
      if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
        completion = 0
      } else {
        throw error
      }
    }

    log('info', 'Successfully retrieved stats for agent', { req, success: true })
    res.json({
      courseCount: stats.CourseCount,
      sessionCount: stats.SessionCount,
      mostUsedActivities: mostUsedActivities.map(activity => activity.fields),
      mostUsedVerbs: mostUsedVerbs.map(verb => verb.fields),
      taskCompletion: completion
    })
  } catch (error) {
    log('error', 'Failed to get stats for agent', { errorMessage: getErrorMessage(error) })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
