import { AGENT_IFIS_CAN_ONLY_BE_ONE } from '../agent/validate.service.js'

export default function validateAuthority (authority: any): void {
  // validates a group representing an authority
  if (authority.member.length !== 2) {
    throw new Error('Groups representing authorities must only contain 2 members')
  }

  const ifis = Object.keys(authority).filter(key => AGENT_IFIS_CAN_ONLY_BE_ONE.includes(key))
  if (ifis.length > 0) {
    throw new Error('Groups representing authorities must not contain an inverse functional identifier')
  }
}
