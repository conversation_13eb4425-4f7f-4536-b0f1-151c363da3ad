import mssql, { deleteRow } from '@lcs/mssql-utility'
import { AgentProfile, ActivityStateTableName } from '@tess-f/sql-tables/dist/lrs/agent-profiles.js'

export default async function deleteAgentProfile(agentId: string, profileID?: string): Promise<number> {
  const identityFields: Partial<AgentProfile> = {
    AgentID: agentId
  }

  if (profileID) {
    identityFields.ProfileID = profileID
  }
  return deleteRow<AgentProfile>(mssql.getPool().request(), ActivityStateTableName, identityFields)
}