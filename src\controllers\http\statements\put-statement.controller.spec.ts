import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST, NO_CONTENT } = httpStatus
import putStatementController from './put-statement.controller.js'
import * as validateService from '../../../services/validators/statement/validate.service.js'
import * as createStatementService from '../../../services/mssql/statements/create.service.js'
import * as getStatementByIdService from '../../../services/mssql/statements/get-by-id.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import StatementModel from '../../../models/statement.model.js'

describe('HTTP Controller: PUT Statement', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  const validStatement = {
    actor: {
      mbox: 'mailto:<EMAIL>'
    },
    verb: {
      id: 'http://example.com/verbs/experienced'
    },
    object: {
      id: 'http://example.com/activities/test'
    }
  }

  describe('Alternate Request Syntax Validation', () => {
    it('should reject alternate request syntax for xAPI 2.0', async () => {
      const mocks = httpMocks.createMocks({
        method: 'PUT',
        query: { statementId: 'test-statement-id' },
        headers: {
          'content-type': 'application/x-www-form-urlencoded',
          'X-Experience-API-Version': '2.0.0'
        },
        body: {
          statementId: 'test-statement-id',
          content: JSON.stringify(validStatement)
        }
      })

      await putStatementController(mocks.req, mocks.res)
      
      expect(mocks.res.statusCode).to.equal(BAD_REQUEST)
      expect(mocks.res._getData()).to.contain('Alternate request syntax is not supported in xAPI 2.0')
    })

    it('should allow alternate request syntax for xAPI 1.0.3', async () => {
      const mocks = httpMocks.createMocks({
        method: 'PUT',
        query: { statementId: 'test-statement-id' },
        headers: {
          'content-type': 'application/x-www-form-urlencoded',
          'X-Experience-API-Version': '1.0.3'
        },
        body: {
          statementId: 'test-statement-id',
          content: JSON.stringify(validStatement)
        },
        authority: {
          mbox: 'mailto:<EMAIL>'
        }
      })

      // Mock the validation service
      const validateStub = Sinon.stub(validateService, 'default')
      validateStub.returns(undefined)

      // Mock the get statement service to simulate no existing statement
      const getStatementStub = Sinon.stub(getStatementByIdService, 'default')
      getStatementStub.rejects(new Error(dbErrors.default.NOT_FOUND_IN_DB))

      // Mock the create statement service
      const createStatementStub = Sinon.stub(createStatementService, 'default')
      createStatementStub.returns(Promise.resolve(new StatementModel(validStatement)))

      await putStatementController(mocks.req, mocks.res)
      
      expect(mocks.res.statusCode).to.equal(NO_CONTENT)
    })

    it('should allow regular JSON requests for xAPI 2.0', async () => {
      const mocks = httpMocks.createMocks({
        method: 'PUT',
        query: { statementId: 'test-statement-id' },
        headers: {
          'content-type': 'application/json',
          'X-Experience-API-Version': '2.0.0'
        },
        body: validStatement,
        authority: {
          mbox: 'mailto:<EMAIL>'
        }
      })

      // Mock the validation service
      const validateStub = Sinon.stub(validateService, 'default')
      validateStub.returns(undefined)

      // Mock the get statement service to simulate no existing statement
      const getStatementStub = Sinon.stub(getStatementByIdService, 'default')
      getStatementStub.rejects(new Error(dbErrors.default.NOT_FOUND_IN_DB))

      // Mock the create statement service
      const createStatementStub = Sinon.stub(createStatementService, 'default')
      createStatementStub.returns(Promise.resolve(new StatementModel(validStatement)))

      await putStatementController(mocks.req, mocks.res)
      
      expect(mocks.res.statusCode).to.equal(NO_CONTENT)
    })
  })

  describe('Basic Validation', () => {
    it('should reject array body', async () => {
      const mocks = httpMocks.createMocks({
        method: 'PUT',
        query: { statementId: 'test-statement-id' },
        headers: {
          'content-type': 'application/json',
          'X-Experience-API-Version': '2.0.0'
        },
        body: [validStatement]
      })

      await putStatementController(mocks.req, mocks.res)
      
      expect(mocks.res.statusCode).to.equal(BAD_REQUEST)
      expect(mocks.res._getData()).to.contain('Request body should be a single statement')
    })

    it('should reject extra query parameters', async () => {
      const mocks = httpMocks.createMocks({
        method: 'PUT',
        query: { 
          statementId: 'test-statement-id',
          extraParam: 'not-allowed'
        },
        headers: {
          'content-type': 'application/json',
          'X-Experience-API-Version': '2.0.0'
        },
        body: validStatement
      })

      await putStatementController(mocks.req, mocks.res)
      
      expect(mocks.res.statusCode).to.equal(BAD_REQUEST)
      expect(mocks.res._getData()).to.contain('Extra parameters in query string')
    })
  })
})
