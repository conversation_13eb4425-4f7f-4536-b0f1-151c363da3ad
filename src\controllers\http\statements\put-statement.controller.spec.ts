import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import logger from '@lcs/logger'
import { shouldAllowAlternateRequestSyntax, isUsingAlternateRequestSyntax } from '../../../utils/xapi-version.utils.js'

describe('HTTP Controller: PUT Statement - xAPI Version Utils', () => {
  before(() => logger.init({ level: 'silly' }))

  describe('xAPI Version Utilities', () => {
    it('should detect alternate request syntax', () => {
      const mocks = httpMocks.createMocks({
        headers: {
          'content-type': 'application/x-www-form-urlencoded'
        }
      })

      expect(isUsingAlternateRequestSyntax(mocks.req)).to.be.true
    })

    it('should not detect alternate request syntax for JSON', () => {
      const mocks = httpMocks.createMocks({
        headers: {
          'content-type': 'application/json'
        }
      })

      expect(isUsingAlternateRequestSyntax(mocks.req)).to.be.false
    })

    it('should not allow alternate request syntax for xAPI 2.0', () => {
      const mocks = httpMocks.createMocks({
        headers: {
          'X-Experience-API-Version': '2.0.0'
        }
      })

      expect(shouldAllowAlternateRequestSyntax(mocks.req)).to.be.false
    })

    it('should not allow alternate request syntax for xAPI 2.0.0', () => {
      const mocks = httpMocks.createMocks({
        headers: {
          'X-Experience-API-Version': '2.0.0'
        }
      })

      expect(shouldAllowAlternateRequestSyntax(mocks.req)).to.be.false
    })
  })
})
