import logger from '@lcs/logger'
import { expect } from 'chai'
import update from './update.controller.js'
import * as getOrCreateAgentService from '../../../../services/mssql/agent/get-or-create.service.js'
import * as getOrCreateActivityStateService from '../../../../services/mssql/activity-state/get-or-create.service.js'
import * as saveFileService from '../../../../services/amqp/file/save.service.js'
import * as deleteFileService from '../../../../services/amqp/file/delete.service.js'
import * as updateActivityStateService from '../../../../services/mssql/activity-state/update.service.js'
import * as deleteActivityStateService from '../../../../services/mssql/activity-state/delete.service.js'
import Sinon from 'sinon'
import AgentModel from '../../../../models/agents.model.js'
import ActivityStateModel from '../../../../models/activity-state.model.js'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
const { NO_CONTENT, PRECONDITION_FAILED, INTERNAL_SERVER_ERROR } = httpStatus
import * as eTagUtils from '../../../../utils/etag.utils.js'

xdescribe('HTTP: Update activity state controller', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  beforeEach(() => {
    const agentStub = Sinon.stub(getOrCreateAgentService, 'default')
    agentStub.returns(Promise.resolve(new AgentModel(undefined, { ID: 'test-1' })))
    const updateStub = Sinon.stub(updateActivityStateService, 'default')
    updateStub.returns(Promise.resolve(new ActivityStateModel({})))
  })

  it('should return no content on successful update of json activity state', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox":"mailto:<EMAIL>"}',
        stateId: '1',
        activityId: '1'
      }
    })

    const createStateStub = Sinon.stub(getOrCreateActivityStateService, 'default')
    createStateStub.returns(Promise.resolve({
      activityState: new ActivityStateModel({
        ID: '1',
        AgentID: 'test-1',
        ActivityID: '1'
      }),
      created: false
    }))

    const checkEtagStub = Sinon.stub(eTagUtils, 'checkModificationConditions')
    checkEtagStub.returns(void 0)
    await update(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
  })

  // etag check is not need
  // it('should return PRECONDITION_FAILED when etag state does not match', async () => {
  //   const mocks = httpMocks.createMocks({
  //     query: {
  //       agent: '{"mbox": "mailto:<EMAIL>"}',
  //       stateId: '1',
  //       activityId: '1',
  //       registration: '1'
  //     },
  //     body: {
  //       test: '1',
  //       foo: 'bar'
  //     },
  //     headers: {
  //       'updated': (new Date()).toISOString(),
  //       'if-match': '123456'
  //     }
  //   })

  //   const createStateStub = Sinon.stub(getOrCreateActivityStateService, 'default')
  //   createStateStub.returns(Promise.resolve({
  //     created: false,
  //     activityState: new ActivityStateModel({
  //       ID: '1',
  //       AgentID: 'test-1',
  //       ActivityID: '1',
  //       RegistrationID: '1',
  //       State: '{"bar":"baz", "test":"2"}',
  //       ModifiedOn: new Date()
  //     })
  //   }))

  //   await update(mocks.req, mocks.res)
  //   expect(mocks.res.statusCode).to.equal(PRECONDITION_FAILED)
  // })

  it('should return INTERNAL_SERVER_ERROR when internal service fail', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        stateId: '1',
        activityId: '1',
        registration: '1'
      },
      body: {
        test: '1',
        foo: 'bar'
      },
      headers: {
        'updated': (new Date()).toISOString(),
        'if-match': '123456'
      }
    })

    const createStateStub = Sinon.stub(getOrCreateActivityStateService, 'default')
    createStateStub.returns(Promise.reject(new Error('server error')))

    await update(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })

  // etag check is not need
  // it('should delete the created resources when etag if match header is present and creating state', async () => {
  //   const mocks = httpMocks.createMocks({
  //     query: {
  //       agent: '{"mbox": "mailto:<EMAIL>"}',
  //       stateId: '1',
  //       activityId: '1',
  //       registration: '1'
  //     },
  //     body: {
  //       test: '1',
  //       foo: 'bar'
  //     },
  //     headers: {
  //       'updated': (new Date()).toISOString(),
  //       'if-match': '123456'
  //     }
  //   })

  //   const createStateStub = Sinon.stub(getOrCreateActivityStateService, 'default')
  //   createStateStub.returns(Promise.resolve({
  //     created: true,
  //     activityState: new ActivityStateModel({
  //       ID: '1',
  //       AgentID: 'test-1',
  //       ActivityID: '1',
  //       RegistrationID: '1',
  //       FileID: '123',
  //       ModifiedOn: new Date()
  //     })
  //   }))

  //   const deleteActivitySub = Sinon.stub(deleteActivityStateService, 'default')
  //   deleteActivitySub.returns(Promise.resolve(1))

  //   const deleteFileStub = Sinon.stub(deleteFileService, 'default')
  //   deleteFileStub.returns(Promise.resolve(1))

  //   await update(mocks.req, mocks.res)
  //   expect(mocks.res.statusCode).to.equal(PRECONDITION_FAILED)
  //   expect(deleteActivitySub.called).to.be.true
  //   expect(deleteFileStub.called).to.be.true
  // })
})
