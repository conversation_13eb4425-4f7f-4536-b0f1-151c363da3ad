import { expect } from 'chai'
import validateObject from './validate.service.js'
import { v4 as uuid } from 'uuid'

describe('Validate object', () => {
  it('throws an error when the object is not a dictionary', () => {
    expect(validateObject.bind(validateObject, 'my object'))
      .to.throw('Object is not a properly formatted dictionary')
  })

  describe('Activity Type Object', () => {
    it('throws an error when the object contains unallowed fields', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'Activity',
        id: uuid(),
        definition: 'my activity',
        foo: 'bar'
      })).to.throw('Invalid field(s) found in Activity - foo')
    })

    it('throws an error when the activity does not have an id', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'Activity',
        definition: 'my activity'
      })).to.throw('Id field must be present in an Activity')
    })

    it('throws an error when when the id is not a valid IRI', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'Activity',
        definition: 'my activity',
        id: 'my-test-id'
      })).to.throw('Activity id with value my-test-id was not a valid IRI')
    })

    describe('Definition', () => {
      it('throws an error when the activity definition is not a dictionary', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: 'my activity',
          id: 'http://example.com/activity#1'
        })).to.throw('Activity definition is not a properly formatted dictionary')
      })

      it('throws an error when the definition contains unallowed fields', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: 'test activity',
            foo: 'bar'
          },
          id: 'http://example.com/activity#1'
        })).to.throw('Invalid field(s) found in Activity definition - foo')
      })

      it('throws an error when the definition name is not a dictionary', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: 'test activity'
          },
          id: 'http://example.com/activity#1'
        })).to.throw('Activity definition name is not a properly formatted dictionary')
      })

      it('throws an error when the definition name is not a valid lang map', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'myFancyLang': 'my activity'
            }
          },
          id: 'http://example.com/activity#1'
        })).to.throw('language myFancyLang is not valid in Activity definition name')
      })

      it('throws an error when the definition description is not a dictionary', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            description: 'test activity'
          },
          id: 'http://example.com/activity#1'
        })).to.throw('Activity definition description is not a properly formatted dictionary')
      })

      it('throws an error when the definition description is not a valid lang map', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'myFancyLang': 'my activity'
            }
          },
          id: 'http://example.com/activity#1'
        })).to.throw('language myFancyLang is not valid in Activity definition description')
      })

      it('throws an error when the definition type is not a valid IRI', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 38
          },
          id: 'http://example.com/activity#1'
        })).to.throw('Activity definition type must be a string')
      })

      it('throws an error when the definition moreInfo is not a valid IRI', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: true
          },
          id: 'http://example.com/activity#1'
        })).to.throw('Activity definition moreInfo must be a string type')
      })

      it('throws an error when the definition interactionType is not a string', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            interactionType: 42
          },
          id: 'http://example.com/activity#1'
        })).to.throw('Activity definition interactionType must be a string')
      })

      it('throws an error when the definition interactionType is not a valid type', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            interactionType: 'simulation'
          },
          id: 'http://example.com/activity#1'
        })).to.throw('Activity definition interactionType simulation is not valid')
      })

      it('throws an error when correctResponsePattern is provided without an interactionType', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            correctResponsesPattern: 'simulation'
          },
          id: 'http://example.com/activity#1'
        })).to.throw('interactionType must be given when correctResponsesPattern is used')
      })

      it('throws an error when the correctResponsesPattern is not a list', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            correctResponsesPattern: 'simulation',
            interactionType: 'true-false'
          },
          id: 'http://example.com/activity#1'
        })).to.throw('Activity definition correctResponsesPattern is not a properly formatted array')
      })

      it('throws an error when a correctResponsesPattern is not a string', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            correctResponsesPattern: [{ foo: 'bar' }],
            interactionType: 'true-false'
          },
          id: 'http://example.com/activity#1'
        })).to.throw('Activity definition correctResponsesPattern answers must all be strings')
      })

      it('throws an error when choices is provided and the interactionType is not provided', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            choices: ['1', '2', '3']
          },
          id: 'http://example.com/activity#1'
        })).to.throw('interactionType must be given when using interaction components')
      })

      it('throws an error when scale is provided and the interactionType is not provided', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            scale: ['1', '2', '3']
          },
          id: 'http://example.com/activity#1'
        })).to.throw('interactionType must be given when using interaction components')
      })

      it('throws an error when source is provided and the interactionType is not provided', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            source: ['1', '2', '3']
          },
          id: 'http://example.com/activity#1'
        })).to.throw('interactionType must be given when using interaction components')
      })

      it('throws an error when target is provided and the interactionType is not provided', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            target: ['1', '2', '3'],
            steps: ['1', '2', '3']
          },
          id: 'http://example.com/activity#1'
        })).to.throw('interactionType must be given when using interaction components')
      })

      it('throws an error when steps is provided and the interactionType is not provided', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            steps: ['1', '2', '3']
          },
          id: 'http://example.com/activity#1'
        })).to.throw('interactionType must be given when using interaction components')
      })

      it('throws an error when the definition has an invalid extension', () => {
        expect(validateObject.bind(validateObject, {
          objectType: 'Activity',
          definition: {
            name: {
              'en-US': 'my activity'
            },
            description: {
              'en-US': 'my activity'
            },
            type: 'http://example.com/type#1',
            moreInfo: 'http://example.com/moreInfo',
            extensions: 'my-extension'
          },
          id: 'http://example.com/activity#1'
        })).to.throw('activity definition extensions extensions is not a properly formatted dictionary')
      })

      describe('Interaction Types', () => {

        describe('Choice and Sequencing', () => {
          it('throws an error when given an interaction field other than choice', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'choice',
                choices: [],
                scale: []
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Only interaction component field(s) allowed (choices) - not allowed: scale')
          })

          it('throws an error when choices is not a list', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'sequencing',
                choices: { foo: 'bar' }
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Activity definition choices is not a properly formatted array')
          })

          it('throws an error when a choice is not a dictionary', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'sequencing',
                choices: ['a']
              },
              id: 'http://example.com/activity#1'
            })).to.throw('choices interaction component is not a properly formatted dictionary')
          })

          it('throws an error when a choice has unallowed fields', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'sequencing',
                choices: [{
                  id: 'test',
                  foo: 'bar'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Invalid field(s) found in Activity definition choices - foo')
          })

          it('throws an error when a choice is missing required fields', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'sequencing',
                choices: [{
                  id: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('description is missing in Activity definition choices')
          })

          it('throws an error if the choice id is not a string', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'sequencing',
                choices: [{
                  id: 34,
                  description: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Interaction activity in component choices has an id that is not a string')
          })

          it('throws an error if the choice description is not a dictionary', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'sequencing',
                choices: [{
                  id: '34',
                  description: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('choices interaction component description is not a properly formatted dictionary')
          })

          it('throws an error if the choice description is not a valid lang map', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'sequencing',
                choices: [{
                  id: '34',
                  description: {
                    'myFancyLang': 'description'
                  }
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('language myFancyLang is not valid in choices interaction component description')
          })

          it('throws an error if any choices share an id', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'sequencing',
                choices: [{
                  id: '34',
                  description: {
                    'en-US': 'description'
                  }
                }, {
                  id: '34',
                  description: {
                    'en-US': 'description'
                  }
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Interaction activities shared the same id(s) (34) which is not allowed')
          })
        })

        describe('Likert', () => {
          it('throws an error when given an interaction field other than scale', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'likert',
                choices: [],
                scale: []
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Only interaction component field(s) allowed (scale) - not allowed: choices')
          })

          it('throws an error when scale is not a list', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'likert',
                scale: { foo: 'bar' }
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Activity definition scale is not a properly formatted array')
          })

          it('throws an error when a scale is not a dictionary', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'likert',
                scale: ['a']
              },
              id: 'http://example.com/activity#1'
            })).to.throw('scale interaction component is not a properly formatted dictionary')
          })

          it('throws an error when a scale has unallowed fields', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'likert',
                scale: [{
                  id: 'test',
                  foo: 'bar'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Invalid field(s) found in Activity definition scale - foo')
          })

          it('throws an error when a scale is missing required fields', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'likert',
                scale: [{
                  id: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('description is missing in Activity definition scale')
          })

          it('throws an error if the scale id is not a string', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'likert',
                scale: [{
                  id: 34,
                  description: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Interaction activity in component scale has an id that is not a string')
          })

          it('throws an error if the scale description is not a dictionary', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'likert',
                scale: [{
                  id: '34',
                  description: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('scale interaction component description is not a properly formatted dictionary')
          })

          it('throws an error if the scale description is not a valid lang map', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'likert',
                scale: [{
                  id: '34',
                  description: {
                    'myFancyLang': 'description'
                  }
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('language myFancyLang is not valid in scale interaction component description')
          })

          it('throws an error if any scales share an id', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'likert',
                scale: [{
                  id: '34',
                  description: {
                    'en-US': 'description'
                  }
                }, {
                  id: '34',
                  description: {
                    'en-US': 'description'
                  }
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Interaction activities shared the same id(s) (34) which is not allowed')
          })
        })

        describe('Matching', () => {
          it('throws an error when given an interaction field other than target or source', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                target: [],
                source: [],
                choices: []
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Only interaction component field(s) allowed (target, source) - not allowed: choices')
          })

          it('throws an error when source is not a list', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                source: { foo: 'bar' }
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Activity definition source is not a properly formatted array')
          })

          it('throws an error when a source is not a dictionary', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                source: ['a']
              },
              id: 'http://example.com/activity#1'
            })).to.throw('source interaction component is not a properly formatted dictionary')
          })

          it('throws an error when a source has unallowed fields', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                source: [{
                  id: 'test',
                  foo: 'bar'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Invalid field(s) found in Activity definition source - foo')
          })

          it('throws an error when a source is missing required fields', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                source: [{
                  id: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('description is missing in Activity definition source')
          })

          it('throws an error if the source id is not a string', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                source: [{
                  id: 34,
                  description: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Interaction activity in component source has an id that is not a string')
          })

          it('throws an error if the source description is not a dictionary', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                source: [{
                  id: '34',
                  description: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('source interaction component description is not a properly formatted dictionary')
          })

          it('throws an error if the source description is not a valid lang map', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                source: [{
                  id: '34',
                  description: {
                    'myFancyLang': 'description'
                  }
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('language myFancyLang is not valid in source interaction component description')
          })

          it('throws an error if any source share an id', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                source: [{
                  id: '34',
                  description: {
                    'en-US': 'description'
                  }
                }, {
                  id: '34',
                  description: {
                    'en-US': 'description'
                  }
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Interaction activities shared the same id(s) (34) which is not allowed')
          })

          it('throws an error when target is not a list', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                target: { foo: 'bar' }
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Activity definition target is not a properly formatted array')
          })

          it('throws an error when a target is not a dictionary', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                target: ['a']
              },
              id: 'http://example.com/activity#1'
            })).to.throw('target interaction component is not a properly formatted dictionary')
          })

          it('throws an error when a target has unallowed fields', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                target: [{
                  id: 'test',
                  foo: 'bar'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Invalid field(s) found in Activity definition target - foo')
          })

          it('throws an error when a target is missing required fields', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                target: [{
                  id: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('description is missing in Activity definition target')
          })

          it('throws an error if the target id is not a string', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                target: [{
                  id: 34,
                  description: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Interaction activity in component target has an id that is not a string')
          })

          it('throws an error if the target description is not a dictionary', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                target: [{
                  id: '34',
                  description: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('target interaction component description is not a properly formatted dictionary')
          })

          it('throws an error if the target description is not a valid lang map', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                target: [{
                  id: '34',
                  description: {
                    'myFancyLang': 'description'
                  }
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('language myFancyLang is not valid in target interaction component description')
          })

          it('throws an error if any target share an id', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'matching',
                target: [{
                  id: '34',
                  description: {
                    'en-US': 'description'
                  }
                }, {
                  id: '34',
                  description: {
                    'en-US': 'description'
                  }
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Interaction activities shared the same id(s) (34) which is not allowed')
          })
        })

        describe('performance', () => {
          it('throws an error when given an interaction field other than steps', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'performance',
                choices: [],
                steps: []
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Only interaction component field(s) allowed (steps) - not allowed: choices')
          })

          it('throws an error when steps is not a list', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'performance',
                steps: { foo: 'bar' }
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Activity definition steps is not a properly formatted array')
          })

          it('throws an error when a step is not a dictionary', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'performance',
                steps: ['a']
              },
              id: 'http://example.com/activity#1'
            })).to.throw('steps interaction component is not a properly formatted dictionary')
          })

          it('throws an error when a step has unallowed fields', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'performance',
                steps: [{
                  id: 'test',
                  foo: 'bar'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Invalid field(s) found in Activity definition steps - foo')
          })

          it('throws an error when a step is missing required fields', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'performance',
                steps: [{
                  id: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('description is missing in Activity definition steps')
          })

          it('throws an error if the step id is not a string', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'performance',
                steps: [{
                  id: 34,
                  description: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Interaction activity in component steps has an id that is not a string')
          })

          it('throws an error if the step description is not a dictionary', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'performance',
                steps: [{
                  id: '34',
                  description: 'test'
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('steps interaction component description is not a properly formatted dictionary')
          })

          it('throws an error if the step description is not a valid lang map', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'performance',
                steps: [{
                  id: '34',
                  description: {
                    'myFancyLang': 'description'
                  }
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('language myFancyLang is not valid in steps interaction component description')
          })

          it('throws an error if any steps share an id', () => {
            expect(validateObject.bind(validateObject, {
              objectType: 'Activity',
              definition: {
                name: {
                  'en-US': 'my activity'
                },
                description: {
                  'en-US': 'my activity'
                },
                type: 'http://example.com/type#1',
                moreInfo: 'http://example.com/moreInfo',
                interactionType: 'performance',
                steps: [{
                  id: '34',
                  description: {
                    'en-US': 'description'
                  }
                }, {
                  id: '34',
                  description: {
                    'en-US': 'description'
                  }
                }]
              },
              id: 'http://example.com/activity#1'
            })).to.throw('Interaction activities shared the same id(s) (34) which is not allowed')
          })
        })
      })
    })

    it('returns void when valid', () => {
      expect(validateObject({
        definition: {
          name: {
            'en-US': 'my activity'
          },
          description: {
            'en-US': 'my activity'
          },
          type: 'http://example.com/type#1',
          moreInfo: 'http://example.com/moreInfo',
          interactionType: 'sequencing',
          choices: [{
            id: '34',
            description: {
              'en-US': 'description'
            }
          }]
        },
        id: 'http://example.com/activity#1'
      })).to.equal(void 0)
    })
  })

  describe('Agent Type Object', () => {
    it('throws an error when the object is not a valid agent', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'Agent',
        mbox: '<EMAIL>'
      })).to.throw('<NAME_EMAIL> did not start with mailto:')
    })

    it('returns void when valid', () => {
      expect(validateObject({
        objectType: 'Group',
        mbox: 'mailto:<EMAIL>'
      })).to.equal(void 0)
    })
  })

  describe('SubStatement Object', () => {
    it('throws an error when the sub statement has unallowed fields', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        foo: 'bar'
      })).to.throw('Invalid field(s) found in SubStatement - foo')
    })

    it('throws an error when a sub statement is missing required fields', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        actor: 'Bob',
        verb: 'placed'
      })).to.throw('object is missing in SubStatement')
    })

    it('throws an error when the sub statement timestamp is not valid', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        actor: { mbox: 'mailto:<EMAIL>' },
        verb: { id: 'https://example.com/verbs/1' },
        object: { id: 'https://example.com/activity/1' },
        timestamp: '08-07-2032T03'
      })).to.throw('Timestamp error - There was an error while parsing the date from 08-07-2032T03 -- Error: Invalid date time stamp: 08-07-2032T03')

      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        actor: { mbox: 'mailto:<EMAIL>' },
        verb: { id: 'https://example.com/verbs/1' },
        object: { id: 'https://example.com/activity/1' },
        timestamp: '2032-08-07T03:08:00.000-00'
      })).to.throw('Timestamp error - Statement Timestamp Illegal offset (-00, -0000, or -00:00) 2032-08-07T03:08:00.000-00')

      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        actor: { mbox: 'mailto:<EMAIL>' },
        verb: { id: 'https://example.com/verbs/1' },
        object: { id: 'https://example.com/activity/1' },
        timestamp: '2032-08-07T03:08:00.000-0000'
      })).to.throw('Timestamp error - Statement Timestamp Illegal offset (-00, -0000, or -00:00) 2032-08-07T03:08:00.000-0000')
    })

    it('throws an error when the sub statement contains another sub statement', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        actor: 'Bob Smith',
        verb: 'viewed',
        object: {
          objectType: 'SubStatement'
        }
      })).to.throw('Cannot nest a SubStatement inside of another SubStatement')
    })

    it('throws an error when the sub statement agent is invalid', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        actor: 'Bob Smith',
        verb: 'viewed',
        object: {
          objectType: 'Activity'
        }
      })).to.throw('Agent in actor is not a properly formatted dictionary')
    })

    it('throws an error when the sub statement object is invalid', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        actor: {
          mbox: 'mailto:<EMAIL>'
        },
        verb: 'viewed',
        object: {
          objectType: 'Activity',
          name: 'Training 101'
        }
      })).to.throw('Invalid field(s) found in Activity - name')
    })

    it('throws an error when the sub statement verb is invalid', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        actor: {
          mbox: 'mailto:<EMAIL>'
        },
        verb: 'viewed',
        object: {
          objectType: 'StatementRef',
          id: uuid()
        }
      })).to.throw('Verb is not a properly formatted dictionary')
    })

    it('throws an error when the sub statement result is invalid', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        actor: {
          mbox: 'mailto:<EMAIL>'
        },
        verb: {
          id: 'http://exmaple.com/verb/1'
        },
        object: {
          objectType: 'StatementRef',
          id: uuid()
        },
        result: 'Passed'
      })).to.throw('Result is not a properly formatted dictionary')
    })

    it('throws an error when the sub statement context is invalid', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'SubStatement',
        actor: {
          mbox: 'mailto:<EMAIL>'
        },
        verb: {
          id: 'http://exmaple.com/verb/1'
        },
        object: {
          objectType: 'StatementRef',
          id: uuid()
        },
        context: 'observer'
      })).to.throw('Context is not a properly formatted dictionary')
    })

    it('returns void when the sub statement is valid', () => {
      expect(validateObject({
        objectType: 'SubStatement',
        actor: {
          mbox: 'mailto:<EMAIL>'
        },
        verb: {
          id: 'http://exmaple.com/verb/1'
        },
        object: {
          objectType: 'StatementRef',
          id: uuid()
        }
      })).to.equal(void 0)
    })
  })

  describe('StatementRef Object', () => {
    it('throws an error when the statement ref contains unallowed fields', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'StatementRef',
        foo: 'bar'
      })).to.throw('Invalid field(s) found in StatementRef - foo')
    })

    it('throws an error when the statement ref is missing required fields', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'StatementRef'
      })).to.throw('id is missing in StatementRef')
    })

    it('throws an error when the statement ref id is not a valid uuid', () => {
      expect(validateObject.bind(validateObject, {
        objectType: 'StatementRef',
        id: 'test-id'
      })).to.throw('StatementRef id is not a valid uuid')
    })

    it('returns void when valid', () => {
      expect(validateObject({
        objectType: 'StatementRef',
        id: uuid()
      })).to.equal(void 0)
    })
  })

  it('throws an error when the object type if invalid', () => {
    expect(validateObject.bind(validateObject, {
      objectType: 'Interaction'
    })).to.throw('The objectType in the statement\'s object is not valid - Interaction')
  })
})
