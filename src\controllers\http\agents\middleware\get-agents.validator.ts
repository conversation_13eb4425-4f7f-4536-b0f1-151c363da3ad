import logger from '@lcs/logger'
import { Request, Response, NextFunction } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST } = httpStatus
import validateAgent from '../../../../services/validators/agent/validate.service.js'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('HTTP-Middleware:get-agents-validator', httpLogTransformer)

export default function (req: Request, res: Response, next: NextFunction) {
  const unallowedParams = Object.keys(req.query).filter(key => key !== 'agent')
  if (unallowedParams.length > 0) {
    log('warn', 'Failed to get agents: unallowed query params', { unallowedParams, success: false, req })
    res.status(BAD_REQUEST).send(`The get agent request contained unexpected parameters: ${unallowedParams.join(', ')}`)
    return
  }

  if (!req.query.agent) {
    log('warn', 'Failed to get agents: no agent in query params', { success: false, req })
    res.status(BAD_REQUEST).send('Error -- agents url, but no agent parameter.. the agent parameter is required')
    return
  }

  let agent: any
  try {
    agent = JSON.parse(req.query.agent.toString())
  } catch (error) {
    log('warn', 'Failed to get agents: malformed agent query param, failed to parse as JSON', { success: false, req })
    res.status(BAD_REQUEST).send(`agent query param is not valid`)
    return
  }

  try {
    validateAgent(agent, 'Agent param')
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('warn', 'Failed to get agents: malformed agent', { success: false, errorMessage, req })
    res.status(BAD_REQUEST).send(errorMessage)
    return
  }

  next()
}
