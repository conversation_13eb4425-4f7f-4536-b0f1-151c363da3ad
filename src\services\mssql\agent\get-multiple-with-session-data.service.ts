import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import AgentSession, { AgentSessionDatabaseRecord } from '../../../models/dashboard/agent-session.model.js'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'
import { AgentTableName } from '@tess-f/sql-tables/dist/lrs/agent.js'

export default async function getPaginatedAgentsWithSessionData(offset = 0, limit = 100, search?: string): Promise<{ totalRecords: number, agents: AgentSession[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  let query = `
    WITH courseCounts AS (
      SELECT COUNT(DISTINCT [ObjectActivityID]) AS CourseCount, [ActorID]
      FROM [${StatementTableName}]
      WHERE [VerbID] = 'https://adlnet.gov/expapi/verbs/initialized'
      AND [ContextRegistration] IS NOT NULL
      GROUP BY [ActorID]
    )

    SELECT [${AgentTableName}].*, 
           TotalRecords = COUNT(*) OVER(),
           COUNT(DISTINCT [${StatementTableName}].[ContextRegistration]) AS [SessionCount],
           COALESCE([courseCounts].[CourseCount], 0) AS [CourseCount]
    FROM [${AgentTableName}]
      JOIN [${StatementTableName}] ON [${StatementTableName}].[ActorID] = [${AgentTableName}].[ID]
      LEFT JOIN [courseCounts] ON [courseCounts].[ActorID] = [${AgentTableName}].[ID]
    WHERE 1 = 1
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, ['Name', 'Mbox', 'MboxSHA1Sum', 'OpenID', 'OAuthIdentifier', 'AccountHomePage', 'AccountName'], 'all')}) `
  }

  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    GROUP BY [${AgentTableName}].[ID], [Name], [Mbox], [MboxSHA1Sum], [OpenID], [OAuthIdentifier], [AccountName], [AccountHomePage], [ObjectType], [CourseCount]
    ORDER BY [Name], [Mbox], [MboxSHA1Sum], [OpenID], [OauthIdentifier], [AccountName], [AccountHomePage]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<AgentSessionDatabaseRecord>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    agents: results.recordset.map(record => new AgentSession(undefined, record))
  }
}
