import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { StatementTableName } from '@tess-f/sql-tables/dist/lrs/Statement.js'

const COMPLETION_VERB = 'https://adlnet.gov/expapi/verbs/completed'

export default async function getCourseActivityCompletionAvg(activityID?: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('verb', COMPLETION_VERB)
  if (activityID) {
    request.input('activityID', activityID)
  }

  const query = `
    SELECT AVG([ResultScoreScaled]) as Completion
    FROM [${StatementTableName}]
    WHERE [VerbID] = @verb
  `

  const result = await request.query<{ Completion: number }>(query)

  if (result.recordset.length === 1 && result.recordset[0].Completion !== null) {
    return result.recordset[0].Completion
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
