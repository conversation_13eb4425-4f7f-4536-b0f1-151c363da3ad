{"typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always", "editor.tabSize": 2, "eslint.alwaysShowStatus": true, "eslint.format.enable": true, "eslint.nodeEnv": "", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "eslint.codeActionsOnSave.rules": null, "sonarlint.connectedMode.project": {"connectionId": "NG Sonarqube", "projectKey": "TESS-LRS-Server"}}