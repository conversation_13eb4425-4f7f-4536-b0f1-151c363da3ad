import logger from '@lcs/logger'
import { expect } from 'chai'
import create from './create.controller.js'
import * as getOrCreateAgentService from '../../../../services/mssql/agent/get-or-create.service.js'
import * as getOrCreateAgentProfileService from '../../../../services/mssql/agent/profile/get-or-create.js'
import * as saveFileService from '../../../../services/amqp/file/save.service.js'
import * as deleteFileService from '../../../../services/amqp/file/delete.service.js'
import * as updateAgentProfileService from '../../../../services/mssql/agent/profile/update.service.js'
import * as deleteAgentProfileService from '../../../../services/mssql/agent/profile/delete.service.js'
import Sinon from 'sinon'
import AgentModel from '../../../../models/agents.model.js'
import AgentProfileModel from '../../../../models/agent-profile.model.js'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
const { NO_CONTENT, PRECONDITION_FAILED, INTERNAL_SERVER_ERROR } = httpStatus

xdescribe('HTTP: Create Agent Profile Controller', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  beforeEach(() => {
    const agentStub = Sinon.stub(getOrCreateAgentService, 'default')
    agentStub.returns(Promise.resolve(new AgentModel(undefined, { ID: 'test-1' })))
    const updateStub = Sinon.stub(updateAgentProfileService, 'default')
    updateStub.returns(Promise.resolve(new AgentProfileModel({ AgentID: 'test-1', FileID: null })))
  })

  it('should return no content after successful creation of new json agent profile', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: '1',
      }
    })

    const createAgentStub = Sinon.stub(getOrCreateAgentProfileService, 'default')
    createAgentStub.returns(Promise.resolve({
      agentProfile: new AgentProfileModel({
        AgentID: 'test-1',
        FileID: null
      }),
      created: true
    }))

    await create(mocks.req, mocks.res)
    console.log(mocks.res.statusCode)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
  })

  it('should merge old and new states together', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: '1',
      },
      body: {
        test: '1',
        foo: 'bar'
      },
      headers: {
        'updated': (new Date()).toISOString()
      }
    })

    const createStateStub = Sinon.stub(getOrCreateAgentProfileService, 'default')
    createStateStub.returns(Promise.resolve({
      created: false,
      agentProfile: new AgentProfileModel({
        ProfileID: '1',
        FileID: null,
        JsonProfile: '{"foo": "bar"}'
      })
    }))

    await create(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NO_CONTENT)
  })

  it('should return PRECONDITION_FAILED when etag state does not match', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: '1'
      },
      body: {
        test: '1',
        foo: 'bar'
      },
      headers: {
        'updated': (new Date()).toISOString(),
        'if-match': '123456'
      }
    })

    const createStateStub = Sinon.stub(getOrCreateAgentProfileService, 'default')
    createStateStub.returns(Promise.resolve({
      created: false,
      agentProfile: new AgentProfileModel({
        ProfileID: '1',
        AgentID: 'test-1',
        Updated: new Date(),
        FileID: null
      })
    }))

    await create(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(PRECONDITION_FAILED)
  })

  it('should return INTERNAL_SERVER_ERROR when internal service fail', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: 'test-1',
      },
      body: {
        test: '1',
        foo: 'bar'
      },
      headers: {
        'updated': (new Date()).toISOString(),
        'if-match': '123456'
      }
    })

    const createStateStub = Sinon.stub(getOrCreateAgentProfileService, 'default')
    createStateStub.returns(Promise.reject(new Error('server error')))

    await create(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })

  it('should delete the created resources when etag if match header is present and creating agent profile', async () => {
    const mocks = httpMocks.createMocks({
      query: {
        agent: '{"mbox": "mailto:<EMAIL>"}',
        profileId: 'test-1'
      },
      body: {
        test: '1',
        foo: 'bar'
      },
      headers: {
        'updated': (new Date()).toISOString(),
        'if-match': '123456'
      }
    })

    const createStateStub = Sinon.stub(getOrCreateAgentProfileService, 'default')
    createStateStub.returns(Promise.resolve({
      created: true,
      agentProfile: new AgentProfileModel({
        AgentID: 'test-1',
        FileID: '123',
        Updated: new Date()
      })
    }))

    const deleteActivitySub = Sinon.stub(deleteAgentProfileService, 'default')
    deleteActivitySub.returns(Promise.resolve(1))

    const deleteFileStub = Sinon.stub(deleteFileService, 'default')
    deleteFileStub.returns(Promise.resolve(1))

    await create(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(PRECONDITION_FAILED)
    expect(deleteActivitySub.called).to.be.true
    expect(deleteFileStub.called).to.be.true
  })
})
