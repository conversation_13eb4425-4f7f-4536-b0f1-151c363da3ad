import { Table } from '@lcs/mssql-utility'
import {
  SubStatementOtherContextActivity,
  SubStatementOtherContextActivityFields,
  SubStatementOtherContextActivityTableName
} from '@tess-f/sql-tables/dist/lrs/sub-statement-other-context-activity.js'

export default class SubStatementOtherContextActivityModel extends Table<SubStatementOtherContextActivity, SubStatementOtherContextActivity> {
  public fields: SubStatementOtherContextActivity

  constructor(fields: SubStatementOtherContextActivity) {
    super(SubStatementOtherContextActivityTableName, [
      SubStatementOtherContextActivityFields.ActivityID,
      SubStatementOtherContextActivityFields.StatementID
    ])
    this.fields = fields
  }

  public importFromDatabase(record: SubStatementOtherContextActivity): void {
    this.fields = record
  }

  public exportJsonToDatabase(): SubStatementOtherContextActivity {
    return this.fields
  }
}
