import logger from '@lcs/logger'
import get from './get.service.js'
import * as getFDSFile from '@tess-f/fds/dist/amqp/get.js'
import { expect } from 'chai'
import Sinon from 'sinon'

describe('AMQP Get File Service', () => {
  before(() => logger.init({ level: 'silly' }))
  afterEach(() => Sinon.restore())

  xit('should return buffer and file name', async () => {
    const stub = Sinon.stub(getFDSFile, 'GetFile')
    stub.returns(Promise.resolve({ success: true, data: { fileBase64: 'dGVzdA==', filename: 'test.txt' } }))
    const result = await get('test')
    expect(result.buffer).to.exist
    expect(result.buffer.length).to.be.gte(1)
    expect(result.filename).to.eq('test.txt')
  })

  xit('should throw an error when the rpc call is successful but has no data', async () => {
    const stub = Sinon.stub(getFDSFile, 'GetFile')
    stub.returns(Promise.resolve({ success: true }))
    try {
      const result = await get('test')
      expect(result).to.not.exist
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('Failed to get file')
    }
  })

  xit('should throw an error when the rpc call is unsuccessful', async () => {
    const stub = Sinon.stub(getFDSFile, 'GetFile')
    stub.returns(Promise.resolve({ success: false, message: 'failure' }))
    try {
      const result = await get('test')
      expect(result).to.not.exist
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('failure')
    }
  })

  xit('should throw an error when the rpc call encounters an error', async () => {
    const stub = Sinon.stub(getFDSFile, 'GetFile')
    stub.returns(Promise.reject(new Error('RPC Timeout')))
    try {
      const result = await get('test')
      expect(result).to.not.exist
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.contain('RPC Timeout')
    }
  })
})
