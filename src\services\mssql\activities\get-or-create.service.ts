import mssql, { DB_Errors as dbErrors, updateRow } from '@lcs/mssql-utility'
import ActivityModel from '../../../models/activity.model.js'
import createActivityService from './create.service.js'
import getActivityService from './get.service.js'
import { Activity } from '@tess-f/sql-tables/dist/lrs/activity.js'

import updateActivityDefinitionName from './name/update.service.js'

export default async function getOrCreateActivityService(activity: ActivityModel, canUpdate = true): Promise<ActivityModel> {
  try {
    const dbActivity = await getActivityService(activity.fields.id)

    if (canUpdate) {
      if (dbActivity.fields.objectType === 'Activity' && !dbActivity.fields.definition && activity.fields.definition) {
        dbActivity.fields.definition = activity.fields.definition
      } else if (dbActivity.fields.objectType === 'Activity' && dbActivity.fields.definition && activity.fields.definition) {
        // update definition scale
        if (activity.fields.definition.scale && dbActivity.fields.definition.scale) {
          dbActivity.fields.definition.scale.forEach(scale => {
            const incomingScale = activity.fields.definition!.scale!.find(s => s.id === scale.id)
            if (incomingScale) {
              // map the def change
              const description = scale.description ?? {}
              for (const key in incomingScale.description) {
                description[key] = incomingScale.description[key]
              }
              scale.description = description
            }
          })
        }
        // update activity definition name
        if (activity.fields.definition.name && dbActivity.fields.definition.name) {
          dbActivity.fields.definition.name = { ...dbActivity.fields.definition.name, ...activity.fields.definition.name }
          await updateActivityDefinitionName(dbActivity.getDefinitionName())
        }
        // TODO: update other mutable properties
      }
      await updateRow<Activity>(mssql.getPool().request(), dbActivity, { ID: dbActivity.fields.id })
    }

    return dbActivity
  } catch (error) {
    if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
      return createActivityService(activity)
    } else {
      throw error
    }
  }
}
