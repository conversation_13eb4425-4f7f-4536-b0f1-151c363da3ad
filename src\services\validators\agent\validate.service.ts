import { checkAllowedFields, checkIfDict, checkIfList, checkRequiredFields, validateIri } from '../../../utils/validate-data-type.js'
import { validateEmail, validateSha1sumEmail } from '../../../utils/validate-email.js'

export const AGENT_IFIS_CAN_ONLY_BE_ONE = ['mbox', 'mbox_sha1sum', 'openid', 'account']
const AGENT_ALLOWED_FIELDS = ['objectType', 'name', 'member', 'mbox', 'mbox_sha1sum', 'openid', 'account']
const ACCOUNT_FIELDS = ['homePage', 'name']

export default function validateAgent (agent: any, placement: string): void {
  // Validates an agent

  // ensure agent is a dictionary
  checkIfDict(agent, `Agent in ${placement}`)

  // check agent only contains allowed fields
  checkAllowedFields(AGENT_ALLOWED_FIELDS, agent, 'Agent/Group')

  validateAgentObjectType(agent, placement)

  // Agent must have only one inverse functional identifier (Group may be Anonymous Group where no IFI is required)
  const ifis = Object.keys(agent).filter(key => AGENT_IFIS_CAN_ONLY_BE_ONE.includes(key))

  if (agent.objectType === 'Agent' && ifis.length !== 1) {
    throw new Error(`One and only one of ${AGENT_IFIS_CAN_ONLY_BE_ONE.join(', ')} may be supplied with an Agent`)
  } else if (agent.objectType === 'Group' && ifis.length > 1) {
    throw new Error(`None or one and only one of ${AGENT_IFIS_CAN_ONLY_BE_ONE.join(', ')} may be supplied with a Group`)
  }

  if (agent.objectType === 'Agent') {
    // If agent, if name given, ensure name is string and validate the IFI
    if (agent.name && typeof agent.name !== 'string') {
      throw new Error(`If name is given in Agent, it must be a string -- got ${typeof agent.name}`)
    }
    validateIFI(ifis[0], agent[ifis[0]])
  } else {
    validateGroup(agent, ifis)
  }
}

function validateAgentObjectType (agent: any, placement: string) {
  // if the agent is then object of a statement, the objectType must be present
  if (placement === 'object' && !agent.objectType) {
    throw new Error('objectType must be set when using an Agent as the object of a statement')
  } else if (placement !== 'object' && agent.objectType && agent.objectType !== 'Group' && agent.objectType !== 'Agent') {
    // If the agent is not the object of a statement and objectType is given,
    // it must be Agent or Group
    throw new Error('An agent\'s objectType must be either Agent or Group if given')
  } else if (placement !== 'object' && !agent.objectType) {
    // If the agent is not the object of a statement and objectType is not given,
    // set it to Agent
    agent.objectType = 'Agent'
  }
}

function validateGroup (agent: any, ifis: string[]) {
  // If group, if name given, ensure name is string
  if (agent.name && typeof agent.name !== 'string') {
    throw new Error(`If name is given in Group, it must be a string`)
  }

  // If no IFIs, it is an anonymous group which must contain the member property
  if (ifis.length === 0) {
    // No ifi means anonymous group - must have member
    if (!agent.member) {
      throw new Error('Anonymous groups must contain member')
    } else {
      validateMembers(agent)
    }
  } else {
    // IFI given, validate it
    validateIFI(ifis[0], agent[ifis[0]])
    if (agent.member) {
      validateMembers(agent)
    }
  }
}

function validateMembers (agent: any): void {
  // validates members of a group
  // Ensure that member is an array
  checkIfList(agent.member, 'Members')
  if (agent.member.length <= 0) {
    throw new Error('Member property must contain agents')
  }

  // Make sure no member of group is another group
  const objectTypes: string[] = (agent.member as Array<any>).map(member => member.objectType).filter(types => types !== null && types !== undefined)
  if (objectTypes.includes('Group')) {
    throw new Error('Group member value cannot be other groups')
  }

  // Validate each member of the group
  (agent.member as Array<any>).forEach(member => {
    validateAgent(member, 'member')
  })
}

function validateIFI (ifi: string, value: any): void {
  // validates an inverse functional identifier
  // Validate each IFI accordingly
  if (ifi === 'mbox') {
    validateEmail(value)
  } else if (ifi === 'mbox_sha1sum') {
    validateSha1sumEmail(value)
  } else if (ifi === 'openid') {
    validateIri(value, 'openid')
  } else if (ifi === 'account') {
    validateAccount(value)
  }
}

function validateAccount (account: any): void {
  // validate an account
  // ensure incoming account is a dictionary
  checkIfDict(account, 'Account')
  // check allowed fields
  checkAllowedFields(ACCOUNT_FIELDS, account, 'Account')
  // check required fields
  checkRequiredFields(ACCOUNT_FIELDS, account, 'Account')

  // Ensure homePage is a valid IRI
  validateIri(account.homePage, 'homePage')

  // Ensure name is a string
  if (typeof account.name !== 'string') {
    throw new Error('account name must be a string')
  }
}
